// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/xian_cwsx/activity/star.proto

package activity

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AwardStatusType int32

const (
	AwardStatusType_AwardStatusType_None    AwardStatusType = 0 // 无奖可领【活动未到公示期、不构成领奖状态】
	AwardStatusType_AwardStatusType_Awarded AwardStatusType = 1 // 已领奖
	AwardStatusType_AwardStatusType_Wait    AwardStatusType = 2 // 有奖可领但未领【活动到公示期且具备领奖条件】
)

// Enum value maps for AwardStatusType.
var (
	AwardStatusType_name = map[int32]string{
		0: "AwardStatusType_None",
		1: "AwardStatusType_Awarded",
		2: "AwardStatusType_Wait",
	}
	AwardStatusType_value = map[string]int32{
		"AwardStatusType_None":    0,
		"AwardStatusType_Awarded": 1,
		"AwardStatusType_Wait":    2,
	}
)

func (x AwardStatusType) Enum() *AwardStatusType {
	p := new(AwardStatusType)
	*p = x
	return p
}

func (x AwardStatusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AwardStatusType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes[0].Descriptor()
}

func (AwardStatusType) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes[0]
}

func (x AwardStatusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AwardStatusType.Descriptor instead.
func (AwardStatusType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{0}
}

type ActivityStarGiftType int32

const (
	ActivityStarGiftType_ActivityStarGiftType_None          ActivityStarGiftType = 0  // 无
	ActivityStarGiftType_ActivityStarGiftType_Postcard      ActivityStarGiftType = -1 // 明信片
	ActivityStarGiftType_ActivityStarGiftType_StarRankScore ActivityStarGiftType = -2 // 排行榜分数
	ActivityStarGiftType_ActivityStarGiftType_Ticket        ActivityStarGiftType = -3 // 门票
)

// Enum value maps for ActivityStarGiftType.
var (
	ActivityStarGiftType_name = map[int32]string{
		0:  "ActivityStarGiftType_None",
		-1: "ActivityStarGiftType_Postcard",
		-2: "ActivityStarGiftType_StarRankScore",
		-3: "ActivityStarGiftType_Ticket",
	}
	ActivityStarGiftType_value = map[string]int32{
		"ActivityStarGiftType_None":          0,
		"ActivityStarGiftType_Postcard":      -1,
		"ActivityStarGiftType_StarRankScore": -2,
		"ActivityStarGiftType_Ticket":        -3,
	}
)

func (x ActivityStarGiftType) Enum() *ActivityStarGiftType {
	p := new(ActivityStarGiftType)
	*p = x
	return p
}

func (x ActivityStarGiftType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityStarGiftType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes[1].Descriptor()
}

func (ActivityStarGiftType) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes[1]
}

func (x ActivityStarGiftType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityStarGiftType.Descriptor instead.
func (ActivityStarGiftType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{1}
}

type ActivityStatusType int32

const (
	ActivityStatusType_ActivityStatusType_None ActivityStatusType = 0 // 无
	ActivityStatusType_ActivityStatusType_Star ActivityStatusType = 1 // 明星活动的状态
)

// Enum value maps for ActivityStatusType.
var (
	ActivityStatusType_name = map[int32]string{
		0: "ActivityStatusType_None",
		1: "ActivityStatusType_Star",
	}
	ActivityStatusType_value = map[string]int32{
		"ActivityStatusType_None": 0,
		"ActivityStatusType_Star": 1,
	}
)

func (x ActivityStatusType) Enum() *ActivityStatusType {
	p := new(ActivityStatusType)
	*p = x
	return p
}

func (x ActivityStatusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityStatusType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes[2].Descriptor()
}

func (ActivityStatusType) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes[2]
}

func (x ActivityStatusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityStatusType.Descriptor instead.
func (ActivityStatusType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{2}
}

type StarStatusType int32

const (
	StarStatusType_StarStatusType_Off          StarStatusType = 0 // 不在展示期
	StarStatusType_StarStatusType_JoinAndShow  StarStatusType = 1 // 玩家[参与了明星活动并且当前正处于活动中不包含公示期]
	StarStatusType_StarStatusType_JustShow     StarStatusType = 2 // 玩家未参与[活动周期内不包含公示期]
	StarStatusType_StarStatusType_ExitActivity StarStatusType = 3 // 玩家退出活动
)

// Enum value maps for StarStatusType.
var (
	StarStatusType_name = map[int32]string{
		0: "StarStatusType_Off",
		1: "StarStatusType_JoinAndShow",
		2: "StarStatusType_JustShow",
		3: "StarStatusType_ExitActivity",
	}
	StarStatusType_value = map[string]int32{
		"StarStatusType_Off":          0,
		"StarStatusType_JoinAndShow":  1,
		"StarStatusType_JustShow":     2,
		"StarStatusType_ExitActivity": 3,
	}
)

func (x StarStatusType) Enum() *StarStatusType {
	p := new(StarStatusType)
	*p = x
	return p
}

func (x StarStatusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StarStatusType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes[3].Descriptor()
}

func (StarStatusType) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes[3]
}

func (x StarStatusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StarStatusType.Descriptor instead.
func (StarStatusType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{3}
}

type StarStatusReq_JoinType int32

const (
	StarStatusReq_JoinType_None StarStatusReq_JoinType = 0 // 普通
	StarStatusReq_JoinType_Join StarStatusReq_JoinType = 1 // 参加
)

// Enum value maps for StarStatusReq_JoinType.
var (
	StarStatusReq_JoinType_name = map[int32]string{
		0: "JoinType_None",
		1: "JoinType_Join",
	}
	StarStatusReq_JoinType_value = map[string]int32{
		"JoinType_None": 0,
		"JoinType_Join": 1,
	}
)

func (x StarStatusReq_JoinType) Enum() *StarStatusReq_JoinType {
	p := new(StarStatusReq_JoinType)
	*p = x
	return p
}

func (x StarStatusReq_JoinType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StarStatusReq_JoinType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes[4].Descriptor()
}

func (StarStatusReq_JoinType) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes[4]
}

func (x StarStatusReq_JoinType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StarStatusReq_JoinType.Descriptor instead.
func (StarStatusReq_JoinType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{13, 0}
}

type StarStatusRsp_StarStatusType int32

const (
	StarStatusRsp_StarStatusType_None   StarStatusRsp_StarStatusType = 0 // 未参与
	StarStatusRsp_StarStatusType_Join   StarStatusRsp_StarStatusType = 1 // 参与中
	StarStatusRsp_StarStatusType_Unable StarStatusRsp_StarStatusType = 2 // 不可参与 【点击参与后:这个值表示公示期结束】/其他情况入口不显示
	StarStatusRsp_StarStatusType_Exit   StarStatusRsp_StarStatusType = 3 // 玩家退出活动
)

// Enum value maps for StarStatusRsp_StarStatusType.
var (
	StarStatusRsp_StarStatusType_name = map[int32]string{
		0: "StarStatusType_None",
		1: "StarStatusType_Join",
		2: "StarStatusType_Unable",
		3: "StarStatusType_Exit",
	}
	StarStatusRsp_StarStatusType_value = map[string]int32{
		"StarStatusType_None":   0,
		"StarStatusType_Join":   1,
		"StarStatusType_Unable": 2,
		"StarStatusType_Exit":   3,
	}
)

func (x StarStatusRsp_StarStatusType) Enum() *StarStatusRsp_StarStatusType {
	p := new(StarStatusRsp_StarStatusType)
	*p = x
	return p
}

func (x StarStatusRsp_StarStatusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StarStatusRsp_StarStatusType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes[5].Descriptor()
}

func (StarStatusRsp_StarStatusType) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes[5]
}

func (x StarStatusRsp_StarStatusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StarStatusRsp_StarStatusType.Descriptor instead.
func (StarStatusRsp_StarStatusType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{14, 0}
}

// StarRankReq 明星活动排行榜 Req
type StarRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Round int64  `protobuf:"varint,1,opt,name=round,proto3" json:"round,omitempty"` // 场次=>对应configPro的star_loop.ID
	Page  uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *StarRankReq) Reset() {
	*x = StarRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarRankReq) ProtoMessage() {}

func (x *StarRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarRankReq.ProtoReflect.Descriptor instead.
func (*StarRankReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{0}
}

func (x *StarRankReq) GetRound() int64 {
	if x != nil {
		return x.Round
	}
	return 0
}

func (x *StarRankReq) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

// StarRankRsp 明星活动排行榜 Rsp
type StarRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List     []*StarRank   `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`                         // 排行榜成员
	SelfInfo *StarRankSelf `protobuf:"bytes,2,opt,name=self_info,json=selfInfo,proto3" json:"self_info,omitempty"` // 个人信息【仅第一页会返回】
}

func (x *StarRankRsp) Reset() {
	*x = StarRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarRankRsp) ProtoMessage() {}

func (x *StarRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarRankRsp.ProtoReflect.Descriptor instead.
func (*StarRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{1}
}

func (x *StarRankRsp) GetList() []*StarRank {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *StarRankRsp) GetSelfInfo() *StarRankSelf {
	if x != nil {
		return x.SelfInfo
	}
	return nil
}

// StarRank 明星成员信息
type StarRank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId   string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"` // openid
	Nickname string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`           // 用户昵称
	Avatar   string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`               // 用户头像
	Score    uint32 `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`                // 贡献苹果数量
	Tt       int64  `protobuf:"varint,5,opt,name=tt,proto3" json:"tt,omitempty"`                      // 贡献时间->加入活动时间
}

func (x *StarRank) Reset() {
	*x = StarRank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarRank) ProtoMessage() {}

func (x *StarRank) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarRank.ProtoReflect.Descriptor instead.
func (*StarRank) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{2}
}

func (x *StarRank) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *StarRank) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *StarRank) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *StarRank) GetScore() uint32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *StarRank) GetTt() int64 {
	if x != nil {
		return x.Tt
	}
	return 0
}

// StarRankSelf 明星成员信息
type StarRankSelf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId   string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"` // openid
	Nickname string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`           // 用户昵称
	Avatar   string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`               // 用户头像
	Score    uint32 `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`                // 贡献苹果数量
	Tt       int64  `protobuf:"varint,5,opt,name=tt,proto3" json:"tt,omitempty"`                      // 贡献时间->加入活动时间
	Ranking  int32  `protobuf:"varint,6,opt,name=ranking,proto3" json:"ranking,omitempty"`            // 排名 0代表未上榜
}

func (x *StarRankSelf) Reset() {
	*x = StarRankSelf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarRankSelf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarRankSelf) ProtoMessage() {}

func (x *StarRankSelf) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarRankSelf.ProtoReflect.Descriptor instead.
func (*StarRankSelf) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{3}
}

func (x *StarRankSelf) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *StarRankSelf) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *StarRankSelf) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *StarRankSelf) GetScore() uint32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *StarRankSelf) GetTt() int64 {
	if x != nil {
		return x.Tt
	}
	return 0
}

func (x *StarRankSelf) GetRanking() int32 {
	if x != nil {
		return x.Ranking
	}
	return 0
}

// StarLotteryReq 明星活动抽奖 Req
type StarLotteryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tid   string `protobuf:"bytes,1,opt,name=tid,proto3" json:"tid,omitempty"`      // 获取的票据
	Round int64  `protobuf:"varint,2,opt,name=round,proto3" json:"round,omitempty"` // 场次=>对应configPro的star_loop.ID
}

func (x *StarLotteryReq) Reset() {
	*x = StarLotteryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarLotteryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarLotteryReq) ProtoMessage() {}

func (x *StarLotteryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarLotteryReq.ProtoReflect.Descriptor instead.
func (*StarLotteryReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{4}
}

func (x *StarLotteryReq) GetTid() string {
	if x != nil {
		return x.Tid
	}
	return ""
}

func (x *StarLotteryReq) GetRound() int64 {
	if x != nil {
		return x.Round
	}
	return 0
}

// StarLotteryRsp 明星活动抽奖 Rsp
type StarLotteryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Site          int32               `protobuf:"varint,1,opt,name=site,proto3" json:"site,omitempty"`                                  // 中奖编号
	TotalVolume   int32               `protobuf:"varint,2,opt,name=total_volume,json=totalVolume,proto3" json:"total_volume,omitempty"` // 剩余奖券数量
	TreasureCards []*TreasureCardStar `protobuf:"bytes,3,rep,name=treasureCards,proto3" json:"treasureCards,omitempty"`                 // 下发道具的宝藏奖励
}

func (x *StarLotteryRsp) Reset() {
	*x = StarLotteryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarLotteryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarLotteryRsp) ProtoMessage() {}

func (x *StarLotteryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarLotteryRsp.ProtoReflect.Descriptor instead.
func (*StarLotteryRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{5}
}

func (x *StarLotteryRsp) GetSite() int32 {
	if x != nil {
		return x.Site
	}
	return 0
}

func (x *StarLotteryRsp) GetTotalVolume() int32 {
	if x != nil {
		return x.TotalVolume
	}
	return 0
}

func (x *StarLotteryRsp) GetTreasureCards() []*TreasureCardStar {
	if x != nil {
		return x.TreasureCards
	}
	return nil
}

// StarShareReq 明星活动抽奖 Req
type StarShareReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Round int64 `protobuf:"varint,1,opt,name=round,proto3" json:"round,omitempty"` // 场次=>对应configPro的star_loop.ID
}

func (x *StarShareReq) Reset() {
	*x = StarShareReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarShareReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarShareReq) ProtoMessage() {}

func (x *StarShareReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarShareReq.ProtoReflect.Descriptor instead.
func (*StarShareReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{6}
}

func (x *StarShareReq) GetRound() int64 {
	if x != nil {
		return x.Round
	}
	return 0
}

// StarShareRsp 明星活动抽奖 Rsp
type StarShareRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StarShareRsp) Reset() {
	*x = StarShareRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarShareRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarShareRsp) ProtoMessage() {}

func (x *StarShareRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarShareRsp.ProtoReflect.Descriptor instead.
func (*StarShareRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{7}
}

// StarLotteryMultipleReq 明星活动抽奖 Req
type StarLotteryMultipleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tid      string `protobuf:"bytes,1,opt,name=tid,proto3" json:"tid,omitempty"`            // 获取的票据
	Round    int64  `protobuf:"varint,2,opt,name=round,proto3" json:"round,omitempty"`       // 场次=>对应configPro的star_loop.ID
	Multiple uint32 `protobuf:"varint,3,opt,name=multiple,proto3" json:"multiple,omitempty"` // 倍数
}

func (x *StarLotteryMultipleReq) Reset() {
	*x = StarLotteryMultipleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarLotteryMultipleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarLotteryMultipleReq) ProtoMessage() {}

func (x *StarLotteryMultipleReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarLotteryMultipleReq.ProtoReflect.Descriptor instead.
func (*StarLotteryMultipleReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{8}
}

func (x *StarLotteryMultipleReq) GetTid() string {
	if x != nil {
		return x.Tid
	}
	return ""
}

func (x *StarLotteryMultipleReq) GetRound() int64 {
	if x != nil {
		return x.Round
	}
	return 0
}

func (x *StarLotteryMultipleReq) GetMultiple() uint32 {
	if x != nil {
		return x.Multiple
	}
	return 0
}

// StarLotteryMultipleRsp 明星活动抽奖 Rsp
type StarLotteryMultipleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalVolume int32           `protobuf:"varint,1,opt,name=total_volume,json=totalVolume,proto3" json:"total_volume,omitempty"`                                                                                 // 剩余奖券数量
	SiteList    map[int32]int32 `protobuf:"bytes,2,rep,name=site_list,json=siteList,proto3" json:"site_list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 中奖结果 格子=>次数
}

func (x *StarLotteryMultipleRsp) Reset() {
	*x = StarLotteryMultipleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarLotteryMultipleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarLotteryMultipleRsp) ProtoMessage() {}

func (x *StarLotteryMultipleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarLotteryMultipleRsp.ProtoReflect.Descriptor instead.
func (*StarLotteryMultipleRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{9}
}

func (x *StarLotteryMultipleRsp) GetTotalVolume() int32 {
	if x != nil {
		return x.TotalVolume
	}
	return 0
}

func (x *StarLotteryMultipleRsp) GetSiteList() map[int32]int32 {
	if x != nil {
		return x.SiteList
	}
	return nil
}

// StarLotteryRecordReq 明星活动抽奖记录 Req
type StarLotteryRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Round int64 `protobuf:"varint,1,opt,name=round,proto3" json:"round,omitempty"` // 场次=>对应configPro的star_loop.ID
	Page  int64 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`   // 页码
}

func (x *StarLotteryRecordReq) Reset() {
	*x = StarLotteryRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarLotteryRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarLotteryRecordReq) ProtoMessage() {}

func (x *StarLotteryRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarLotteryRecordReq.ProtoReflect.Descriptor instead.
func (*StarLotteryRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{10}
}

func (x *StarLotteryRecordReq) GetRound() int64 {
	if x != nil {
		return x.Round
	}
	return 0
}

func (x *StarLotteryRecordReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

// StarLotteryRecordRsp 明星活动抽奖记录 Rsp
type StarLotteryRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ll []*StarLotteryRecord `protobuf:"bytes,1,rep,name=ll,proto3" json:"ll,omitempty"` // 抽奖记录
}

func (x *StarLotteryRecordRsp) Reset() {
	*x = StarLotteryRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarLotteryRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarLotteryRecordRsp) ProtoMessage() {}

func (x *StarLotteryRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarLotteryRecordRsp.ProtoReflect.Descriptor instead.
func (*StarLotteryRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{11}
}

func (x *StarLotteryRecordRsp) GetLl() []*StarLotteryRecord {
	if x != nil {
		return x.Ll
	}
	return nil
}

type StarLotteryRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tt         int64       `protobuf:"varint,1,opt,name=tt,proto3" json:"tt,omitempty"`                                  // 抽奖时间
	RewardInfo *RewardInfo `protobuf:"bytes,2,opt,name=reward_info,json=rewardInfo,proto3" json:"reward_info,omitempty"` // 奖品信息
}

func (x *StarLotteryRecord) Reset() {
	*x = StarLotteryRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarLotteryRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarLotteryRecord) ProtoMessage() {}

func (x *StarLotteryRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarLotteryRecord.ProtoReflect.Descriptor instead.
func (*StarLotteryRecord) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{12}
}

func (x *StarLotteryRecord) GetTt() int64 {
	if x != nil {
		return x.Tt
	}
	return 0
}

func (x *StarLotteryRecord) GetRewardInfo() *RewardInfo {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

// StarStatusReq 明星活动数值状态 Req
type StarStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Round    int64                  `protobuf:"varint,1,opt,name=round,proto3" json:"round,omitempty"`                                                             // 场次=>对应configPro的star_loop.ID
	JoinType StarStatusReq_JoinType `protobuf:"varint,2,opt,name=join_type,json=joinType,proto3,enum=xian_cwsx.StarStatusReq_JoinType" json:"join_type,omitempty"` // 参与类型
}

func (x *StarStatusReq) Reset() {
	*x = StarStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarStatusReq) ProtoMessage() {}

func (x *StarStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarStatusReq.ProtoReflect.Descriptor instead.
func (*StarStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{13}
}

func (x *StarStatusReq) GetRound() int64 {
	if x != nil {
		return x.Round
	}
	return 0
}

func (x *StarStatusReq) GetJoinType() StarStatusReq_JoinType {
	if x != nil {
		return x.JoinType
	}
	return StarStatusReq_JoinType_None
}

// StarStatusRsp 明星活动数值状态 Rsp
type StarStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	T               uint32 `protobuf:"varint,1,opt,name=t,proto3" json:"t,omitempty"`                                                      // 参与类型 StarStatusType
	DayVolume       uint32 `protobuf:"varint,2,opt,name=day_volume,json=dayVolume,proto3" json:"day_volume,omitempty"`                     // 单日抽奖券-累计多少张
	TotalVolume     uint32 `protobuf:"varint,3,opt,name=total_volume,json=totalVolume,proto3" json:"total_volume,omitempty"`               // 总抽奖券-剩余多少张
	Ranking         int32  `protobuf:"varint,4,opt,name=ranking,proto3" json:"ranking,omitempty"`                                          // 排名 [活动结束后才会有值]
	AwardStatus     int32  `protobuf:"varint,5,opt,name=award_status,json=awardStatus,proto3" json:"award_status,omitempty"`               // 是否已领奖=>AwardStatusType
	RewardList      string `protobuf:"bytes,6,opt,name=reward_list,json=rewardList,proto3" json:"reward_list,omitempty"`                   // 公示期结束，玩家符合领奖状态才可能会有值，
	DaySurplusAd    int32  `protobuf:"varint,7,opt,name=day_surplus_ad,json=daySurplusAd,proto3" json:"day_surplus_ad,omitempty"`          // 每日剩余广告次数
	DaySurplusShare int32  `protobuf:"varint,8,opt,name=day_surplus_share,json=daySurplusShare,proto3" json:"day_surplus_share,omitempty"` // 每日剩余分享次数
}

func (x *StarStatusRsp) Reset() {
	*x = StarStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarStatusRsp) ProtoMessage() {}

func (x *StarStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarStatusRsp.ProtoReflect.Descriptor instead.
func (*StarStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{14}
}

func (x *StarStatusRsp) GetT() uint32 {
	if x != nil {
		return x.T
	}
	return 0
}

func (x *StarStatusRsp) GetDayVolume() uint32 {
	if x != nil {
		return x.DayVolume
	}
	return 0
}

func (x *StarStatusRsp) GetTotalVolume() uint32 {
	if x != nil {
		return x.TotalVolume
	}
	return 0
}

func (x *StarStatusRsp) GetRanking() int32 {
	if x != nil {
		return x.Ranking
	}
	return 0
}

func (x *StarStatusRsp) GetAwardStatus() int32 {
	if x != nil {
		return x.AwardStatus
	}
	return 0
}

func (x *StarStatusRsp) GetRewardList() string {
	if x != nil {
		return x.RewardList
	}
	return ""
}

func (x *StarStatusRsp) GetDaySurplusAd() int32 {
	if x != nil {
		return x.DaySurplusAd
	}
	return 0
}

func (x *StarStatusRsp) GetDaySurplusShare() int32 {
	if x != nil {
		return x.DaySurplusShare
	}
	return 0
}

type TreasureCardStar struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TreasureCardId uint32 `protobuf:"varint,1,opt,name=treasureCardId,proto3" json:"treasureCardId,omitempty"` // 宝藏卡id
	IsDecompose    bool   `protobuf:"varint,2,opt,name=isDecompose,proto3" json:"isDecompose,omitempty"`       // 是否被分
	DecomposeNum   uint32 `protobuf:"varint,3,opt,name=decomposeNum,proto3" json:"decomposeNum,omitempty"`     // 分解出多少碎片
}

func (x *TreasureCardStar) Reset() {
	*x = TreasureCardStar{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TreasureCardStar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TreasureCardStar) ProtoMessage() {}

func (x *TreasureCardStar) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TreasureCardStar.ProtoReflect.Descriptor instead.
func (*TreasureCardStar) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{15}
}

func (x *TreasureCardStar) GetTreasureCardId() uint32 {
	if x != nil {
		return x.TreasureCardId
	}
	return 0
}

func (x *TreasureCardStar) GetIsDecompose() bool {
	if x != nil {
		return x.IsDecompose
	}
	return false
}

func (x *TreasureCardStar) GetDecomposeNum() uint32 {
	if x != nil {
		return x.DecomposeNum
	}
	return 0
}

// StarEndStatusReq 领取排行榜奖励 Req
type StarEndStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Round int64 `protobuf:"varint,1,opt,name=round,proto3" json:"round,omitempty"` // 场次=>对应configPro的star_loop.ID
}

func (x *StarEndStatusReq) Reset() {
	*x = StarEndStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarEndStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarEndStatusReq) ProtoMessage() {}

func (x *StarEndStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarEndStatusReq.ProtoReflect.Descriptor instead.
func (*StarEndStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{16}
}

func (x *StarEndStatusReq) GetRound() int64 {
	if x != nil {
		return x.Round
	}
	return 0
}

// StarEndStatusRsp 领取排行榜奖励 Rsp
type StarEndStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ranking       int32               `protobuf:"varint,1,opt,name=ranking,proto3" json:"ranking,omitempty"`            // 排名
	TreasureCards []*TreasureCardStar `protobuf:"bytes,2,rep,name=treasureCards,proto3" json:"treasureCards,omitempty"` // 下发道具的宝藏奖励
}

func (x *StarEndStatusRsp) Reset() {
	*x = StarEndStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarEndStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarEndStatusRsp) ProtoMessage() {}

func (x *StarEndStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarEndStatusRsp.ProtoReflect.Descriptor instead.
func (*StarEndStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{17}
}

func (x *StarEndStatusRsp) GetRanking() int32 {
	if x != nil {
		return x.Ranking
	}
	return 0
}

func (x *StarEndStatusRsp) GetTreasureCards() []*TreasureCardStar {
	if x != nil {
		return x.TreasureCards
	}
	return nil
}

type RewardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftNumber    int32               `protobuf:"varint,1,opt,name=gift_number,json=giftNumber,proto3" json:"gift_number,omitempty"` // -1 是明星活动内部礼包 大于0，则是礼物包ID =>ActivityPostcardType
	Bundles       *StarBundle         `protobuf:"bytes,2,opt,name=bundles,proto3" json:"bundles,omitempty"`                          // 奖励信息【礼包平台单个礼包的具体信息】
	TreasureCards []*TreasureCardStar `protobuf:"bytes,3,rep,name=treasureCards,proto3" json:"treasureCards,omitempty"`              // 下发道具的宝藏奖励
	GiftCnt       int32               `protobuf:"varint,4,opt,name=gift_cnt,json=giftCnt,proto3" json:"gift_cnt,omitempty"`          // 礼包发放的数量，默认为1
}

func (x *RewardInfo) Reset() {
	*x = RewardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardInfo) ProtoMessage() {}

func (x *RewardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardInfo.ProtoReflect.Descriptor instead.
func (*RewardInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{18}
}

func (x *RewardInfo) GetGiftNumber() int32 {
	if x != nil {
		return x.GiftNumber
	}
	return 0
}

func (x *RewardInfo) GetBundles() *StarBundle {
	if x != nil {
		return x.Bundles
	}
	return nil
}

func (x *RewardInfo) GetTreasureCards() []*TreasureCardStar {
	if x != nil {
		return x.TreasureCards
	}
	return nil
}

func (x *RewardInfo) GetGiftCnt() int32 {
	if x != nil {
		return x.GiftCnt
	}
	return 0
}

type StarBundle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*StarItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *StarBundle) Reset() {
	*x = StarBundle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarBundle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarBundle) ProtoMessage() {}

func (x *StarBundle) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarBundle.ProtoReflect.Descriptor instead.
func (*StarBundle) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{19}
}

func (x *StarBundle) GetItems() []*StarItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type StarItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemId int64 `protobuf:"varint,1,opt,name=itemId,proto3" json:"itemId,omitempty"`
	Num    int64 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	Type   int64 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"` // 1=>游戏内物资 2=>平台内物资 3=>游戏内特殊物资
}

func (x *StarItem) Reset() {
	*x = StarItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarItem) ProtoMessage() {}

func (x *StarItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarItem.ProtoReflect.Descriptor instead.
func (*StarItem) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{20}
}

func (x *StarItem) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *StarItem) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *StarItem) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

// ActivityStatusReq 活动状态 Req
type ActivityStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ActivityStatusReq) Reset() {
	*x = ActivityStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityStatusReq) ProtoMessage() {}

func (x *ActivityStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityStatusReq.ProtoReflect.Descriptor instead.
func (*ActivityStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{21}
}

// ActivityStatusRsp 活动状态 Rsp
type ActivityStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StatusMap map[uint32]uint32 `protobuf:"bytes,1,rep,name=status_map,json=statusMap,proto3" json:"status_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 查看下面的ActivityStatusType=>StarStatusType
}

func (x *ActivityStatusRsp) Reset() {
	*x = ActivityStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityStatusRsp) ProtoMessage() {}

func (x *ActivityStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityStatusRsp.ProtoReflect.Descriptor instead.
func (*ActivityStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{22}
}

func (x *ActivityStatusRsp) GetStatusMap() map[uint32]uint32 {
	if x != nil {
		return x.StatusMap
	}
	return nil
}

// StarExitReq 活动退出 Req
type StarExitReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Round int64 `protobuf:"varint,1,opt,name=round,proto3" json:"round,omitempty"` // 场次=>对应configPro的star_loop.ID
}

func (x *StarExitReq) Reset() {
	*x = StarExitReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarExitReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarExitReq) ProtoMessage() {}

func (x *StarExitReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarExitReq.ProtoReflect.Descriptor instead.
func (*StarExitReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{23}
}

func (x *StarExitReq) GetRound() int64 {
	if x != nil {
		return x.Round
	}
	return 0
}

// StarExitRsp 活动退出 Rsp
type StarExitRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StarExitRsp) Reset() {
	*x = StarExitRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarExitRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarExitRsp) ProtoMessage() {}

func (x *StarExitRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarExitRsp.ProtoReflect.Descriptor instead.
func (*StarExitRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP(), []int{24}
}

var File_pb_game_cwsx_xian_cwsx_activity_star_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2f, 0x73, 0x74, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x22, 0x37, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x72, 0x52,
	0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x22, 0x6c, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12,
	0x27, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x52, 0x61,
	0x6e, 0x6b, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x09, 0x73, 0x65, 0x6c, 0x66,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x52, 0x61, 0x6e, 0x6b,
	0x53, 0x65, 0x6c, 0x66, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x7d,
	0x0a, 0x08, 0x53, 0x74, 0x61, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x74, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x74, 0x22, 0x9b, 0x01,
	0x0a, 0x0c, 0x53, 0x74, 0x61, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x53, 0x65, 0x6c, 0x66, 0x12, 0x17,
	0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x22, 0x38, 0x0a, 0x0e, 0x53,
	0x74, 0x61, 0x72, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a,
	0x03, 0x74, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x22, 0x8a, 0x01, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x72, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12,
	0x41, 0x0a, 0x0d, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x53,
	0x74, 0x61, 0x72, 0x52, 0x0d, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72,
	0x64, 0x73, 0x22, 0x24, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x72, 0x53, 0x68, 0x61, 0x72, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x22, 0x0e, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x72,
	0x53, 0x68, 0x61, 0x72, 0x65, 0x52, 0x73, 0x70, 0x22, 0x5c, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x72,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x74, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x22, 0xc6, 0x01, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x72, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x09, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x4d,
	0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x53, 0x69, 0x74, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x73, 0x69, 0x74, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x1a, 0x3b, 0x0a, 0x0d, 0x53, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x40, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x72, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x22, 0x44, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x72, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x2c, 0x0a, 0x02, 0x6c, 0x6c, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x02, 0x6c, 0x6c, 0x22, 0x5b, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x72, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x74, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x74, 0x12, 0x36, 0x0a, 0x0b,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x97, 0x01, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x3e, 0x0a, 0x09,
	0x6a, 0x6f, 0x69, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x21, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x08, 0x6a, 0x6f, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x30, 0x0a, 0x08,
	0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x4a, 0x6f, 0x69, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x4a,
	0x6f, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4a, 0x6f, 0x69, 0x6e, 0x10, 0x01, 0x22, 0x87,
	0x03, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70,
	0x12, 0x0c, 0x0a, 0x01, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x64, 0x61, 0x79, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x64, 0x61, 0x79, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x61, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x24,
	0x0a, 0x0e, 0x64, 0x61, 0x79, 0x5f, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x5f, 0x61, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64, 0x61, 0x79, 0x53, 0x75, 0x72, 0x70, 0x6c,
	0x75, 0x73, 0x41, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x61, 0x79, 0x5f, 0x73, 0x75, 0x72, 0x70,
	0x6c, 0x75, 0x73, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x64, 0x61, 0x79, 0x53, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x22, 0x76, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x53,
	0x74, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4a, 0x6f,
	0x69, 0x6e, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x74, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x02, 0x12,
	0x17, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x45, 0x78, 0x69, 0x74, 0x10, 0x03, 0x22, 0x80, 0x01, 0x0a, 0x10, 0x54, 0x72, 0x65,
	0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x72, 0x12, 0x26, 0x0a,
	0x0e, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43,
	0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x73, 0x44, 0x65, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x44, 0x65,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x65, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x64,
	0x65, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x22, 0x28, 0x0a, 0x10, 0x53,
	0x74, 0x61, 0x72, 0x45, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x14, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x22, 0x6f, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x72, 0x45, 0x6e, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x61, 0x6e,
	0x6b, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x61, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x12, 0x41, 0x0a, 0x0d, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43,
	0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43,
	0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x72, 0x52, 0x0d, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72,
	0x65, 0x43, 0x61, 0x72, 0x64, 0x73, 0x22, 0xbc, 0x01, 0x0a, 0x0a, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x67, 0x69, 0x66, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x07, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x07,
	0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x12, 0x41, 0x0a, 0x0d, 0x74, 0x72, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x72, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x72, 0x52, 0x0d, 0x74, 0x72, 0x65,
	0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x69,
	0x66, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x67, 0x69,
	0x66, 0x74, 0x43, 0x6e, 0x74, 0x22, 0x37, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x72, 0x42, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x12, 0x29, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53,
	0x74, 0x61, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x48,
	0x0a, 0x08, 0x53, 0x74, 0x61, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x74,
	0x65, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x13, 0x0a, 0x11, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x22, 0x9d, 0x01,
	0x0a, 0x11, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x73, 0x70, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x61, 0x70, 0x1a,
	0x3c, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x23, 0x0a,
	0x0b, 0x53, 0x74, 0x61, 0x72, 0x45, 0x78, 0x69, 0x74, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x22, 0x0d, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x72, 0x45, 0x78, 0x69, 0x74, 0x52, 0x73,
	0x70, 0x2a, 0x62, 0x0a, 0x0f, 0x41, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x1b,
	0x0a, 0x17, 0x41, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x41, 0x77, 0x61, 0x72, 0x64, 0x65, 0x64, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x41,
	0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x57,
	0x61, 0x69, 0x74, 0x10, 0x02, 0x2a, 0xbc, 0x01, 0x0a, 0x14, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x72, 0x47, 0x69, 0x66, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d,
	0x0a, 0x19, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x72, 0x47, 0x69,
	0x66, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x2a, 0x0a,
	0x1d, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x72, 0x47, 0x69, 0x66,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x6f, 0x73, 0x74, 0x63, 0x61, 0x72, 0x64, 0x10, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x2f, 0x0a, 0x22, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x72, 0x47, 0x69, 0x66, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x53, 0x74, 0x61, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x10,
	0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x28, 0x0a, 0x1b, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x72, 0x47, 0x69, 0x66, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x10, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0x01, 0x2a, 0x4e, 0x0a, 0x12, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x74,
	0x61, 0x72, 0x10, 0x01, 0x2a, 0x86, 0x01, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x74, 0x61, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4f, 0x66, 0x66, 0x10, 0x00, 0x12,
	0x1e, 0x0a, 0x1a, 0x53, 0x74, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x4a, 0x6f, 0x69, 0x6e, 0x41, 0x6e, 0x64, 0x53, 0x68, 0x6f, 0x77, 0x10, 0x01, 0x12,
	0x1b, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x4a, 0x75, 0x73, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b,
	0x53, 0x74, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x45,
	0x78, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x10, 0x03, 0x42, 0x5d, 0x5a,
	0x5b, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x6d, 0x65, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x63, 0x6f, 0x72, 0x70,
	0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescOnce sync.Once
	file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescData = file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDesc
)

func file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescGZIP() []byte {
	file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescOnce.Do(func() {
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescData)
	})
	return file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDescData
}

var file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_pb_game_cwsx_xian_cwsx_activity_star_proto_goTypes = []interface{}{
	(AwardStatusType)(0),              // 0: xian_cwsx.AwardStatusType
	(ActivityStarGiftType)(0),         // 1: xian_cwsx.ActivityStarGiftType
	(ActivityStatusType)(0),           // 2: xian_cwsx.ActivityStatusType
	(StarStatusType)(0),               // 3: xian_cwsx.StarStatusType
	(StarStatusReq_JoinType)(0),       // 4: xian_cwsx.StarStatusReq.JoinType
	(StarStatusRsp_StarStatusType)(0), // 5: xian_cwsx.StarStatusRsp.StarStatusType
	(*StarRankReq)(nil),               // 6: xian_cwsx.StarRankReq
	(*StarRankRsp)(nil),               // 7: xian_cwsx.StarRankRsp
	(*StarRank)(nil),                  // 8: xian_cwsx.StarRank
	(*StarRankSelf)(nil),              // 9: xian_cwsx.StarRankSelf
	(*StarLotteryReq)(nil),            // 10: xian_cwsx.StarLotteryReq
	(*StarLotteryRsp)(nil),            // 11: xian_cwsx.StarLotteryRsp
	(*StarShareReq)(nil),              // 12: xian_cwsx.StarShareReq
	(*StarShareRsp)(nil),              // 13: xian_cwsx.StarShareRsp
	(*StarLotteryMultipleReq)(nil),    // 14: xian_cwsx.StarLotteryMultipleReq
	(*StarLotteryMultipleRsp)(nil),    // 15: xian_cwsx.StarLotteryMultipleRsp
	(*StarLotteryRecordReq)(nil),      // 16: xian_cwsx.StarLotteryRecordReq
	(*StarLotteryRecordRsp)(nil),      // 17: xian_cwsx.StarLotteryRecordRsp
	(*StarLotteryRecord)(nil),         // 18: xian_cwsx.StarLotteryRecord
	(*StarStatusReq)(nil),             // 19: xian_cwsx.StarStatusReq
	(*StarStatusRsp)(nil),             // 20: xian_cwsx.StarStatusRsp
	(*TreasureCardStar)(nil),          // 21: xian_cwsx.TreasureCardStar
	(*StarEndStatusReq)(nil),          // 22: xian_cwsx.StarEndStatusReq
	(*StarEndStatusRsp)(nil),          // 23: xian_cwsx.StarEndStatusRsp
	(*RewardInfo)(nil),                // 24: xian_cwsx.RewardInfo
	(*StarBundle)(nil),                // 25: xian_cwsx.StarBundle
	(*StarItem)(nil),                  // 26: xian_cwsx.StarItem
	(*ActivityStatusReq)(nil),         // 27: xian_cwsx.ActivityStatusReq
	(*ActivityStatusRsp)(nil),         // 28: xian_cwsx.ActivityStatusRsp
	(*StarExitReq)(nil),               // 29: xian_cwsx.StarExitReq
	(*StarExitRsp)(nil),               // 30: xian_cwsx.StarExitRsp
	nil,                               // 31: xian_cwsx.StarLotteryMultipleRsp.SiteListEntry
	nil,                               // 32: xian_cwsx.ActivityStatusRsp.StatusMapEntry
}
var file_pb_game_cwsx_xian_cwsx_activity_star_proto_depIdxs = []int32{
	8,  // 0: xian_cwsx.StarRankRsp.list:type_name -> xian_cwsx.StarRank
	9,  // 1: xian_cwsx.StarRankRsp.self_info:type_name -> xian_cwsx.StarRankSelf
	21, // 2: xian_cwsx.StarLotteryRsp.treasureCards:type_name -> xian_cwsx.TreasureCardStar
	31, // 3: xian_cwsx.StarLotteryMultipleRsp.site_list:type_name -> xian_cwsx.StarLotteryMultipleRsp.SiteListEntry
	18, // 4: xian_cwsx.StarLotteryRecordRsp.ll:type_name -> xian_cwsx.StarLotteryRecord
	24, // 5: xian_cwsx.StarLotteryRecord.reward_info:type_name -> xian_cwsx.RewardInfo
	4,  // 6: xian_cwsx.StarStatusReq.join_type:type_name -> xian_cwsx.StarStatusReq.JoinType
	21, // 7: xian_cwsx.StarEndStatusRsp.treasureCards:type_name -> xian_cwsx.TreasureCardStar
	25, // 8: xian_cwsx.RewardInfo.bundles:type_name -> xian_cwsx.StarBundle
	21, // 9: xian_cwsx.RewardInfo.treasureCards:type_name -> xian_cwsx.TreasureCardStar
	26, // 10: xian_cwsx.StarBundle.items:type_name -> xian_cwsx.StarItem
	32, // 11: xian_cwsx.ActivityStatusRsp.status_map:type_name -> xian_cwsx.ActivityStatusRsp.StatusMapEntry
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_xian_cwsx_activity_star_proto_init() }
func file_pb_game_cwsx_xian_cwsx_activity_star_proto_init() {
	if File_pb_game_cwsx_xian_cwsx_activity_star_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarRank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarRankSelf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarLotteryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarLotteryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarShareReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarShareRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarLotteryMultipleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarLotteryMultipleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarLotteryRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarLotteryRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarLotteryRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TreasureCardStar); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarEndStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarEndStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarBundle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarExitReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarExitRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_game_cwsx_xian_cwsx_activity_star_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_xian_cwsx_activity_star_proto_depIdxs,
		EnumInfos:         file_pb_game_cwsx_xian_cwsx_activity_star_proto_enumTypes,
		MessageInfos:      file_pb_game_cwsx_xian_cwsx_activity_star_proto_msgTypes,
	}.Build()
	File_pb_game_cwsx_xian_cwsx_activity_star_proto = out.File
	file_pb_game_cwsx_xian_cwsx_activity_star_proto_rawDesc = nil
	file_pb_game_cwsx_xian_cwsx_activity_star_proto_goTypes = nil
	file_pb_game_cwsx_xian_cwsx_activity_star_proto_depIdxs = nil
}
