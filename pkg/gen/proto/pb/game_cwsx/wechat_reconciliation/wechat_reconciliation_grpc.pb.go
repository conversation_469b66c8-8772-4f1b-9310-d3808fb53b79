// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_cwsx/wechat_reconciliation/wechat_reconciliation.proto

package wechat_reconciliation

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	WechatReconciliation_Reconciliation_FullMethodName = "/game_cwsx.WechatReconciliation/Reconciliation"
	WechatReconciliation_SyncOrders_FullMethodName     = "/game_cwsx.WechatReconciliation/SyncOrders"
)

// WechatReconciliationClient is the client API for WechatReconciliation service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WechatReconciliationClient interface {
	// 对账接口
	Reconciliation(ctx context.Context, in *ReconciliationReq, opts ...grpc.CallOption) (*ReconciliationRsp, error)
	// 同步订单接口
	SyncOrders(ctx context.Context, in *SyncOrdersReq, opts ...grpc.CallOption) (*SyncOrdersRsp, error)
}

type wechatReconciliationClient struct {
	cc grpc.ClientConnInterface
}

func NewWechatReconciliationClient(cc grpc.ClientConnInterface) WechatReconciliationClient {
	return &wechatReconciliationClient{cc}
}

func (c *wechatReconciliationClient) Reconciliation(ctx context.Context, in *ReconciliationReq, opts ...grpc.CallOption) (*ReconciliationRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReconciliationRsp)
	err := c.cc.Invoke(ctx, WechatReconciliation_Reconciliation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wechatReconciliationClient) SyncOrders(ctx context.Context, in *SyncOrdersReq, opts ...grpc.CallOption) (*SyncOrdersRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncOrdersRsp)
	err := c.cc.Invoke(ctx, WechatReconciliation_SyncOrders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WechatReconciliationServer is the server API for WechatReconciliation service.
// All implementations should embed UnimplementedWechatReconciliationServer
// for forward compatibility
type WechatReconciliationServer interface {
	// 对账接口
	Reconciliation(context.Context, *ReconciliationReq) (*ReconciliationRsp, error)
	// 同步订单接口
	SyncOrders(context.Context, *SyncOrdersReq) (*SyncOrdersRsp, error)
}

// UnimplementedWechatReconciliationServer should be embedded to have forward compatible implementations.
type UnimplementedWechatReconciliationServer struct {
}

func (UnimplementedWechatReconciliationServer) Reconciliation(context.Context, *ReconciliationReq) (*ReconciliationRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Reconciliation not implemented")
}
func (UnimplementedWechatReconciliationServer) SyncOrders(context.Context, *SyncOrdersReq) (*SyncOrdersRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncOrders not implemented")
}

// UnsafeWechatReconciliationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WechatReconciliationServer will
// result in compilation errors.
type UnsafeWechatReconciliationServer interface {
	mustEmbedUnimplementedWechatReconciliationServer()
}

func RegisterWechatReconciliationServer(s grpc.ServiceRegistrar, srv WechatReconciliationServer) {
	s.RegisterService(&WechatReconciliation_ServiceDesc, srv)
}

func _WechatReconciliation_Reconciliation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReconciliationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WechatReconciliationServer).Reconciliation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WechatReconciliation_Reconciliation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WechatReconciliationServer).Reconciliation(ctx, req.(*ReconciliationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WechatReconciliation_SyncOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncOrdersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WechatReconciliationServer).SyncOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WechatReconciliation_SyncOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WechatReconciliationServer).SyncOrders(ctx, req.(*SyncOrdersReq))
	}
	return interceptor(ctx, in, info, handler)
}

// WechatReconciliation_ServiceDesc is the grpc.ServiceDesc for WechatReconciliation service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WechatReconciliation_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game_cwsx.WechatReconciliation",
	HandlerType: (*WechatReconciliationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Reconciliation",
			Handler:    _WechatReconciliation_Reconciliation_Handler,
		},
		{
			MethodName: "SyncOrders",
			Handler:    _WechatReconciliation_SyncOrders_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_cwsx/wechat_reconciliation/wechat_reconciliation.proto",
}
