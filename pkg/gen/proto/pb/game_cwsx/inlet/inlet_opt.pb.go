// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/inlet/inlet_opt.proto

package inlet

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ManageSwitch 开关类型
type ManageSwitch int32

const (
	ManageSwitch_ManageSwitch_OFF      ManageSwitch = 0 // 关闭
	ManageSwitch_ManageSwitch_Yes      ManageSwitch = 1 // 开启
	ManageSwitch_ManageSwitch_SwitchGm ManageSwitch = 2 // 白名单开启
)

// Enum value maps for ManageSwitch.
var (
	ManageSwitch_name = map[int32]string{
		0: "ManageSwitch_OFF",
		1: "ManageSwitch_Yes",
		2: "ManageSwitch_SwitchGm",
	}
	ManageSwitch_value = map[string]int32{
		"ManageSwitch_OFF":      0,
		"ManageSwitch_Yes":      1,
		"ManageSwitch_SwitchGm": 2,
	}
)

func (x ManageSwitch) Enum() *ManageSwitch {
	p := new(ManageSwitch)
	*p = x
	return p
}

func (x ManageSwitch) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ManageSwitch) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_inlet_inlet_opt_proto_enumTypes[0].Descriptor()
}

func (ManageSwitch) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_inlet_inlet_opt_proto_enumTypes[0]
}

func (x ManageSwitch) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ManageSwitch.Descriptor instead.
func (ManageSwitch) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_inlet_inlet_opt_proto_rawDescGZIP(), []int{0}
}

// ActivityOptReq 活动配置 Req
type ActivityOptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number InletNumber `protobuf:"varint,1,opt,name=number,proto3,enum=inlet.InletNumber" json:"number,omitempty"` // 入口编号
	V      int64       `protobuf:"varint,2,opt,name=v,proto3" json:"v,omitempty"`                                  // 版本号
}

func (x *ActivityOptReq) Reset() {
	*x = ActivityOptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityOptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityOptReq) ProtoMessage() {}

func (x *ActivityOptReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityOptReq.ProtoReflect.Descriptor instead.
func (*ActivityOptReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_inlet_inlet_opt_proto_rawDescGZIP(), []int{0}
}

func (x *ActivityOptReq) GetNumber() InletNumber {
	if x != nil {
		return x.Number
	}
	return InletNumber_InletNumber_None
}

func (x *ActivityOptReq) GetV() int64 {
	if x != nil {
		return x.V
	}
	return 0
}

// ActivityOptRsp 活动配置 Rsp
type ActivityOptRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V   int64        `protobuf:"varint,1,opt,name=v,proto3" json:"v,omitempty"`    // 版本号
	Opt *ActivityOpt `protobuf:"bytes,2,opt,name=opt,proto3" json:"opt,omitempty"` // 活动配置
}

func (x *ActivityOptRsp) Reset() {
	*x = ActivityOptRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityOptRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityOptRsp) ProtoMessage() {}

func (x *ActivityOptRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityOptRsp.ProtoReflect.Descriptor instead.
func (*ActivityOptRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_inlet_inlet_opt_proto_rawDescGZIP(), []int{1}
}

func (x *ActivityOptRsp) GetV() int64 {
	if x != nil {
		return x.V
	}
	return 0
}

func (x *ActivityOptRsp) GetOpt() *ActivityOpt {
	if x != nil {
		return x.Opt
	}
	return nil
}

// ActivityOpt 活动配置 Rsp
type ActivityOpt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number           InletNumber        `protobuf:"varint,1,opt,name=number,proto3,enum=inlet.InletNumber" json:"number,omitempty"`                                        // 入口编号
	Switch           ManageSwitch       `protobuf:"varint,2,opt,name=switch,proto3,enum=inlet.ManageSwitch" json:"switch,omitempty"`                                       // 总开关类型
	SwitchDeviceType uint32             `protobuf:"varint,3,opt,name=switch_device_type,json=switchDeviceType,proto3" json:"switch_device_type,omitempty"`                 // 手机类型开关  安卓[1<<1]  |  ios[1<<2]
	Threshold        int32              `protobuf:"varint,4,opt,name=threshold,proto3" json:"threshold,omitempty"`                                                         // 关卡展示门槛
	ThresholdMaxShow int32              `protobuf:"varint,5,opt,name=threshold_max_show,json=thresholdMaxShow,proto3" json:"threshold_max_show,omitempty"`                 // 关卡最大展示门槛 -1 则不使用
	DurationType     ManageDurationType `protobuf:"varint,6,opt,name=duration_type,json=durationType,proto3,enum=inlet.ManageDurationType" json:"duration_type,omitempty"` // 时间类型
	WeekObj          *ManageWeekObj     `protobuf:"bytes,7,opt,name=week_obj,json=weekObj,proto3" json:"week_obj,omitempty"`                                               // 时间周期对象
}

func (x *ActivityOpt) Reset() {
	*x = ActivityOpt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityOpt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityOpt) ProtoMessage() {}

func (x *ActivityOpt) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityOpt.ProtoReflect.Descriptor instead.
func (*ActivityOpt) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_inlet_inlet_opt_proto_rawDescGZIP(), []int{2}
}

func (x *ActivityOpt) GetNumber() InletNumber {
	if x != nil {
		return x.Number
	}
	return InletNumber_InletNumber_None
}

func (x *ActivityOpt) GetSwitch() ManageSwitch {
	if x != nil {
		return x.Switch
	}
	return ManageSwitch_ManageSwitch_OFF
}

func (x *ActivityOpt) GetSwitchDeviceType() uint32 {
	if x != nil {
		return x.SwitchDeviceType
	}
	return 0
}

func (x *ActivityOpt) GetThreshold() int32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *ActivityOpt) GetThresholdMaxShow() int32 {
	if x != nil {
		return x.ThresholdMaxShow
	}
	return 0
}

func (x *ActivityOpt) GetDurationType() ManageDurationType {
	if x != nil {
		return x.DurationType
	}
	return ManageDurationType_ManageDurationType_DurationNone
}

func (x *ActivityOpt) GetWeekObj() *ManageWeekObj {
	if x != nil {
		return x.WeekObj
	}
	return nil
}

// ManageWeekObj 时间周期对象
type ManageWeekObj struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 周刷新
	WBeginWeek  int32  `protobuf:"varint,1,opt,name=w_begin_week,json=wBeginWeek,proto3" json:"w_begin_week,omitempty"`   // 周几开始
	WBeginClock string `protobuf:"bytes,2,opt,name=w_begin_clock,json=wBeginClock,proto3" json:"w_begin_clock,omitempty"` // 几点开始 00:00:00
	WDuration   int32  `protobuf:"varint,3,opt,name=w_duration,json=wDuration,proto3" json:"w_duration,omitempty"`        // 持续时间(s)
	// 周每天刷新
	WdWeek       int32  `protobuf:"varint,4,opt,name=wd_week,json=wdWeek,proto3" json:"wd_week,omitempty"`                    // 活动执行周期 二进制占位
	WdBeginClock string `protobuf:"bytes,5,opt,name=wd_begin_clock,json=wdBeginClock,proto3" json:"wd_begin_clock,omitempty"` // 开始时间点 [00->23]
	WdEndClock   string `protobuf:"bytes,6,opt,name=wd_end_clock,json=wdEndClock,proto3" json:"wd_end_clock,omitempty"`       // 结束时间点 [00->23]
	WdDuration   int32  `protobuf:"varint,7,opt,name=wd_duration,json=wdDuration,proto3" json:"wd_duration,omitempty"`        // 活动持续时间(s)
}

func (x *ManageWeekObj) Reset() {
	*x = ManageWeekObj{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManageWeekObj) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManageWeekObj) ProtoMessage() {}

func (x *ManageWeekObj) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManageWeekObj.ProtoReflect.Descriptor instead.
func (*ManageWeekObj) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_inlet_inlet_opt_proto_rawDescGZIP(), []int{3}
}

func (x *ManageWeekObj) GetWBeginWeek() int32 {
	if x != nil {
		return x.WBeginWeek
	}
	return 0
}

func (x *ManageWeekObj) GetWBeginClock() string {
	if x != nil {
		return x.WBeginClock
	}
	return ""
}

func (x *ManageWeekObj) GetWDuration() int32 {
	if x != nil {
		return x.WDuration
	}
	return 0
}

func (x *ManageWeekObj) GetWdWeek() int32 {
	if x != nil {
		return x.WdWeek
	}
	return 0
}

func (x *ManageWeekObj) GetWdBeginClock() string {
	if x != nil {
		return x.WdBeginClock
	}
	return ""
}

func (x *ManageWeekObj) GetWdEndClock() string {
	if x != nil {
		return x.WdEndClock
	}
	return ""
}

func (x *ManageWeekObj) GetWdDuration() int32 {
	if x != nil {
		return x.WdDuration
	}
	return 0
}

var File_pb_game_cwsx_inlet_inlet_opt_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_inlet_inlet_opt_proto_rawDesc = []byte{
	0x0a, 0x22, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x69,
	0x6e, 0x6c, 0x65, 0x74, 0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x1a, 0x1e, 0x70, 0x62, 0x2f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2f,
	0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4a, 0x0a, 0x0e, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4f, 0x70, 0x74, 0x52, 0x65, 0x71, 0x12, 0x2a, 0x0a,
	0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e,
	0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x0c, 0x0a, 0x01, 0x76, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x01, 0x76, 0x22, 0x44, 0x0a, 0x0e, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x4f, 0x70, 0x74, 0x52, 0x73, 0x70, 0x12, 0x0c, 0x0a, 0x01, 0x76, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x01, 0x76, 0x12, 0x24, 0x0a, 0x03, 0x6f, 0x70, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x4f, 0x70, 0x74, 0x52, 0x03, 0x6f, 0x70, 0x74, 0x22, 0xd1, 0x02,
	0x0a, 0x0b, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4f, 0x70, 0x74, 0x12, 0x2a, 0x0a,
	0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e,
	0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x06, 0x73, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x69, 0x6e, 0x6c, 0x65,
	0x74, 0x2e, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x06,
	0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x10, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x5f,
	0x6d, 0x61, 0x78, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10,
	0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x4d, 0x61, 0x78, 0x53, 0x68, 0x6f, 0x77,
	0x12, 0x3e, 0x0a, 0x0d, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0c, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2f, 0x0a, 0x08, 0x77, 0x65, 0x65, 0x6b, 0x5f, 0x6f, 0x62, 0x6a, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x4d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x57, 0x65, 0x65, 0x6b, 0x4f, 0x62, 0x6a, 0x52, 0x07, 0x77, 0x65, 0x65, 0x6b, 0x4f, 0x62,
	0x6a, 0x22, 0xf6, 0x01, 0x0a, 0x0d, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x57, 0x65, 0x65, 0x6b,
	0x4f, 0x62, 0x6a, 0x12, 0x20, 0x0a, 0x0c, 0x77, 0x5f, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x77,
	0x65, 0x65, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x77, 0x42, 0x65, 0x67, 0x69,
	0x6e, 0x57, 0x65, 0x65, 0x6b, 0x12, 0x22, 0x0a, 0x0d, 0x77, 0x5f, 0x62, 0x65, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x42,
	0x65, 0x67, 0x69, 0x6e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x5f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x77,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x77, 0x64, 0x5f, 0x77,
	0x65, 0x65, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x77, 0x64, 0x57, 0x65, 0x65,
	0x6b, 0x12, 0x24, 0x0a, 0x0e, 0x77, 0x64, 0x5f, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6c,
	0x6f, 0x63, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x64, 0x42, 0x65, 0x67,
	0x69, 0x6e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x20, 0x0a, 0x0c, 0x77, 0x64, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77,
	0x64, 0x45, 0x6e, 0x64, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x64, 0x5f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x77, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2a, 0x55, 0x0a, 0x0c, 0x4d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x5f, 0x59, 0x65, 0x73, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x47, 0x6d, 0x10,
	0x02, 0x42, 0x50, 0x5a, 0x4e, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67,
	0x63, 0x6f, 0x72, 0x70, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e,
	0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x69, 0x6e,
	0x6c, 0x65, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_cwsx_inlet_inlet_opt_proto_rawDescOnce sync.Once
	file_pb_game_cwsx_inlet_inlet_opt_proto_rawDescData = file_pb_game_cwsx_inlet_inlet_opt_proto_rawDesc
)

func file_pb_game_cwsx_inlet_inlet_opt_proto_rawDescGZIP() []byte {
	file_pb_game_cwsx_inlet_inlet_opt_proto_rawDescOnce.Do(func() {
		file_pb_game_cwsx_inlet_inlet_opt_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_cwsx_inlet_inlet_opt_proto_rawDescData)
	})
	return file_pb_game_cwsx_inlet_inlet_opt_proto_rawDescData
}

var file_pb_game_cwsx_inlet_inlet_opt_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_pb_game_cwsx_inlet_inlet_opt_proto_goTypes = []interface{}{
	(ManageSwitch)(0),       // 0: inlet.ManageSwitch
	(*ActivityOptReq)(nil),  // 1: inlet.ActivityOptReq
	(*ActivityOptRsp)(nil),  // 2: inlet.ActivityOptRsp
	(*ActivityOpt)(nil),     // 3: inlet.ActivityOpt
	(*ManageWeekObj)(nil),   // 4: inlet.ManageWeekObj
	(InletNumber)(0),        // 5: inlet.InletNumber
	(ManageDurationType)(0), // 6: inlet.ManageDurationType
}
var file_pb_game_cwsx_inlet_inlet_opt_proto_depIdxs = []int32{
	5, // 0: inlet.ActivityOptReq.number:type_name -> inlet.InletNumber
	3, // 1: inlet.ActivityOptRsp.opt:type_name -> inlet.ActivityOpt
	5, // 2: inlet.ActivityOpt.number:type_name -> inlet.InletNumber
	0, // 3: inlet.ActivityOpt.switch:type_name -> inlet.ManageSwitch
	6, // 4: inlet.ActivityOpt.duration_type:type_name -> inlet.ManageDurationType
	4, // 5: inlet.ActivityOpt.week_obj:type_name -> inlet.ManageWeekObj
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_inlet_inlet_opt_proto_init() }
func file_pb_game_cwsx_inlet_inlet_opt_proto_init() {
	if File_pb_game_cwsx_inlet_inlet_opt_proto != nil {
		return
	}
	file_pb_game_cwsx_inlet_inlet_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityOptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityOptRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityOpt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManageWeekObj); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_inlet_inlet_opt_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_game_cwsx_inlet_inlet_opt_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_inlet_inlet_opt_proto_depIdxs,
		EnumInfos:         file_pb_game_cwsx_inlet_inlet_opt_proto_enumTypes,
		MessageInfos:      file_pb_game_cwsx_inlet_inlet_opt_proto_msgTypes,
	}.Build()
	File_pb_game_cwsx_inlet_inlet_opt_proto = out.File
	file_pb_game_cwsx_inlet_inlet_opt_proto_rawDesc = nil
	file_pb_game_cwsx_inlet_inlet_opt_proto_goTypes = nil
	file_pb_game_cwsx_inlet_inlet_opt_proto_depIdxs = nil
}
