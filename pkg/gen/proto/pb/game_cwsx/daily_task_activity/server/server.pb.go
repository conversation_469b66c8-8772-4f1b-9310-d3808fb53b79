// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/daily_task_activity/server/server.proto

package server

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	game_api "kugou_adapter_service/pkg/gen/proto/pb/game_api"
	inlet "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/inlet"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddProgressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId     string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	MaxFloorId int64  `protobuf:"varint,3,opt,name=maxFloorId,proto3" json:"maxFloorId,omitempty"` // maxFloorId
	EndTime    int64  `protobuf:"varint,4,opt,name=endTime,proto3" json:"endTime,omitempty"`       // 闯关结束时间
	From       string `protobuf:"bytes,5,opt,name=from,proto3" json:"from,omitempty"`              // 页面来源
}

func (x *AddProgressReq) Reset() {
	*x = AddProgressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddProgressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddProgressReq) ProtoMessage() {}

func (x *AddProgressReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddProgressReq.ProtoReflect.Descriptor instead.
func (*AddProgressReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDescGZIP(), []int{0}
}

func (x *AddProgressReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AddProgressReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *AddProgressReq) GetMaxFloorId() int64 {
	if x != nil {
		return x.MaxFloorId
	}
	return 0
}

func (x *AddProgressReq) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *AddProgressReq) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

type AddProgressRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddProgressRsp) Reset() {
	*x = AddProgressRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddProgressRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddProgressRsp) ProtoMessage() {}

func (x *AddProgressRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddProgressRsp.ProtoReflect.Descriptor instead.
func (*AddProgressRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDescGZIP(), []int{1}
}

type JoinStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *JoinStatusReq) Reset() {
	*x = JoinStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JoinStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinStatusReq) ProtoMessage() {}

func (x *JoinStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinStatusReq.ProtoReflect.Descriptor instead.
func (*JoinStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDescGZIP(), []int{2}
}

func (x *JoinStatusReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *JoinStatusReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type JoinStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Num     uint32                `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`
	Nodes   []*JoinStatusRsp_Node `protobuf:"bytes,2,rep,name=nodes,proto3" json:"nodes,omitempty"`      // 任务配置
	IsNewer uint32                `protobuf:"varint,3,opt,name=isNewer,proto3" json:"isNewer,omitempty"` // 是否是新手期
}

func (x *JoinStatusRsp) Reset() {
	*x = JoinStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JoinStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinStatusRsp) ProtoMessage() {}

func (x *JoinStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinStatusRsp.ProtoReflect.Descriptor instead.
func (*JoinStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDescGZIP(), []int{3}
}

func (x *JoinStatusRsp) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *JoinStatusRsp) GetNodes() []*JoinStatusRsp_Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *JoinStatusRsp) GetIsNewer() uint32 {
	if x != nil {
		return x.IsNewer
	}
	return 0
}

type InletData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RemainSec int64 `protobuf:"varint,1,opt,name=remainSec,proto3" json:"remainSec,omitempty"` // 倒计时
}

func (x *InletData) Reset() {
	*x = InletData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InletData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InletData) ProtoMessage() {}

func (x *InletData) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InletData.ProtoReflect.Descriptor instead.
func (*InletData) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDescGZIP(), []int{4}
}

func (x *InletData) GetRemainSec() int64 {
	if x != nil {
		return x.RemainSec
	}
	return 0
}

type JoinStatusRsp_Node struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cap       uint32            `protobuf:"varint,1,opt,name=cap,proto3" json:"cap,omitempty"`             // 领奖值
	Status    uint32            `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`       // 任务状态
	Package   *game_api.Package `protobuf:"bytes,3,opt,name=Package,proto3" json:"Package,omitempty"`      // 礼包
	TimeStamp uint32            `protobuf:"varint,4,opt,name=timeStamp,proto3" json:"timeStamp,omitempty"` // 领奖时间
	Num       uint32            `protobuf:"varint,5,opt,name=num,proto3" json:"num,omitempty"`
	CapNewer  uint32            `protobuf:"varint,6,opt,name=capNewer,proto3" json:"capNewer,omitempty"`
}

func (x *JoinStatusRsp_Node) Reset() {
	*x = JoinStatusRsp_Node{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JoinStatusRsp_Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinStatusRsp_Node) ProtoMessage() {}

func (x *JoinStatusRsp_Node) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinStatusRsp_Node.ProtoReflect.Descriptor instead.
func (*JoinStatusRsp_Node) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDescGZIP(), []int{3, 0}
}

func (x *JoinStatusRsp_Node) GetCap() uint32 {
	if x != nil {
		return x.Cap
	}
	return 0
}

func (x *JoinStatusRsp_Node) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *JoinStatusRsp_Node) GetPackage() *game_api.Package {
	if x != nil {
		return x.Package
	}
	return nil
}

func (x *JoinStatusRsp_Node) GetTimeStamp() uint32 {
	if x != nil {
		return x.TimeStamp
	}
	return 0
}

func (x *JoinStatusRsp_Node) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *JoinStatusRsp_Node) GetCapNewer() uint32 {
	if x != nil {
		return x.CapNewer
	}
	return 0
}

var File_pb_game_cwsx_daily_task_activity_server_server_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDesc = []byte{
	0x0a, 0x34, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x64,
	0x61, 0x69, 0x6c, 0x79, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x1a, 0x1a, 0x70, 0x62,
	0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2f, 0x69, 0x6e, 0x6c,
	0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8c, 0x01, 0x0a, 0x0e, 0x41, 0x64, 0x64,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x78,
	0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d,
	0x61, 0x78, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x22, 0x10, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x73, 0x70, 0x22, 0x3d, 0x0a, 0x0d, 0x4a, 0x6f, 0x69,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0xa7, 0x02, 0x0a, 0x0d, 0x4a, 0x6f, 0x69,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x3e, 0x0a, 0x05,
	0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x69, 0x73, 0x4e, 0x65, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x69,
	0x73, 0x4e, 0x65, 0x77, 0x65, 0x72, 0x1a, 0xa9, 0x01, 0x0a, 0x04, 0x4e, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x63, 0x61, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x63, 0x61,
	0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x07, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x07, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x4e, 0x65, 0x77,
	0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x61, 0x70, 0x4e, 0x65, 0x77,
	0x65, 0x72, 0x22, 0x29, 0x0a, 0x09, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x63, 0x32, 0xc9, 0x02,
	0x0a, 0x06, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x0d, 0x52, 0x65, 0x74, 0x65,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x12, 0x1b, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f,
	0x70, 0x75, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70,
	0x52, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x0d, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e,
	0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x56, 0x0a, 0x0a, 0x4a, 0x6f, 0x69, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x4a, 0x6f, 0x69, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x59,
	0x0a, 0x0b, 0x41, 0x64, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x24, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x73, 0x70, 0x42, 0x5c, 0x5a, 0x5a, 0x74, 0x63, 0x6f,
	0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f,
	0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x64, 0x61, 0x69,
	0x6c, 0x79, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDescOnce sync.Once
	file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDescData = file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDesc
)

func file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDescGZIP() []byte {
	file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDescOnce.Do(func() {
		file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDescData)
	})
	return file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDescData
}

var file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pb_game_cwsx_daily_task_activity_server_server_proto_goTypes = []interface{}{
	(*AddProgressReq)(nil),             // 0: game_cwsx_daily_task.AddProgressReq
	(*AddProgressRsp)(nil),             // 1: game_cwsx_daily_task.AddProgressRsp
	(*JoinStatusReq)(nil),              // 2: game_cwsx_daily_task.JoinStatusReq
	(*JoinStatusRsp)(nil),              // 3: game_cwsx_daily_task.JoinStatusRsp
	(*InletData)(nil),                  // 4: game_cwsx_daily_task.InletData
	(*JoinStatusRsp_Node)(nil),         // 5: game_cwsx_daily_task.JoinStatusRsp.Node
	(*game_api.Package)(nil),           // 6: game_api.Package
	(*game_api.RetentionPopupReq)(nil), // 7: game_api.RetentionPopupReq
	(*inlet.ActivityStateReq)(nil),     // 8: inlet.ActivityStateReq
	(*game_api.RetentionPopupRsp)(nil), // 9: game_api.RetentionPopupRsp
	(*inlet.ActivityStateRsp)(nil),     // 10: inlet.ActivityStateRsp
}
var file_pb_game_cwsx_daily_task_activity_server_server_proto_depIdxs = []int32{
	5,  // 0: game_cwsx_daily_task.JoinStatusRsp.nodes:type_name -> game_cwsx_daily_task.JoinStatusRsp.Node
	6,  // 1: game_cwsx_daily_task.JoinStatusRsp.Node.Package:type_name -> game_api.Package
	7,  // 2: game_cwsx_daily_task.Server.RetetionPopup:input_type -> game_api.RetentionPopupReq
	8,  // 3: game_cwsx_daily_task.Server.ActivityState:input_type -> inlet.ActivityStateReq
	2,  // 4: game_cwsx_daily_task.Server.JoinStatus:input_type -> game_cwsx_daily_task.JoinStatusReq
	0,  // 5: game_cwsx_daily_task.Server.AddProgress:input_type -> game_cwsx_daily_task.AddProgressReq
	9,  // 6: game_cwsx_daily_task.Server.RetetionPopup:output_type -> game_api.RetentionPopupRsp
	10, // 7: game_cwsx_daily_task.Server.ActivityState:output_type -> inlet.ActivityStateRsp
	3,  // 8: game_cwsx_daily_task.Server.JoinStatus:output_type -> game_cwsx_daily_task.JoinStatusRsp
	1,  // 9: game_cwsx_daily_task.Server.AddProgress:output_type -> game_cwsx_daily_task.AddProgressRsp
	6,  // [6:10] is the sub-list for method output_type
	2,  // [2:6] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_daily_task_activity_server_server_proto_init() }
func file_pb_game_cwsx_daily_task_activity_server_server_proto_init() {
	if File_pb_game_cwsx_daily_task_activity_server_server_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddProgressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddProgressRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JoinStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JoinStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InletData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JoinStatusRsp_Node); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_cwsx_daily_task_activity_server_server_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_daily_task_activity_server_server_proto_depIdxs,
		MessageInfos:      file_pb_game_cwsx_daily_task_activity_server_server_proto_msgTypes,
	}.Build()
	File_pb_game_cwsx_daily_task_activity_server_server_proto = out.File
	file_pb_game_cwsx_daily_task_activity_server_server_proto_rawDesc = nil
	file_pb_game_cwsx_daily_task_activity_server_server_proto_goTypes = nil
	file_pb_game_cwsx_daily_task_activity_server_server_proto_depIdxs = nil
}
