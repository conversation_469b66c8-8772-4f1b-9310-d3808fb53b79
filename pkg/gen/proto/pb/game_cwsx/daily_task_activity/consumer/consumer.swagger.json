{"swagger": "2.0", "info": {"title": "pb/game_cwsx/daily_task_activity/consumer/consumer.proto", "version": "version not set"}, "tags": [{"name": "Consumer"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game_cwsx_daily_task.Consumer/Consume": {"post": {"operationId": "Consumer_Consume", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_cwsx_daily_taskConsumeRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_cwsx_daily_taskConsumeReq"}}], "tags": ["Consumer"]}}}, "definitions": {"game_cwsx_daily_taskConsumeReq": {"type": "object", "properties": {"payload": {"type": "string", "format": "byte"}}}, "game_cwsx_daily_taskConsumeRsp": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}