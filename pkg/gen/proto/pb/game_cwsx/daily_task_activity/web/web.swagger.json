{"swagger": "2.0", "info": {"title": "pb/game_cwsx/daily_task_activity/web/web.proto", "version": "version not set"}, "tags": [{"name": "Web"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game_cwsx_daily_task.Web/QueryTask": {"post": {"summary": "查询任务", "operationId": "Web_QueryTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_cwsx_daily_taskQueryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_cwsx_daily_taskQueryReq"}}], "tags": ["Web"]}}, "/game_cwsx_daily_task.Web/QueryTip": {"post": {"summary": "查询状态:查询可以领取多少金币", "operationId": "Web_QueryTip", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_cwsx_daily_taskQueryTipRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_cwsx_daily_taskQueryTipReq"}}], "tags": ["Web"]}}}, "definitions": {"game_cwsx_daily_taskNode": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "节点id"}, "icon": {"type": "string", "title": "图标"}, "title": {"type": "string", "title": "文案"}, "status": {"$ref": "#/definitions/game_cwsx_daily_taskStatus", "title": "状态"}, "num": {"type": "integer", "format": "int64", "title": "数量"}}, "title": "节点信息"}, "game_cwsx_daily_taskPlayer": {"type": "object", "properties": {"avatar": {"type": "string", "title": "头像"}, "nick": {"type": "string", "title": "昵称"}, "ts": {"type": "integer", "format": "int64", "title": "达成时间"}, "num": {"type": "integer", "format": "int64", "title": "数量"}}}, "game_cwsx_daily_taskQueryReq": {"type": "object"}, "game_cwsx_daily_taskQueryRsp": {"type": "object", "properties": {"nodes": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_cwsx_daily_taskNode"}, "title": "任务列表"}, "remainSec": {"type": "integer", "format": "int64", "title": "倒计时"}, "luckys": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_cwsx_daily_taskPlayer"}, "title": "达成10000金币的"}, "initFloor": {"type": "integer", "format": "int64", "title": "今日首次闯关时的关卡数"}, "curPlayed": {"type": "integer", "format": "int64", "title": "已完成的闯关数"}, "totalCap": {"type": "integer", "format": "int64", "title": "今日可闯关总数"}, "introLink": {"type": "string", "title": "规则介绍"}}}, "game_cwsx_daily_taskQueryTipReq": {"type": "object", "properties": {"scene": {"type": "string", "format": "int64", "title": "TipScene"}}}, "game_cwsx_daily_taskQueryTipRsp": {"type": "object", "properties": {"tip": {"type": "string", "title": "文案"}, "num": {"type": "integer", "format": "int64", "title": "数量"}, "show": {"type": "integer", "format": "int64", "title": "非0展示"}, "isMax": {"type": "integer", "format": "int64", "title": "非0时表示最高可以拿到num"}, "real": {"type": "integer", "format": "int64", "title": "实际得到的数值"}, "actionType": {"type": "integer", "format": "int64", "title": "前端的承接逻辑 ActionType"}}}, "game_cwsx_daily_taskStatus": {"type": "object", "properties": {"status": {"type": "integer", "format": "int64", "title": "状态NodeStatus"}, "pkgId": {"type": "integer", "format": "int64", "title": "礼包Id"}, "num": {"type": "integer", "format": "int64", "title": "已下发的数量"}}, "title": "节点状态"}, "googlerpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}}}