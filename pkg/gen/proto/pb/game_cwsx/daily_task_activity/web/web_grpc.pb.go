// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_cwsx/daily_task_activity/web/web.proto

package web

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Web_QueryTask_FullMethodName = "/game_cwsx_daily_task.Web/QueryTask"
	Web_QueryTip_FullMethodName  = "/game_cwsx_daily_task.Web/QueryTip"
)

// WebClient is the client API for Web service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebClient interface {
	// 查询任务
	QueryTask(ctx context.Context, in *QueryReq, opts ...grpc.CallOption) (*QueryRsp, error)
	// 查询状态:查询可以领取多少金币
	QueryTip(ctx context.Context, in *QueryTipReq, opts ...grpc.CallOption) (*QueryTipRsp, error)
}

type webClient struct {
	cc grpc.ClientConnInterface
}

func NewWebClient(cc grpc.ClientConnInterface) WebClient {
	return &webClient{cc}
}

func (c *webClient) QueryTask(ctx context.Context, in *QueryReq, opts ...grpc.CallOption) (*QueryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRsp)
	err := c.cc.Invoke(ctx, Web_QueryTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webClient) QueryTip(ctx context.Context, in *QueryTipReq, opts ...grpc.CallOption) (*QueryTipRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryTipRsp)
	err := c.cc.Invoke(ctx, Web_QueryTip_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebServer is the server API for Web service.
// All implementations should embed UnimplementedWebServer
// for forward compatibility
type WebServer interface {
	// 查询任务
	QueryTask(context.Context, *QueryReq) (*QueryRsp, error)
	// 查询状态:查询可以领取多少金币
	QueryTip(context.Context, *QueryTipReq) (*QueryTipRsp, error)
}

// UnimplementedWebServer should be embedded to have forward compatible implementations.
type UnimplementedWebServer struct {
}

func (UnimplementedWebServer) QueryTask(context.Context, *QueryReq) (*QueryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryTask not implemented")
}
func (UnimplementedWebServer) QueryTip(context.Context, *QueryTipReq) (*QueryTipRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryTip not implemented")
}

// UnsafeWebServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebServer will
// result in compilation errors.
type UnsafeWebServer interface {
	mustEmbedUnimplementedWebServer()
}

func RegisterWebServer(s grpc.ServiceRegistrar, srv WebServer) {
	s.RegisterService(&Web_ServiceDesc, srv)
}

func _Web_QueryTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebServer).QueryTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Web_QueryTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebServer).QueryTask(ctx, req.(*QueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Web_QueryTip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryTipReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebServer).QueryTip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Web_QueryTip_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebServer).QueryTip(ctx, req.(*QueryTipReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Web_ServiceDesc is the grpc.ServiceDesc for Web service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Web_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game_cwsx_daily_task.Web",
	HandlerType: (*WebServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryTask",
			Handler:    _Web_QueryTask_Handler,
		},
		{
			MethodName: "QueryTip",
			Handler:    _Web_QueryTip_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_cwsx/daily_task_activity/web/web.proto",
}
