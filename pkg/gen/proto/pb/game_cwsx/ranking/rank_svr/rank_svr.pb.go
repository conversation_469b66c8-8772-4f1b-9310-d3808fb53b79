// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/ranking/rank_svr/rank_svr.proto

package rank_svr

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/ranking/common"
	webapi "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/ranking/webapi"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChangeBindingSvrReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId             string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId            string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	OldCityCode       string `protobuf:"bytes,3,opt,name=oldCityCode,proto3" json:"oldCityCode,omitempty"`
	NewCityCode       string `protobuf:"bytes,4,opt,name=newCityCode,proto3" json:"newCityCode,omitempty"`
	MaxFloor          uint64 `protobuf:"varint,5,opt,name=maxFloor,proto3" json:"maxFloor,omitempty"`
	OldUniformRankKey string `protobuf:"bytes,6,opt,name=oldUniformRankKey,proto3" json:"oldUniformRankKey,omitempty"`
	OldScore          uint64 `protobuf:"varint,7,opt,name=oldScore,proto3" json:"oldScore,omitempty"`
}

func (x *ChangeBindingSvrReq) Reset() {
	*x = ChangeBindingSvrReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeBindingSvrReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeBindingSvrReq) ProtoMessage() {}

func (x *ChangeBindingSvrReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeBindingSvrReq.ProtoReflect.Descriptor instead.
func (*ChangeBindingSvrReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDescGZIP(), []int{0}
}

func (x *ChangeBindingSvrReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ChangeBindingSvrReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ChangeBindingSvrReq) GetOldCityCode() string {
	if x != nil {
		return x.OldCityCode
	}
	return ""
}

func (x *ChangeBindingSvrReq) GetNewCityCode() string {
	if x != nil {
		return x.NewCityCode
	}
	return ""
}

func (x *ChangeBindingSvrReq) GetMaxFloor() uint64 {
	if x != nil {
		return x.MaxFloor
	}
	return 0
}

func (x *ChangeBindingSvrReq) GetOldUniformRankKey() string {
	if x != nil {
		return x.OldUniformRankKey
	}
	return ""
}

func (x *ChangeBindingSvrReq) GetOldScore() uint64 {
	if x != nil {
		return x.OldScore
	}
	return 0
}

type ChangeBindingSvrRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ChangeBindingSvrRsp) Reset() {
	*x = ChangeBindingSvrRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeBindingSvrRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeBindingSvrRsp) ProtoMessage() {}

func (x *ChangeBindingSvrRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeBindingSvrRsp.ProtoReflect.Descriptor instead.
func (*ChangeBindingSvrRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDescGZIP(), []int{1}
}

type GmChangeBindingSvrReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId       string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId      string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	NewCityCode string `protobuf:"bytes,3,opt,name=newCityCode,proto3" json:"newCityCode,omitempty"` // 需要强制转换得地区
}

func (x *GmChangeBindingSvrReq) Reset() {
	*x = GmChangeBindingSvrReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmChangeBindingSvrReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmChangeBindingSvrReq) ProtoMessage() {}

func (x *GmChangeBindingSvrReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmChangeBindingSvrReq.ProtoReflect.Descriptor instead.
func (*GmChangeBindingSvrReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDescGZIP(), []int{2}
}

func (x *GmChangeBindingSvrReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GmChangeBindingSvrReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *GmChangeBindingSvrReq) GetNewCityCode() string {
	if x != nil {
		return x.NewCityCode
	}
	return ""
}

type GmChangeBindingSvrRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OldCityName string `protobuf:"bytes,1,opt,name=oldCityName,proto3" json:"oldCityName,omitempty"` // 更换前城市
	NewCityName string `protobuf:"bytes,2,opt,name=newCityName,proto3" json:"newCityName,omitempty"` // 更换后城市
}

func (x *GmChangeBindingSvrRsp) Reset() {
	*x = GmChangeBindingSvrRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmChangeBindingSvrRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmChangeBindingSvrRsp) ProtoMessage() {}

func (x *GmChangeBindingSvrRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmChangeBindingSvrRsp.ProtoReflect.Descriptor instead.
func (*GmChangeBindingSvrRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDescGZIP(), []int{3}
}

func (x *GmChangeBindingSvrRsp) GetOldCityName() string {
	if x != nil {
		return x.OldCityName
	}
	return ""
}

func (x *GmChangeBindingSvrRsp) GetNewCityName() string {
	if x != nil {
		return x.NewCityName
	}
	return ""
}

type GmQueryConfigSvrReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GmQueryConfigSvrReq) Reset() {
	*x = GmQueryConfigSvrReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmQueryConfigSvrReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmQueryConfigSvrReq) ProtoMessage() {}

func (x *GmQueryConfigSvrReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmQueryConfigSvrReq.ProtoReflect.Descriptor instead.
func (*GmQueryConfigSvrReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDescGZIP(), []int{4}
}

type GmQueryConfigSvrRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Regions []*common.GeoRegion `protobuf:"bytes,1,rep,name=regions,proto3" json:"regions,omitempty"`
}

func (x *GmQueryConfigSvrRsp) Reset() {
	*x = GmQueryConfigSvrRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmQueryConfigSvrRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmQueryConfigSvrRsp) ProtoMessage() {}

func (x *GmQueryConfigSvrRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmQueryConfigSvrRsp.ProtoReflect.Descriptor instead.
func (*GmQueryConfigSvrRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDescGZIP(), []int{5}
}

func (x *GmQueryConfigSvrRsp) GetRegions() []*common.GeoRegion {
	if x != nil {
		return x.Regions
	}
	return nil
}

var File_pb_game_cwsx_ranking_rank_svr_rank_svr_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x72,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x73, 0x76, 0x72, 0x2f,
	0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x1a, 0x28, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f,
	0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x77, 0x65, 0x62, 0x61, 0x70, 0x69, 0x2f, 0x77,
	0x65, 0x62, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x70, 0x62, 0x2f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xed, 0x01, 0x0a, 0x13, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x76, 0x72, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6f,
	0x6c, 0x64, 0x43, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x6f, 0x6c, 0x64, 0x43, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x6e, 0x65, 0x77, 0x43, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65, 0x77, 0x43, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x2c, 0x0a, 0x11, 0x6f,
	0x6c, 0x64, 0x55, 0x6e, 0x69, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x4b, 0x65, 0x79,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x6c, 0x64, 0x55, 0x6e, 0x69, 0x66, 0x6f,
	0x72, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x6c, 0x64,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x6f, 0x6c, 0x64,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x15, 0x0a, 0x13, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x76, 0x72, 0x52, 0x73, 0x70, 0x22, 0x67, 0x0a, 0x15,
	0x47, 0x6d, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x76, 0x72, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x65, 0x77, 0x43, 0x69, 0x74, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65, 0x77, 0x43, 0x69, 0x74,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x5b, 0x0a, 0x15, 0x47, 0x6d, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x76, 0x72, 0x52, 0x73, 0x70, 0x12, 0x20,
	0x0a, 0x0b, 0x6f, 0x6c, 0x64, 0x43, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x6c, 0x64, 0x43, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x65, 0x77, 0x43, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65, 0x77, 0x43, 0x69, 0x74, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x15, 0x0a, 0x13, 0x47, 0x6d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x53, 0x76, 0x72, 0x52, 0x65, 0x71, 0x22, 0x4d, 0x0a, 0x13, 0x47, 0x6d, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x76, 0x72, 0x52, 0x73, 0x70,
	0x12, 0x36, 0x0a, 0x07, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x72, 0x61,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52,
	0x07, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x32, 0xcc, 0x04, 0x0a, 0x0a, 0x52, 0x61, 0x6e,
	0x6b, 0x69, 0x6e, 0x67, 0x53, 0x76, 0x72, 0x12, 0x59, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x76, 0x72, 0x12, 0x22, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x22,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x73, 0x70, 0x12, 0x56, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x53, 0x76, 0x72, 0x12, 0x21, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x12, 0x59, 0x0a, 0x0f, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x76, 0x72, 0x12, 0x22, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x1a, 0x22, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x72, 0x61,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x52, 0x73, 0x70, 0x12, 0x62, 0x0a, 0x10, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x76, 0x72, 0x12, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x76, 0x72, 0x52, 0x65,
	0x71, 0x1a, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x72, 0x61,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x76, 0x72, 0x52, 0x73, 0x70, 0x12, 0x68, 0x0a, 0x12, 0x47, 0x6d, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x76, 0x72, 0x12,
	0x28, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x72, 0x61, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x6d, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x76, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x6d,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x76, 0x72,
	0x52, 0x73, 0x70, 0x12, 0x62, 0x0a, 0x10, 0x47, 0x6d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x53, 0x76, 0x72, 0x12, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x6d, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x76, 0x72, 0x52, 0x65, 0x71, 0x1a,
	0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x72, 0x61, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x6d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x53, 0x76, 0x72, 0x52, 0x73, 0x70, 0x42, 0x52, 0x5a, 0x50, 0x74, 0x63, 0x6f, 0x64, 0x65,
	0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62,
	0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x73, 0x76, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDescOnce sync.Once
	file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDescData = file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDesc
)

func file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDescGZIP() []byte {
	file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDescOnce.Do(func() {
		file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDescData)
	})
	return file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDescData
}

var file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_goTypes = []interface{}{
	(*ChangeBindingSvrReq)(nil),    // 0: game_cwsx_ranking.ChangeBindingSvrReq
	(*ChangeBindingSvrRsp)(nil),    // 1: game_cwsx_ranking.ChangeBindingSvrRsp
	(*GmChangeBindingSvrReq)(nil),  // 2: game_cwsx_ranking.GmChangeBindingSvrReq
	(*GmChangeBindingSvrRsp)(nil),  // 3: game_cwsx_ranking.GmChangeBindingSvrRsp
	(*GmQueryConfigSvrReq)(nil),    // 4: game_cwsx_ranking.GmQueryConfigSvrReq
	(*GmQueryConfigSvrRsp)(nil),    // 5: game_cwsx_ranking.GmQueryConfigSvrRsp
	(*common.GeoRegion)(nil),       // 6: game_cwsx_ranking.GeoRegion
	(*webapi.QueryRankingReq)(nil), // 7: game_cwsx_ranking.QueryRankingReq
	(*webapi.QueryConfigReq)(nil),  // 8: game_cwsx_ranking.QueryConfigReq
	(*webapi.QueryBindingReq)(nil), // 9: game_cwsx_ranking.QueryBindingReq
	(*webapi.QueryRankingRsp)(nil), // 10: game_cwsx_ranking.QueryRankingRsp
	(*webapi.QueryConfigRsp)(nil),  // 11: game_cwsx_ranking.QueryConfigRsp
	(*webapi.QueryBindingRsp)(nil), // 12: game_cwsx_ranking.QueryBindingRsp
}
var file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_depIdxs = []int32{
	6,  // 0: game_cwsx_ranking.GmQueryConfigSvrRsp.regions:type_name -> game_cwsx_ranking.GeoRegion
	7,  // 1: game_cwsx_ranking.RankingSvr.QueryRankingSvr:input_type -> game_cwsx_ranking.QueryRankingReq
	8,  // 2: game_cwsx_ranking.RankingSvr.QueryConfigSvr:input_type -> game_cwsx_ranking.QueryConfigReq
	9,  // 3: game_cwsx_ranking.RankingSvr.QueryBindingSvr:input_type -> game_cwsx_ranking.QueryBindingReq
	0,  // 4: game_cwsx_ranking.RankingSvr.ChangeBindingSvr:input_type -> game_cwsx_ranking.ChangeBindingSvrReq
	2,  // 5: game_cwsx_ranking.RankingSvr.GmChangeBindingSvr:input_type -> game_cwsx_ranking.GmChangeBindingSvrReq
	4,  // 6: game_cwsx_ranking.RankingSvr.GmQueryConfigSvr:input_type -> game_cwsx_ranking.GmQueryConfigSvrReq
	10, // 7: game_cwsx_ranking.RankingSvr.QueryRankingSvr:output_type -> game_cwsx_ranking.QueryRankingRsp
	11, // 8: game_cwsx_ranking.RankingSvr.QueryConfigSvr:output_type -> game_cwsx_ranking.QueryConfigRsp
	12, // 9: game_cwsx_ranking.RankingSvr.QueryBindingSvr:output_type -> game_cwsx_ranking.QueryBindingRsp
	1,  // 10: game_cwsx_ranking.RankingSvr.ChangeBindingSvr:output_type -> game_cwsx_ranking.ChangeBindingSvrRsp
	3,  // 11: game_cwsx_ranking.RankingSvr.GmChangeBindingSvr:output_type -> game_cwsx_ranking.GmChangeBindingSvrRsp
	5,  // 12: game_cwsx_ranking.RankingSvr.GmQueryConfigSvr:output_type -> game_cwsx_ranking.GmQueryConfigSvrRsp
	7,  // [7:13] is the sub-list for method output_type
	1,  // [1:7] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_init() }
func file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_init() {
	if File_pb_game_cwsx_ranking_rank_svr_rank_svr_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeBindingSvrReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeBindingSvrRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmChangeBindingSvrReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmChangeBindingSvrRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmQueryConfigSvrReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmQueryConfigSvrRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_depIdxs,
		MessageInfos:      file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_msgTypes,
	}.Build()
	File_pb_game_cwsx_ranking_rank_svr_rank_svr_proto = out.File
	file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_rawDesc = nil
	file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_goTypes = nil
	file_pb_game_cwsx_ranking_rank_svr_rank_svr_proto_depIdxs = nil
}
