// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_cwsx/ranking/rank_svr/rank_svr.proto

package rank_svr

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	webapi "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/ranking/webapi"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	RankingSvr_QueryRankingSvr_FullMethodName    = "/game_cwsx_ranking.RankingSvr/QueryRankingSvr"
	RankingSvr_QueryConfigSvr_FullMethodName     = "/game_cwsx_ranking.RankingSvr/QueryConfigSvr"
	RankingSvr_QueryBindingSvr_FullMethodName    = "/game_cwsx_ranking.RankingSvr/QueryBindingSvr"
	RankingSvr_ChangeBindingSvr_FullMethodName   = "/game_cwsx_ranking.RankingSvr/ChangeBindingSvr"
	RankingSvr_GmChangeBindingSvr_FullMethodName = "/game_cwsx_ranking.RankingSvr/GmChangeBindingSvr"
	RankingSvr_GmQueryConfigSvr_FullMethodName   = "/game_cwsx_ranking.RankingSvr/GmQueryConfigSvr"
)

// RankingSvrClient is the client API for RankingSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RankingSvrClient interface {
	// 查询榜单 --不做绑定操作
	QueryRankingSvr(ctx context.Context, in *webapi.QueryRankingReq, opts ...grpc.CallOption) (*webapi.QueryRankingRsp, error)
	// 查询配置 --不做绑定操作
	QueryConfigSvr(ctx context.Context, in *webapi.QueryConfigReq, opts ...grpc.CallOption) (*webapi.QueryConfigRsp, error)
	// 查询用户的城市绑定信息 --不传ip不做绑定操作,传ip做绑定操作
	QueryBindingSvr(ctx context.Context, in *webapi.QueryBindingReq, opts ...grpc.CallOption) (*webapi.QueryBindingRsp, error)
	// 地区绑定切换
	ChangeBindingSvr(ctx context.Context, in *ChangeBindingSvrReq, opts ...grpc.CallOption) (*ChangeBindingSvrRsp, error)
	// gm强制更换地区
	GmChangeBindingSvr(ctx context.Context, in *GmChangeBindingSvrReq, opts ...grpc.CallOption) (*GmChangeBindingSvrRsp, error)
	// gm拉取地区列表
	GmQueryConfigSvr(ctx context.Context, in *GmQueryConfigSvrReq, opts ...grpc.CallOption) (*GmQueryConfigSvrRsp, error)
}

type rankingSvrClient struct {
	cc grpc.ClientConnInterface
}

func NewRankingSvrClient(cc grpc.ClientConnInterface) RankingSvrClient {
	return &rankingSvrClient{cc}
}

func (c *rankingSvrClient) QueryRankingSvr(ctx context.Context, in *webapi.QueryRankingReq, opts ...grpc.CallOption) (*webapi.QueryRankingRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(webapi.QueryRankingRsp)
	err := c.cc.Invoke(ctx, RankingSvr_QueryRankingSvr_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankingSvrClient) QueryConfigSvr(ctx context.Context, in *webapi.QueryConfigReq, opts ...grpc.CallOption) (*webapi.QueryConfigRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(webapi.QueryConfigRsp)
	err := c.cc.Invoke(ctx, RankingSvr_QueryConfigSvr_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankingSvrClient) QueryBindingSvr(ctx context.Context, in *webapi.QueryBindingReq, opts ...grpc.CallOption) (*webapi.QueryBindingRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(webapi.QueryBindingRsp)
	err := c.cc.Invoke(ctx, RankingSvr_QueryBindingSvr_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankingSvrClient) ChangeBindingSvr(ctx context.Context, in *ChangeBindingSvrReq, opts ...grpc.CallOption) (*ChangeBindingSvrRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChangeBindingSvrRsp)
	err := c.cc.Invoke(ctx, RankingSvr_ChangeBindingSvr_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankingSvrClient) GmChangeBindingSvr(ctx context.Context, in *GmChangeBindingSvrReq, opts ...grpc.CallOption) (*GmChangeBindingSvrRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GmChangeBindingSvrRsp)
	err := c.cc.Invoke(ctx, RankingSvr_GmChangeBindingSvr_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankingSvrClient) GmQueryConfigSvr(ctx context.Context, in *GmQueryConfigSvrReq, opts ...grpc.CallOption) (*GmQueryConfigSvrRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GmQueryConfigSvrRsp)
	err := c.cc.Invoke(ctx, RankingSvr_GmQueryConfigSvr_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RankingSvrServer is the server API for RankingSvr service.
// All implementations should embed UnimplementedRankingSvrServer
// for forward compatibility
type RankingSvrServer interface {
	// 查询榜单 --不做绑定操作
	QueryRankingSvr(context.Context, *webapi.QueryRankingReq) (*webapi.QueryRankingRsp, error)
	// 查询配置 --不做绑定操作
	QueryConfigSvr(context.Context, *webapi.QueryConfigReq) (*webapi.QueryConfigRsp, error)
	// 查询用户的城市绑定信息 --不传ip不做绑定操作,传ip做绑定操作
	QueryBindingSvr(context.Context, *webapi.QueryBindingReq) (*webapi.QueryBindingRsp, error)
	// 地区绑定切换
	ChangeBindingSvr(context.Context, *ChangeBindingSvrReq) (*ChangeBindingSvrRsp, error)
	// gm强制更换地区
	GmChangeBindingSvr(context.Context, *GmChangeBindingSvrReq) (*GmChangeBindingSvrRsp, error)
	// gm拉取地区列表
	GmQueryConfigSvr(context.Context, *GmQueryConfigSvrReq) (*GmQueryConfigSvrRsp, error)
}

// UnimplementedRankingSvrServer should be embedded to have forward compatible implementations.
type UnimplementedRankingSvrServer struct {
}

func (UnimplementedRankingSvrServer) QueryRankingSvr(context.Context, *webapi.QueryRankingReq) (*webapi.QueryRankingRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRankingSvr not implemented")
}
func (UnimplementedRankingSvrServer) QueryConfigSvr(context.Context, *webapi.QueryConfigReq) (*webapi.QueryConfigRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryConfigSvr not implemented")
}
func (UnimplementedRankingSvrServer) QueryBindingSvr(context.Context, *webapi.QueryBindingReq) (*webapi.QueryBindingRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryBindingSvr not implemented")
}
func (UnimplementedRankingSvrServer) ChangeBindingSvr(context.Context, *ChangeBindingSvrReq) (*ChangeBindingSvrRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeBindingSvr not implemented")
}
func (UnimplementedRankingSvrServer) GmChangeBindingSvr(context.Context, *GmChangeBindingSvrReq) (*GmChangeBindingSvrRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GmChangeBindingSvr not implemented")
}
func (UnimplementedRankingSvrServer) GmQueryConfigSvr(context.Context, *GmQueryConfigSvrReq) (*GmQueryConfigSvrRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GmQueryConfigSvr not implemented")
}

// UnsafeRankingSvrServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RankingSvrServer will
// result in compilation errors.
type UnsafeRankingSvrServer interface {
	mustEmbedUnimplementedRankingSvrServer()
}

func RegisterRankingSvrServer(s grpc.ServiceRegistrar, srv RankingSvrServer) {
	s.RegisterService(&RankingSvr_ServiceDesc, srv)
}

func _RankingSvr_QueryRankingSvr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(webapi.QueryRankingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankingSvrServer).QueryRankingSvr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankingSvr_QueryRankingSvr_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankingSvrServer).QueryRankingSvr(ctx, req.(*webapi.QueryRankingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankingSvr_QueryConfigSvr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(webapi.QueryConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankingSvrServer).QueryConfigSvr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankingSvr_QueryConfigSvr_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankingSvrServer).QueryConfigSvr(ctx, req.(*webapi.QueryConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankingSvr_QueryBindingSvr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(webapi.QueryBindingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankingSvrServer).QueryBindingSvr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankingSvr_QueryBindingSvr_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankingSvrServer).QueryBindingSvr(ctx, req.(*webapi.QueryBindingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankingSvr_ChangeBindingSvr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeBindingSvrReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankingSvrServer).ChangeBindingSvr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankingSvr_ChangeBindingSvr_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankingSvrServer).ChangeBindingSvr(ctx, req.(*ChangeBindingSvrReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankingSvr_GmChangeBindingSvr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GmChangeBindingSvrReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankingSvrServer).GmChangeBindingSvr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankingSvr_GmChangeBindingSvr_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankingSvrServer).GmChangeBindingSvr(ctx, req.(*GmChangeBindingSvrReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankingSvr_GmQueryConfigSvr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GmQueryConfigSvrReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankingSvrServer).GmQueryConfigSvr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankingSvr_GmQueryConfigSvr_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankingSvrServer).GmQueryConfigSvr(ctx, req.(*GmQueryConfigSvrReq))
	}
	return interceptor(ctx, in, info, handler)
}

// RankingSvr_ServiceDesc is the grpc.ServiceDesc for RankingSvr service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RankingSvr_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game_cwsx_ranking.RankingSvr",
	HandlerType: (*RankingSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryRankingSvr",
			Handler:    _RankingSvr_QueryRankingSvr_Handler,
		},
		{
			MethodName: "QueryConfigSvr",
			Handler:    _RankingSvr_QueryConfigSvr_Handler,
		},
		{
			MethodName: "QueryBindingSvr",
			Handler:    _RankingSvr_QueryBindingSvr_Handler,
		},
		{
			MethodName: "ChangeBindingSvr",
			Handler:    _RankingSvr_ChangeBindingSvr_Handler,
		},
		{
			MethodName: "GmChangeBindingSvr",
			Handler:    _RankingSvr_GmChangeBindingSvr_Handler,
		},
		{
			MethodName: "GmQueryConfigSvr",
			Handler:    _RankingSvr_GmQueryConfigSvr_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_cwsx/ranking/rank_svr/rank_svr.proto",
}
