{"swagger": "2.0", "info": {"title": "pb/game_cwsx/ranking/webapi/webapi.proto", "version": "version not set"}, "tags": [{"name": "WebApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game_cwsx_ranking.WebApi/Claim": {"post": {"summary": "领奖", "operationId": "WebApi_Claim", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_cwsx_rankingClaimRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_cwsx_rankingClaimReq"}}], "tags": ["WebApi"]}}, "/game_cwsx_ranking.WebApi/FlushBinding": {"post": {"summary": "刷新绑定", "operationId": "WebApi_FlushBinding", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_cwsx_rankingFlushBindingRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_cwsx_rankingFlushBindingReq"}}], "tags": ["WebApi"]}}, "/game_cwsx_ranking.WebApi/PopupState": {"post": {"summary": "弹窗查询", "operationId": "WebApi_PopupState", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/inletActivityStateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/inletActivityStateReq"}}], "tags": ["WebApi"]}}, "/game_cwsx_ranking.WebApi/QueryBinding": {"post": {"summary": "查询用户的城市绑定信息", "operationId": "WebApi_QueryBinding", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_cwsx_rankingQueryBindingRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_cwsx_rankingQueryBindingReq"}}], "tags": ["WebApi"]}}, "/game_cwsx_ranking.WebApi/QueryClaimStatus": {"post": {"summary": "查询领奖", "operationId": "WebApi_QueryClaimStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_cwsx_rankingQueryClaimStatusRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_cwsx_rankingQueryClaimStatusReq"}}], "tags": ["WebApi"]}}, "/game_cwsx_ranking.WebApi/QueryConfig": {"post": {"summary": "查询配置", "operationId": "WebApi_QueryConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_cwsx_rankingQueryConfigRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_cwsx_rankingQueryConfigReq"}}], "tags": ["WebApi"]}}, "/game_cwsx_ranking.WebApi/QueryRanking": {"post": {"summary": "查询榜单", "operationId": "WebApi_QueryRanking", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_cwsx_rankingQueryRankingRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_cwsx_rankingQueryRankingReq"}}], "tags": ["WebApi"]}}}, "definitions": {"game_apiRewardItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "奖励id"}, "num": {"type": "integer", "format": "int64", "title": "奖励数量 限时道具表示分钟数"}, "name": {"type": "string", "title": "奖励名称"}, "type": {"$ref": "#/definitions/game_apiRewardItemType", "title": "资产类型"}, "img": {"type": "string", "title": "图片"}, "desc": {"type": "string", "title": "描述问题呢"}}}, "game_apiRewardItemType": {"type": "string", "enum": ["Reward_From_GameNormal", "Reward_From_Platform", "Reward_From_GameLimitedTime"], "default": "Reward_From_GameNormal", "title": "- Reward_From_GameNormal: 游戏物品-默认\n - Reward_From_Platform: 平台物品\n - Reward_From_GameLimitedTime: 游戏物品-限时"}, "game_apiTreasureCard": {"type": "object", "properties": {"treasureCardId": {"type": "integer", "format": "int64", "title": "宝藏卡id"}, "isDecompose": {"type": "boolean", "title": "是否被分"}, "decomposeNum": {"type": "integer", "format": "int64", "title": "分解出多少碎片"}}, "title": "宝藏卡结构"}, "game_cwsx_rankingClaimReq": {"type": "object"}, "game_cwsx_rankingClaimRsp": {"type": "object", "properties": {"roundId": {"type": "string"}, "claimTime": {"type": "string", "format": "int64"}, "status": {"$ref": "#/definitions/game_cwsx_rankingClaimStatus"}, "changed": {"type": "integer", "format": "int32", "title": "如果为1,则表示本次请求发生了变更,后续重复操作则返回changed=0"}}}, "game_cwsx_rankingClaimStatus": {"type": "string", "enum": ["ClaimStatusNone", "ClaimStatusNoClaim", "ClaimStatusClaimed"], "default": "ClaimStatusNone", "title": "- ClaimStatusNone: 未参与\n - ClaimStatusNoClaim: 达不到名次, 不能获得奖励\n - ClaimStatusClaimed: 达到名次, 领取了奖励, 弹窗"}, "game_cwsx_rankingFlushBindingReq": {"type": "object"}, "game_cwsx_rankingFlushBindingRsp": {"type": "object", "properties": {"region": {"$ref": "#/definitions/game_cwsx_rankingRegion", "title": "用户绑定的地区信息"}}}, "game_cwsx_rankingGeoCity": {"type": "object", "properties": {"name": {"type": "string", "title": "地区名称"}, "code": {"type": "string", "title": "地区代码"}, "pyPrefix": {"type": "string", "title": "拼音首字母"}}}, "game_cwsx_rankingGeoRegion": {"type": "object", "properties": {"name": {"type": "string"}, "code": {"type": "string"}, "cities": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_cwsx_rankingGeoCity"}}}}, "game_cwsx_rankingPagenation": {"type": "object", "properties": {"passback": {"type": "string", "title": "分页参数"}, "hasMore": {"type": "boolean", "title": "是否还有更多"}}}, "game_cwsx_rankingQueryBindingReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "timestamp": {"type": "string", "format": "int64", "title": "不传默认用当前时间"}, "ip": {"type": "string", "title": "如果用户绑定过了,就用现有的,否则用这个ip走地区绑定流程"}}}, "game_cwsx_rankingQueryBindingRsp": {"type": "object", "properties": {"region": {"$ref": "#/definitions/game_cwsx_rankingRegion", "title": "用户绑定的地区信息"}}}, "game_cwsx_rankingQueryClaimStatusReq": {"type": "object", "properties": {"roundId": {"type": "string", "title": "指定轮次, 不指定就是当前周"}, "passback": {"type": "string", "title": "透传回包中的passback参数,第一次不用传"}, "timestamp": {"type": "string", "title": "优先判断roundId, 没有再通过timestamp, 兜底就是当前时间"}}}, "game_cwsx_rankingQueryClaimStatusRsp": {"type": "object", "properties": {"region": {"$ref": "#/definitions/game_cwsx_rankingRegion", "title": "上周参与的地区"}, "score": {"type": "integer", "format": "int64", "title": "上周获得的分数"}, "pagenation": {"$ref": "#/definitions/game_cwsx_rankingPagenation", "title": "分页信息"}, "ranks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_cwsx_rankingRankingItem"}, "title": "榜单信息"}, "status": {"$ref": "#/definitions/game_cwsx_rankingClaimStatus", "title": "获奖状态, 只有等于ClaimStatusClaimed时才会返回ranks"}, "roundId": {"type": "string", "title": "轮次id"}, "curUserRank": {"$ref": "#/definitions/game_cwsx_rankingRankingItem", "title": "当前用户的排名"}, "weeks": {"type": "integer", "format": "int64", "title": "第几周"}, "weekDate": {"type": "string", "title": "日期"}}}, "game_cwsx_rankingQueryConfigReq": {"type": "object", "properties": {"cacheVer": {"type": "string", "title": "前端回传本地缓存的版本号"}}}, "game_cwsx_rankingQueryConfigRsp": {"type": "object", "properties": {"regionVer": {"type": "string"}, "regions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_cwsx_rankingGeoRegion"}}, "curRegionCode": {"type": "string", "title": "当前用户所在的地区"}}, "title": "前端本地缓存回包中的地区列表regions以及regionVersion\n当地区映射未发生变化时,回包不会返回regions数据"}, "game_cwsx_rankingQueryRankingReq": {"type": "object", "properties": {"passback": {"type": "string", "title": "透传回包中的passback参数,第一次不用传"}, "reginCode": {"type": "string", "title": "地区码"}, "timestamp": {"type": "string", "format": "int64", "title": "时间戳"}}}, "game_cwsx_rankingQueryRankingRsp": {"type": "object", "properties": {"pagenation": {"$ref": "#/definitions/game_cwsx_rankingPagenation", "title": "分页信息"}, "ranks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_cwsx_rankingRankingItem"}, "title": "榜单信息"}, "remainSec": {"type": "integer", "format": "int64", "title": "剩余时间"}, "roundId": {"type": "string", "title": "轮次id"}, "curUserRank": {"$ref": "#/definitions/game_cwsx_rankingRankingItem", "title": "当前用户的排名"}, "region": {"$ref": "#/definitions/game_cwsx_rankingRegion", "title": "当前城市"}, "bindType": {"type": "integer", "format": "int64", "title": "ip绑定类型,0未绑定,1从profile取, 2从ip取, 3从兜底配置取"}, "strRule": {"type": "string", "title": "规则图"}}}, "game_cwsx_rankingRankingItem": {"type": "object", "properties": {"openId": {"type": "string", "title": "openId"}, "nick": {"type": "string", "title": "昵称"}, "avatar": {"type": "string", "title": "头像"}, "petIcon": {"type": "string", "title": "宠物头像"}, "teamName": {"type": "string", "title": "战队名"}, "rank": {"type": "integer", "format": "int64", "title": "排名"}, "score": {"type": "integer", "format": "int64", "title": "分数"}, "reward": {"$ref": "#/definitions/game_cwsx_rankingRewardPackage", "title": "奖励信息"}, "uid": {"type": "string", "title": "平台uid"}, "private": {"type": "integer", "format": "int64", "title": "是否屏蔽个人主页"}, "petId": {"type": "string", "format": "int64", "title": "宠物id"}, "encryptUid": {"type": "string"}, "privilege": {"type": "object", "additionalProperties": {"type": "string"}, "title": "特权信息"}, "danInfo": {"$ref": "#/definitions/xian_cwsx_danDanInfo", "title": "玩家段位"}}}, "game_cwsx_rankingRegion": {"type": "object", "properties": {"regionName": {"type": "string", "title": "地区名称"}, "regionCode": {"type": "string", "title": "地区代码"}}}, "game_cwsx_rankingRewardPackage": {"type": "object", "properties": {"cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_apiTreasureCard"}, "title": "宝藏卡信息, 如果产生宝藏卡领取, 则放在这里"}, "rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_apiRewardItem"}, "title": "奖励信息"}}}, "inletActivityStateReq": {"type": "object", "properties": {"number": {"$ref": "#/definitions/inletInletNumber", "title": "入口活动编号=>InletNumber"}, "openId": {"type": "string", "title": "用户的open_id"}, "os": {"$ref": "#/definitions/inletPlatformType", "title": "设备类型"}, "sceneId": {"type": "integer", "format": "int32", "title": "场景ID"}, "userAgent": {"type": "string", "title": "ua"}, "appId": {"type": "string", "title": "appid"}, "needData": {"$ref": "#/definitions/inletNeedDataType", "title": "需要具体数据"}, "floor": {"type": "string", "format": "int64", "title": "玩家当前关卡"}, "cacheBindKey": {"type": "string", "title": "卢学彦 【荣耀奖牌赛】"}, "teamId": {"type": "string", "title": "战队ID"}}, "title": "ActivityStateReq 查询活动状态请求"}, "inletActivityStateRsp": {"type": "object", "properties": {"inletStatus": {"$ref": "#/definitions/inletInletStatusType", "title": "入口状态 【1.0版本弃用】"}, "nextReqTime": {"type": "string", "format": "int64", "title": "下次请求时间  -1永久保持历史状态"}, "startTime": {"type": "string", "format": "int64", "title": "活动开始时间"}, "endTime": {"type": "string", "format": "int64", "title": "活动结束时间"}, "data": {"type": "string", "format": "byte", "title": "当need_data=1时，返回具体活动的数据。"}, "round": {"type": "string", "title": "轮次 刷新记录使用"}, "buyStatus": {"$ref": "#/definitions/inletBuyType", "title": "购买状态"}, "showType": {"$ref": "#/definitions/inletManageShowType", "description": "入口展示状态【决定是否入口展示】:在nextReqTime过期后重新加载", "title": "-- 入口展示条件，下面的值拥有后，忽略上面的【 InletStatusType inlet_status = 1; // 入口状态】的状态"}, "popupType": {"$ref": "#/definitions/inletPopupType", "title": "弹窗的状态【决定弹窗的类型，在showType开启的时候才会有效】:在nextReqTime过期后重新加载"}}, "title": "ActivityStateRsp 查询活动状态响应 下次请求仅在 在need_data=1的时候不判断next_req_time。入口服务也不记载data"}, "inletBuyType": {"type": "string", "enum": ["BuyType_None", "BuyType_Yes"], "default": "BuyType_None", "description": "- BuyType_None: 未购买\n - BuyType_Yes: 已购买", "title": "BuyType 需要具体数据"}, "inletInletNumber": {"type": "string", "enum": ["InletNumber_None", "InletNumber_HonorMedalCompetition", "InletNumber_AirplaneRace", "InletNumber_SuperColorfulLights", "InletNumber_TeamRudder", "InletNumber_TeamCompetition", "InletNumber_BattlePass", "InletNumber_Fishing", "InletNumber_CuteRabbitParadise", "InletNumber_EndlessTreasures", "InletNumber_CheckIn", "InletNumber_LightingRush", "InletNumber_SpecialDiscountPackage", "InletNumber_NoviceChallengeEvent", "InletNumber_DragonsTreasure", "InletNumber_InviteFriends", "InletNumber_ShareFriends", "InletNumber_Favorite", "InletNumber_Recharge", "InletNumber_EveryDayReceive", "InletNumber_DragonBoat", "InletNumber_DailyTask", "InletNumber_Laba", "InletNumber_Vip", "InletNumber_CwsxShop", "InletNumber_GameHub", "InletNumber_Announces", "InletNumber_RescuePlants", "InletNumber_Dan", "InletNumber_Block", "InletNumber_DecPop", "InletNumber_WeekRank", "InletNumber_StarActivity", "InletNumber_PeakRaceBigRound", "InletNumber_CollectTasks", "InletNumber_PeakRaceSmallRound", "InletNumber_Announce"], "default": "InletNumber_None", "description": "- InletNumber_None: 无\n - InletNumber_HonorMedalCompetition: 荣耀奖牌赛 @卢学彦\n - InletNumber_AirplaneRace: 飞机竞赛 @作废\n - InletNumber_SuperColorfulLights: 超级彩灯 @上官冲\n - InletNumber_TeamRudder: 战队淘金 @裴晓晨\n - InletNumber_TeamCompetition: 战队竞赛  @作废\n - InletNumber_BattlePass: 战令 @上官冲\n - InletNumber_Fishing: 小猫钓鱼 @裴晓晨\n - InletNumber_CuteRabbitParadise: 萌兔乐园 @卢学彦\n - InletNumber_EndlessTreasures: 无尽宝藏 @陈航\n - InletNumber_CheckIn: 签到 @上官冲\n - InletNumber_LightingRush: 彩虹竞速 @裴晓晨\n - InletNumber_SpecialDiscountPackage: 特惠礼包 @陈航\n - InletNumber_NoviceChallengeEvent: 新手闯关活动 @陈航\n - InletNumber_DragonsTreasure: 巨龙宝藏 @陈航\n - InletNumber_InviteFriends: 邀请好友 @白龙斐\n - InletNumber_ShareFriends: 分享好友 @上官冲\n - InletNumber_Favorite: 收藏 @白龙斐\n - InletNumber_Recharge: 宠物三消充值活动 @王国栋\n - InletNumber_EveryDayReceive: 分天领取礼包 @王国栋\n - InletNumber_DragonBoat: 龙舟竞赛 @曾润良\n - InletNumber_DailyTask: 任务中心每日消除金币任务 @卢学彦\n - InletNumber_Laba: 拉霸活动@车照\n - InletNumber_Vip: VIP专有客服 @曾润良\n - InletNumber_CwsxShop: 宠物三消商城入口 @裴晓晨\n - InletNumber_GameHub: 微信游戏圈 @裴晓晨\n - InletNumber_Announces: 公告 @白龙斐\n - InletNumber_RescuePlants: 营救植物 @上官冲\n - InletNumber_Dan: 段位赛 @车照\n - InletNumber_Block: 俄罗斯方块 @裴晓晨\n - InletNumber_DecPop: ------ 游戏中的单机小游戏 从200开始-------\n\n解密消除 @白龙斐\n - InletNumber_WeekRank: -------------\n下面是仅有弹窗无的枚举 Begin\n-------------\n非入口的活动 增加枚举值， 1000 开头\n\n周排行榜 @上官冲\n - InletNumber_StarActivity: 明星活动 @裴晓晨\n - InletNumber_PeakRaceBigRound: 巅峰赛大轮次 @上官冲\n - InletNumber_CollectTasks: 收集任务 @上官冲\n - InletNumber_PeakRaceSmallRound: 巅峰赛小轮次 @上官冲\n - InletNumber_Announce: @废弃 公告由 InletNumber_Announces(26号)代替", "title": "InletNumber 增加枚举值"}, "inletInletStatusType": {"type": "string", "enum": ["InletStatusType_None", "InletStatusType_Open", "InletStatusType_OpenAndJoin", "InletStatusType_EndHaveAward", "InletStatusType_EndReceivedAward", "InletStatusType_EndNoAward", "InletStatusType_EndNoJoin", "InletStatusType_AbClose", "InletStatusType_OpenAndJoin_HasAward", "InletStatusType_Notice"], "default": "InletStatusType_None", "description": "- InletStatusType_None: 无定义，继续保持老状态\n - InletStatusType_Open: 活动开启中但未参与\n - InletStatusType_OpenAndJoin: 活动开启，玩家参与中\n - InletStatusType_EndHaveAward: 活动结束，但有奖可领：有待领取的奖品\n - InletStatusType_EndReceivedAward: 活动结束，但有奖可领：全部奖品领取完成\n - InletStatusType_EndNoAward: 活动结束，但无奖可领：参与后未达到领奖条件\n - InletStatusType_EndNoJoin: 活动结束，且一直未参与\n - InletStatusType_AbClose: Ab无法参与\n - InletStatusType_OpenAndJoin_HasAward: 活动开启，玩家参与中：有待领取的奖品[特殊活动使用]\n - InletStatusType_Notice: 预告期", "title": "InletStatusType 入口状态枚举 （活动入口加载的状态值、弹窗所用的状态值）"}, "inletManageShowType": {"type": "string", "enum": ["ManageShowType_None", "ManageShowType_Open", "ManageShowType_Close"], "default": "ManageShowType_None", "description": "- ManageShowType_None: 无定义，继续保持老状态\n - ManageShowType_Open: 入口展示\n - ManageShowType_Close: 入口关闭", "title": "ManageShowType 查询活动状态响应"}, "inletNeedDataType": {"type": "string", "enum": ["NeedDataType_None", "NeedDataType_Yes"], "default": "NeedDataType_None", "description": "- NeedDataType_None: 不需要活动具体数据，仅返回状态即可\n - NeedDataType_Yes: 需要具体数据", "title": "NeedDataType 需要具体数据"}, "inletPlatformType": {"type": "string", "enum": ["PlatformUnknown", "PlatformAndroid", "PlatformIOS"], "default": "PlatformUnknown", "title": "PlatformType 设备类型"}, "inletPopupType": {"type": "string", "enum": ["PopupType_None", "PopupType_EndNoAward", "PopupType_EndHaveAward", "PopupType_Begin", "PopupType_Buy"], "default": "PopupType_None", "description": "- PopupType_None: 无\n - PopupType_EndNoAward: 结算期间：无领奖状态\n - PopupType_EndHaveAward: 结算期间：有领奖状态\n - PopupType_Begin: 活动开启弹窗\n - PopupType_Buy: 购买类型:展示时间", "title": "弹窗的类型 弹窗的类型状态 弹窗顺序由编号升序决策"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "xian_cwsx_danDanInfo": {"type": "object", "properties": {"dan": {"type": "integer", "format": "int32"}, "cd": {"type": "integer", "format": "int32"}}}}}