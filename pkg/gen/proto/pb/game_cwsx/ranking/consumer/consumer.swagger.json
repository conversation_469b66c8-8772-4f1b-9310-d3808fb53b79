{"swagger": "2.0", "info": {"title": "pb/game_cwsx/ranking/consumer/consumer.proto", "version": "version not set"}, "tags": [{"name": "Consumer"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game_cwsx_ranking.Consumer/PlaceHolder": {"post": {"operationId": "Consumer_PlaceHolder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_cwsx_rankingPlaceHolderRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_cwsx_rankingPlaceHolderReq"}}], "tags": ["Consumer"]}}}, "definitions": {"game_cwsx_rankingPlaceHolderReq": {"type": "object"}, "game_cwsx_rankingPlaceHolderRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}