// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_cwsx/wechat_proxy/wechat_proxy.proto

package wechat_proxy

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	WechatProxy_SendMail_FullMethodName               = "/game.WechatProxy/SendMail"
	WechatProxy_SafetyCheck_FullMethodName            = "/game.WechatProxy/SafetyCheck"
	WechatProxy_SafetyCallback_FullMethodName         = "/game.WechatProxy/SafetyCallback"
	WechatProxy_DeliverGoodsCallback_FullMethodName   = "/game.WechatProxy/DeliverGoodsCallback"
	WechatProxy_DeliverGoodsCallbackV2_FullMethodName = "/game.WechatProxy/DeliverGoodsCallbackV2"
)

// WechatProxyClient is the client API for WechatProxy service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WechatProxyClient interface {
	SendMail(ctx context.Context, in *SendMailReq, opts ...grpc.CallOption) (*SendMailRsp, error)
	SafetyCheck(ctx context.Context, in *SafetyCheckReq, opts ...grpc.CallOption) (*SafetyCheckRsp, error)
	SafetyCallback(ctx context.Context, in *SafetyCallbackReq, opts ...grpc.CallOption) (*SafetyCallbackRsp, error)
	DeliverGoodsCallback(ctx context.Context, in *DeliverGoodsCallbackReq, opts ...grpc.CallOption) (*DeliverGoodsCallbackRsp, error)
	DeliverGoodsCallbackV2(ctx context.Context, in *DeliverGoodsCallbackV2Req, opts ...grpc.CallOption) (*DeliverGoodsCallbackV2Rsp, error)
}

type wechatProxyClient struct {
	cc grpc.ClientConnInterface
}

func NewWechatProxyClient(cc grpc.ClientConnInterface) WechatProxyClient {
	return &wechatProxyClient{cc}
}

func (c *wechatProxyClient) SendMail(ctx context.Context, in *SendMailReq, opts ...grpc.CallOption) (*SendMailRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendMailRsp)
	err := c.cc.Invoke(ctx, WechatProxy_SendMail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wechatProxyClient) SafetyCheck(ctx context.Context, in *SafetyCheckReq, opts ...grpc.CallOption) (*SafetyCheckRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SafetyCheckRsp)
	err := c.cc.Invoke(ctx, WechatProxy_SafetyCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wechatProxyClient) SafetyCallback(ctx context.Context, in *SafetyCallbackReq, opts ...grpc.CallOption) (*SafetyCallbackRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SafetyCallbackRsp)
	err := c.cc.Invoke(ctx, WechatProxy_SafetyCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wechatProxyClient) DeliverGoodsCallback(ctx context.Context, in *DeliverGoodsCallbackReq, opts ...grpc.CallOption) (*DeliverGoodsCallbackRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeliverGoodsCallbackRsp)
	err := c.cc.Invoke(ctx, WechatProxy_DeliverGoodsCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wechatProxyClient) DeliverGoodsCallbackV2(ctx context.Context, in *DeliverGoodsCallbackV2Req, opts ...grpc.CallOption) (*DeliverGoodsCallbackV2Rsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeliverGoodsCallbackV2Rsp)
	err := c.cc.Invoke(ctx, WechatProxy_DeliverGoodsCallbackV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WechatProxyServer is the server API for WechatProxy service.
// All implementations should embed UnimplementedWechatProxyServer
// for forward compatibility
type WechatProxyServer interface {
	SendMail(context.Context, *SendMailReq) (*SendMailRsp, error)
	SafetyCheck(context.Context, *SafetyCheckReq) (*SafetyCheckRsp, error)
	SafetyCallback(context.Context, *SafetyCallbackReq) (*SafetyCallbackRsp, error)
	DeliverGoodsCallback(context.Context, *DeliverGoodsCallbackReq) (*DeliverGoodsCallbackRsp, error)
	DeliverGoodsCallbackV2(context.Context, *DeliverGoodsCallbackV2Req) (*DeliverGoodsCallbackV2Rsp, error)
}

// UnimplementedWechatProxyServer should be embedded to have forward compatible implementations.
type UnimplementedWechatProxyServer struct {
}

func (UnimplementedWechatProxyServer) SendMail(context.Context, *SendMailReq) (*SendMailRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMail not implemented")
}
func (UnimplementedWechatProxyServer) SafetyCheck(context.Context, *SafetyCheckReq) (*SafetyCheckRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SafetyCheck not implemented")
}
func (UnimplementedWechatProxyServer) SafetyCallback(context.Context, *SafetyCallbackReq) (*SafetyCallbackRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SafetyCallback not implemented")
}
func (UnimplementedWechatProxyServer) DeliverGoodsCallback(context.Context, *DeliverGoodsCallbackReq) (*DeliverGoodsCallbackRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeliverGoodsCallback not implemented")
}
func (UnimplementedWechatProxyServer) DeliverGoodsCallbackV2(context.Context, *DeliverGoodsCallbackV2Req) (*DeliverGoodsCallbackV2Rsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeliverGoodsCallbackV2 not implemented")
}

// UnsafeWechatProxyServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WechatProxyServer will
// result in compilation errors.
type UnsafeWechatProxyServer interface {
	mustEmbedUnimplementedWechatProxyServer()
}

func RegisterWechatProxyServer(s grpc.ServiceRegistrar, srv WechatProxyServer) {
	s.RegisterService(&WechatProxy_ServiceDesc, srv)
}

func _WechatProxy_SendMail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WechatProxyServer).SendMail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WechatProxy_SendMail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WechatProxyServer).SendMail(ctx, req.(*SendMailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WechatProxy_SafetyCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SafetyCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WechatProxyServer).SafetyCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WechatProxy_SafetyCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WechatProxyServer).SafetyCheck(ctx, req.(*SafetyCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WechatProxy_SafetyCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SafetyCallbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WechatProxyServer).SafetyCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WechatProxy_SafetyCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WechatProxyServer).SafetyCallback(ctx, req.(*SafetyCallbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WechatProxy_DeliverGoodsCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeliverGoodsCallbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WechatProxyServer).DeliverGoodsCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WechatProxy_DeliverGoodsCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WechatProxyServer).DeliverGoodsCallback(ctx, req.(*DeliverGoodsCallbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WechatProxy_DeliverGoodsCallbackV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeliverGoodsCallbackV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WechatProxyServer).DeliverGoodsCallbackV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WechatProxy_DeliverGoodsCallbackV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WechatProxyServer).DeliverGoodsCallbackV2(ctx, req.(*DeliverGoodsCallbackV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

// WechatProxy_ServiceDesc is the grpc.ServiceDesc for WechatProxy service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WechatProxy_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game.WechatProxy",
	HandlerType: (*WechatProxyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendMail",
			Handler:    _WechatProxy_SendMail_Handler,
		},
		{
			MethodName: "SafetyCheck",
			Handler:    _WechatProxy_SafetyCheck_Handler,
		},
		{
			MethodName: "SafetyCallback",
			Handler:    _WechatProxy_SafetyCallback_Handler,
		},
		{
			MethodName: "DeliverGoodsCallback",
			Handler:    _WechatProxy_DeliverGoodsCallback_Handler,
		},
		{
			MethodName: "DeliverGoodsCallbackV2",
			Handler:    _WechatProxy_DeliverGoodsCallbackV2_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_cwsx/wechat_proxy/wechat_proxy.proto",
}
