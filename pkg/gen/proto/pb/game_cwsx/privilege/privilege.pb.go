// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/privilege/privilege.proto

package privilege

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BatchGetDecorateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId []string `protobuf:"bytes,2,rep,name=openId,proto3" json:"openId,omitempty"`
}

func (x *BatchGetDecorateReq) Reset() {
	*x = BatchGetDecorateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_privilege_privilege_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetDecorateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetDecorateReq) ProtoMessage() {}

func (x *BatchGetDecorateReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_privilege_privilege_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetDecorateReq.ProtoReflect.Descriptor instead.
func (*BatchGetDecorateReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_privilege_privilege_proto_rawDescGZIP(), []int{0}
}

func (x *BatchGetDecorateReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchGetDecorateReq) GetOpenId() []string {
	if x != nil {
		return x.OpenId
	}
	return nil
}

type UserPriviledges struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key以约定好的cwsx_plg_作为前缀
	Status map[string]string `protobuf:"bytes,1,rep,name=status,proto3" json:"status,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 用户正在穿戴的特权列表
}

func (x *UserPriviledges) Reset() {
	*x = UserPriviledges{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_privilege_privilege_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPriviledges) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPriviledges) ProtoMessage() {}

func (x *UserPriviledges) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_privilege_privilege_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPriviledges.ProtoReflect.Descriptor instead.
func (*UserPriviledges) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_privilege_privilege_proto_rawDescGZIP(), []int{1}
}

func (x *UserPriviledges) GetStatus() map[string]string {
	if x != nil {
		return x.Status
	}
	return nil
}

type BatchGetDecorateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapExt map[string]*UserPriviledges `protobuf:"bytes,1,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchGetDecorateRsp) Reset() {
	*x = BatchGetDecorateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_privilege_privilege_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetDecorateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetDecorateRsp) ProtoMessage() {}

func (x *BatchGetDecorateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_privilege_privilege_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetDecorateRsp.ProtoReflect.Descriptor instead.
func (*BatchGetDecorateRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_privilege_privilege_proto_rawDescGZIP(), []int{2}
}

func (x *BatchGetDecorateRsp) GetMapExt() map[string]*UserPriviledges {
	if x != nil {
		return x.MapExt
	}
	return nil
}

var File_pb_game_cwsx_privilege_privilege_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_privilege_privilege_proto_rawDesc = []byte{
	0x0a, 0x26, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x70,
	0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x2f, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65,
	0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c,
	0x65, 0x67, 0x65, 0x22, 0x43, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x44,
	0x65, 0x63, 0x6f, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x8c, 0x01, 0x0a, 0x0f, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70,
	0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x69,
	0x76, 0x69, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x39, 0x0a, 0x0b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb0, 0x01, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x44, 0x65, 0x63, 0x6f, 0x72, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x42, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x44, 0x65, 0x63, 0x6f, 0x72, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x2e,
	0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70,
	0x45, 0x78, 0x74, 0x1a, 0x55, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x73, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x4b, 0x5a, 0x49, 0x74, 0x63,
	0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e,
	0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x70, 0x72,
	0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_cwsx_privilege_privilege_proto_rawDescOnce sync.Once
	file_pb_game_cwsx_privilege_privilege_proto_rawDescData = file_pb_game_cwsx_privilege_privilege_proto_rawDesc
)

func file_pb_game_cwsx_privilege_privilege_proto_rawDescGZIP() []byte {
	file_pb_game_cwsx_privilege_privilege_proto_rawDescOnce.Do(func() {
		file_pb_game_cwsx_privilege_privilege_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_cwsx_privilege_privilege_proto_rawDescData)
	})
	return file_pb_game_cwsx_privilege_privilege_proto_rawDescData
}

var file_pb_game_cwsx_privilege_privilege_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_pb_game_cwsx_privilege_privilege_proto_goTypes = []interface{}{
	(*BatchGetDecorateReq)(nil), // 0: privilege.BatchGetDecorateReq
	(*UserPriviledges)(nil),     // 1: privilege.UserPriviledges
	(*BatchGetDecorateRsp)(nil), // 2: privilege.BatchGetDecorateRsp
	nil,                         // 3: privilege.UserPriviledges.StatusEntry
	nil,                         // 4: privilege.BatchGetDecorateRsp.MapExtEntry
}
var file_pb_game_cwsx_privilege_privilege_proto_depIdxs = []int32{
	3, // 0: privilege.UserPriviledges.status:type_name -> privilege.UserPriviledges.StatusEntry
	4, // 1: privilege.BatchGetDecorateRsp.mapExt:type_name -> privilege.BatchGetDecorateRsp.MapExtEntry
	1, // 2: privilege.BatchGetDecorateRsp.MapExtEntry.value:type_name -> privilege.UserPriviledges
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_privilege_privilege_proto_init() }
func file_pb_game_cwsx_privilege_privilege_proto_init() {
	if File_pb_game_cwsx_privilege_privilege_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_cwsx_privilege_privilege_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetDecorateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_privilege_privilege_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserPriviledges); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_privilege_privilege_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetDecorateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_privilege_privilege_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_game_cwsx_privilege_privilege_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_privilege_privilege_proto_depIdxs,
		MessageInfos:      file_pb_game_cwsx_privilege_privilege_proto_msgTypes,
	}.Build()
	File_pb_game_cwsx_privilege_privilege_proto = out.File
	file_pb_game_cwsx_privilege_privilege_proto_rawDesc = nil
	file_pb_game_cwsx_privilege_privilege_proto_goTypes = nil
	file_pb_game_cwsx_privilege_privilege_proto_depIdxs = nil
}
