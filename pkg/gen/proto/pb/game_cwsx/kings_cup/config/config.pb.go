// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/kings_cup/config/config.proto

package config

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type KingsCupRoundConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoundId          uint32 `protobuf:"varint,1,opt,name=roundId,proto3" json:"roundId,omitempty"`
	RunBegin         uint32 `protobuf:"varint,2,opt,name=runBegin,proto3" json:"runBegin,omitempty"`                 // 参与开始时间
	RunEnd           uint32 `protobuf:"varint,3,opt,name=runEnd,proto3" json:"runEnd,omitempty"`                     // 参与结束时间
	ClaimBegin       uint32 `protobuf:"varint,4,opt,name=claimBegin,proto3" json:"claimBegin,omitempty"`             // 领奖开始时间(星期)
	ClaimEnd         uint32 `protobuf:"varint,5,opt,name=claimEnd,proto3" json:"claimEnd,omitempty"`                 // 领奖结束时间(星期)
	ClaimBeginMinute uint32 `protobuf:"varint,6,opt,name=claimBeginMinute,proto3" json:"claimBeginMinute,omitempty"` // 领奖开始时刻(分钟)
	ClaimEndMinite   uint32 `protobuf:"varint,7,opt,name=claimEndMinite,proto3" json:"claimEndMinite,omitempty"`     // 领奖结束时刻(分钟)
	RunBeginMinite   uint32 `protobuf:"varint,8,opt,name=runBeginMinite,proto3" json:"runBeginMinite,omitempty"`     // 轮次开始时刻(分钟)
	RunEndMinite     uint32 `protobuf:"varint,9,opt,name=runEndMinite,proto3" json:"runEndMinite,omitempty"`         // 轮次结束时刻(分钟)
}

func (x *KingsCupRoundConfig) Reset() {
	*x = KingsCupRoundConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KingsCupRoundConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KingsCupRoundConfig) ProtoMessage() {}

func (x *KingsCupRoundConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KingsCupRoundConfig.ProtoReflect.Descriptor instead.
func (*KingsCupRoundConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_config_config_proto_rawDescGZIP(), []int{0}
}

func (x *KingsCupRoundConfig) GetRoundId() uint32 {
	if x != nil {
		return x.RoundId
	}
	return 0
}

func (x *KingsCupRoundConfig) GetRunBegin() uint32 {
	if x != nil {
		return x.RunBegin
	}
	return 0
}

func (x *KingsCupRoundConfig) GetRunEnd() uint32 {
	if x != nil {
		return x.RunEnd
	}
	return 0
}

func (x *KingsCupRoundConfig) GetClaimBegin() uint32 {
	if x != nil {
		return x.ClaimBegin
	}
	return 0
}

func (x *KingsCupRoundConfig) GetClaimEnd() uint32 {
	if x != nil {
		return x.ClaimEnd
	}
	return 0
}

func (x *KingsCupRoundConfig) GetClaimBeginMinute() uint32 {
	if x != nil {
		return x.ClaimBeginMinute
	}
	return 0
}

func (x *KingsCupRoundConfig) GetClaimEndMinite() uint32 {
	if x != nil {
		return x.ClaimEndMinite
	}
	return 0
}

func (x *KingsCupRoundConfig) GetRunBeginMinite() uint32 {
	if x != nil {
		return x.RunBeginMinite
	}
	return 0
}

func (x *KingsCupRoundConfig) GetRunEndMinite() uint32 {
	if x != nil {
		return x.RunEndMinite
	}
	return 0
}

type KingsCupStageMapConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CupsNum   uint32 `protobuf:"varint,1,opt,name=cupsNum,proto3" json:"cupsNum,omitempty"`
	StageType uint32 `protobuf:"varint,2,opt,name=stageType,proto3" json:"stageType,omitempty"`
}

func (x *KingsCupStageMapConfig) Reset() {
	*x = KingsCupStageMapConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KingsCupStageMapConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KingsCupStageMapConfig) ProtoMessage() {}

func (x *KingsCupStageMapConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KingsCupStageMapConfig.ProtoReflect.Descriptor instead.
func (*KingsCupStageMapConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_config_config_proto_rawDescGZIP(), []int{1}
}

func (x *KingsCupStageMapConfig) GetCupsNum() uint32 {
	if x != nil {
		return x.CupsNum
	}
	return 0
}

func (x *KingsCupStageMapConfig) GetStageType() uint32 {
	if x != nil {
		return x.StageType
	}
	return 0
}

type KingsCupRewardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rank   uint32   `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"`
	PkgIds []uint32 `protobuf:"varint,2,rep,packed,name=pkgIds,proto3" json:"pkgIds,omitempty"`
}

func (x *KingsCupRewardConfig) Reset() {
	*x = KingsCupRewardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KingsCupRewardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KingsCupRewardConfig) ProtoMessage() {}

func (x *KingsCupRewardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KingsCupRewardConfig.ProtoReflect.Descriptor instead.
func (*KingsCupRewardConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_config_config_proto_rawDescGZIP(), []int{2}
}

func (x *KingsCupRewardConfig) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *KingsCupRewardConfig) GetPkgIds() []uint32 {
	if x != nil {
		return x.PkgIds
	}
	return nil
}

type KingsCupWhitelistConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable  bool     `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	OpenIds []string `protobuf:"bytes,2,rep,name=openIds,proto3" json:"openIds,omitempty"`
}

func (x *KingsCupWhitelistConfig) Reset() {
	*x = KingsCupWhitelistConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KingsCupWhitelistConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KingsCupWhitelistConfig) ProtoMessage() {}

func (x *KingsCupWhitelistConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KingsCupWhitelistConfig.ProtoReflect.Descriptor instead.
func (*KingsCupWhitelistConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_config_config_proto_rawDescGZIP(), []int{3}
}

func (x *KingsCupWhitelistConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *KingsCupWhitelistConfig) GetOpenIds() []string {
	if x != nil {
		return x.OpenIds
	}
	return nil
}

type KingsCupPartConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TypeId   uint32 `protobuf:"varint,1,opt,name=typeId,proto3" json:"typeId,omitempty"`     // 排行榜分类id
	MaxStage uint32 `protobuf:"varint,2,opt,name=maxStage,proto3" json:"maxStage,omitempty"` // 初始闯关stageId<maxStage时算入typeId
	MaxSize  uint32 `protobuf:"varint,4,opt,name=maxSize,proto3" json:"maxSize,omitempty"`   // 每个排行榜最大数量
}

func (x *KingsCupPartConfig) Reset() {
	*x = KingsCupPartConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KingsCupPartConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KingsCupPartConfig) ProtoMessage() {}

func (x *KingsCupPartConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KingsCupPartConfig.ProtoReflect.Descriptor instead.
func (*KingsCupPartConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_config_config_proto_rawDescGZIP(), []int{4}
}

func (x *KingsCupPartConfig) GetTypeId() uint32 {
	if x != nil {
		return x.TypeId
	}
	return 0
}

func (x *KingsCupPartConfig) GetMaxStage() uint32 {
	if x != nil {
		return x.MaxStage
	}
	return 0
}

func (x *KingsCupPartConfig) GetMaxSize() uint32 {
	if x != nil {
		return x.MaxSize
	}
	return 0
}

type KingsCupRobotConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Interval     uint32 `protobuf:"varint,1,opt,name=interval,proto3" json:"interval,omitempty"`         // 间隔
	InitCount    uint32 `protobuf:"varint,2,opt,name=initCount,proto3" json:"initCount,omitempty"`       // 初始化人数
	Rate         uint32 `protobuf:"varint,3,opt,name=rate,proto3" json:"rate,omitempty"`                 // 加进度概率(0-100)
	InitMaxCount uint32 `protobuf:"varint,4,opt,name=initMaxCount,proto3" json:"initMaxCount,omitempty"` // 初始最大人数, 在[initCount,initMaxCount]随机取一个
}

func (x *KingsCupRobotConfig) Reset() {
	*x = KingsCupRobotConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KingsCupRobotConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KingsCupRobotConfig) ProtoMessage() {}

func (x *KingsCupRobotConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KingsCupRobotConfig.ProtoReflect.Descriptor instead.
func (*KingsCupRobotConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_config_config_proto_rawDescGZIP(), []int{5}
}

func (x *KingsCupRobotConfig) GetInterval() uint32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (x *KingsCupRobotConfig) GetInitCount() uint32 {
	if x != nil {
		return x.InitCount
	}
	return 0
}

func (x *KingsCupRobotConfig) GetRate() uint32 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *KingsCupRobotConfig) GetInitMaxCount() uint32 {
	if x != nil {
		return x.InitMaxCount
	}
	return 0
}

type KingsCupConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                uint32                    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                              // 配置id
	Enable            bool                      `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`                      // 上下线
	BeginTime         int64                     `protobuf:"varint,3,opt,name=beginTime,proto3" json:"beginTime,omitempty"`                // 整体上线时间
	EndTime           int64                     `protobuf:"varint,4,opt,name=endTime,proto3" json:"endTime,omitempty"`                    // 整体下线时间
	Rounds            []*KingsCupRoundConfig    `protobuf:"bytes,5,rep,name=rounds,proto3" json:"rounds,omitempty"`                       // 每周轮次配置
	Cups              []*KingsCupStageMapConfig `protobuf:"bytes,6,rep,name=cups,proto3" json:"cups,omitempty"`                           // 关卡难度映射
	Rewards           []*KingsCupRewardConfig   `protobuf:"bytes,7,rep,name=rewards,proto3" json:"rewards,omitempty"`                     // 排名配置
	Part              []*KingsCupPartConfig     `protobuf:"bytes,8,rep,name=part,proto3" json:"part,omitempty"`                           // 排行榜分区配置
	RewardSenderRoute string                    `protobuf:"bytes,9,opt,name=rewardSenderRoute,proto3" json:"rewardSenderRoute,omitempty"` // 发奖接口
	RewardQueryRoute  string                    `protobuf:"bytes,10,opt,name=rewardQueryRoute,proto3" json:"rewardQueryRoute,omitempty"`  // 查询礼包接口
	Whitelist         *KingsCupWhitelistConfig  `protobuf:"bytes,11,opt,name=whitelist,proto3" json:"whitelist,omitempty"`                // 白名单
	Robot             *KingsCupRobotConfig      `protobuf:"bytes,12,opt,name=robot,proto3" json:"robot,omitempty"`
	AppId             string                    `protobuf:"bytes,13,opt,name=appId,proto3" json:"appId,omitempty"`
	MinStageId        int64                     `protobuf:"varint,14,opt,name=minStageId,proto3" json:"minStageId,omitempty"` // 最小关卡Id
}

func (x *KingsCupConfig) Reset() {
	*x = KingsCupConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KingsCupConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KingsCupConfig) ProtoMessage() {}

func (x *KingsCupConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KingsCupConfig.ProtoReflect.Descriptor instead.
func (*KingsCupConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_config_config_proto_rawDescGZIP(), []int{6}
}

func (x *KingsCupConfig) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *KingsCupConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *KingsCupConfig) GetBeginTime() int64 {
	if x != nil {
		return x.BeginTime
	}
	return 0
}

func (x *KingsCupConfig) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *KingsCupConfig) GetRounds() []*KingsCupRoundConfig {
	if x != nil {
		return x.Rounds
	}
	return nil
}

func (x *KingsCupConfig) GetCups() []*KingsCupStageMapConfig {
	if x != nil {
		return x.Cups
	}
	return nil
}

func (x *KingsCupConfig) GetRewards() []*KingsCupRewardConfig {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *KingsCupConfig) GetPart() []*KingsCupPartConfig {
	if x != nil {
		return x.Part
	}
	return nil
}

func (x *KingsCupConfig) GetRewardSenderRoute() string {
	if x != nil {
		return x.RewardSenderRoute
	}
	return ""
}

func (x *KingsCupConfig) GetRewardQueryRoute() string {
	if x != nil {
		return x.RewardQueryRoute
	}
	return ""
}

func (x *KingsCupConfig) GetWhitelist() *KingsCupWhitelistConfig {
	if x != nil {
		return x.Whitelist
	}
	return nil
}

func (x *KingsCupConfig) GetRobot() *KingsCupRobotConfig {
	if x != nil {
		return x.Robot
	}
	return nil
}

func (x *KingsCupConfig) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *KingsCupConfig) GetMinStageId() int64 {
	if x != nil {
		return x.MinStageId
	}
	return 0
}

var File_pb_game_cwsx_kings_cup_config_config_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_kings_cup_config_config_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x6b,
	0x69, 0x6e, 0x67, 0x73, 0x5f, 0x63, 0x75, 0x70, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70,
	0x22, 0xbf, 0x02, 0x0a, 0x13, 0x4b, 0x69, 0x6e, 0x67, 0x73, 0x43, 0x75, 0x70, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x75, 0x6e, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x75, 0x6e, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x75, 0x6e, 0x45, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x72, 0x75, 0x6e, 0x45, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x42,
	0x65, 0x67, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x6c, 0x61, 0x69,
	0x6d, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x45,
	0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x45,
	0x6e, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x42, 0x65, 0x67, 0x69, 0x6e,
	0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x63, 0x6c,
	0x61, 0x69, 0x6d, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x12, 0x26,
	0x0a, 0x0e, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x45, 0x6e, 0x64, 0x4d, 0x69, 0x6e, 0x69, 0x74, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x45, 0x6e, 0x64,
	0x4d, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x75, 0x6e, 0x42, 0x65, 0x67,
	0x69, 0x6e, 0x4d, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e,
	0x72, 0x75, 0x6e, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x4d, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x72, 0x75, 0x6e, 0x45, 0x6e, 0x64, 0x4d, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x72, 0x75, 0x6e, 0x45, 0x6e, 0x64, 0x4d, 0x69, 0x6e, 0x69,
	0x74, 0x65, 0x22, 0x50, 0x0a, 0x16, 0x4b, 0x69, 0x6e, 0x67, 0x73, 0x43, 0x75, 0x70, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x4d, 0x61, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x75, 0x70, 0x73, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x63,
	0x75, 0x70, 0x73, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x42, 0x0a, 0x14, 0x4b, 0x69, 0x6e, 0x67, 0x73, 0x43, 0x75, 0x70,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04,
	0x72, 0x61, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b,
	0x12, 0x16, 0x0a, 0x06, 0x70, 0x6b, 0x67, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d,
	0x52, 0x06, 0x70, 0x6b, 0x67, 0x49, 0x64, 0x73, 0x22, 0x4b, 0x0a, 0x17, 0x4b, 0x69, 0x6e, 0x67,
	0x73, 0x43, 0x75, 0x70, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x62, 0x0a, 0x12, 0x4b, 0x69, 0x6e, 0x67, 0x73, 0x43, 0x75,
	0x70, 0x50, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x74,
	0x79, 0x70, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x74, 0x79, 0x70,
	0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x53, 0x74, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x6d, 0x61, 0x78, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x13, 0x4b, 0x69,
	0x6e, 0x67, 0x73, 0x43, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x1c, 0x0a,
	0x09, 0x69, 0x6e, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x69, 0x6e, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x78, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x8b, 0x05, 0x0a, 0x0e, 0x4b, 0x69, 0x6e, 0x67, 0x73, 0x43, 0x75, 0x70,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x06, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x4b, 0x69, 0x6e, 0x67,
	0x73, 0x43, 0x75, 0x70, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x06, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x3e, 0x0a, 0x04, 0x63, 0x75, 0x70, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x4b, 0x69, 0x6e, 0x67, 0x73,
	0x43, 0x75, 0x70, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4d, 0x61, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x04, 0x63, 0x75, 0x70, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x4b, 0x69,
	0x6e, 0x67, 0x73, 0x43, 0x75, 0x70, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x3a, 0x0a, 0x04, 0x70,
	0x61, 0x72, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x4b,
	0x69, 0x6e, 0x67, 0x73, 0x43, 0x75, 0x70, 0x50, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x04, 0x70, 0x61, 0x72, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x12, 0x49, 0x0a, 0x09, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x4b, 0x69, 0x6e, 0x67, 0x73, 0x43,
	0x75, 0x70, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x09, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x05,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70,
	0x2e, 0x4b, 0x69, 0x6e, 0x67, 0x73, 0x43, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49,
	0x64, 0x42, 0x52, 0x5a, 0x50, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x63, 0x75, 0x70, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_cwsx_kings_cup_config_config_proto_rawDescOnce sync.Once
	file_pb_game_cwsx_kings_cup_config_config_proto_rawDescData = file_pb_game_cwsx_kings_cup_config_config_proto_rawDesc
)

func file_pb_game_cwsx_kings_cup_config_config_proto_rawDescGZIP() []byte {
	file_pb_game_cwsx_kings_cup_config_config_proto_rawDescOnce.Do(func() {
		file_pb_game_cwsx_kings_cup_config_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_cwsx_kings_cup_config_config_proto_rawDescData)
	})
	return file_pb_game_cwsx_kings_cup_config_config_proto_rawDescData
}

var file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_pb_game_cwsx_kings_cup_config_config_proto_goTypes = []interface{}{
	(*KingsCupRoundConfig)(nil),     // 0: game_cwsx_kingscup.KingsCupRoundConfig
	(*KingsCupStageMapConfig)(nil),  // 1: game_cwsx_kingscup.KingsCupStageMapConfig
	(*KingsCupRewardConfig)(nil),    // 2: game_cwsx_kingscup.KingsCupRewardConfig
	(*KingsCupWhitelistConfig)(nil), // 3: game_cwsx_kingscup.KingsCupWhitelistConfig
	(*KingsCupPartConfig)(nil),      // 4: game_cwsx_kingscup.KingsCupPartConfig
	(*KingsCupRobotConfig)(nil),     // 5: game_cwsx_kingscup.KingsCupRobotConfig
	(*KingsCupConfig)(nil),          // 6: game_cwsx_kingscup.KingsCupConfig
}
var file_pb_game_cwsx_kings_cup_config_config_proto_depIdxs = []int32{
	0, // 0: game_cwsx_kingscup.KingsCupConfig.rounds:type_name -> game_cwsx_kingscup.KingsCupRoundConfig
	1, // 1: game_cwsx_kingscup.KingsCupConfig.cups:type_name -> game_cwsx_kingscup.KingsCupStageMapConfig
	2, // 2: game_cwsx_kingscup.KingsCupConfig.rewards:type_name -> game_cwsx_kingscup.KingsCupRewardConfig
	4, // 3: game_cwsx_kingscup.KingsCupConfig.part:type_name -> game_cwsx_kingscup.KingsCupPartConfig
	3, // 4: game_cwsx_kingscup.KingsCupConfig.whitelist:type_name -> game_cwsx_kingscup.KingsCupWhitelistConfig
	5, // 5: game_cwsx_kingscup.KingsCupConfig.robot:type_name -> game_cwsx_kingscup.KingsCupRobotConfig
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_kings_cup_config_config_proto_init() }
func file_pb_game_cwsx_kings_cup_config_config_proto_init() {
	if File_pb_game_cwsx_kings_cup_config_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KingsCupRoundConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KingsCupStageMapConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KingsCupRewardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KingsCupWhitelistConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KingsCupPartConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KingsCupRobotConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KingsCupConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_kings_cup_config_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_game_cwsx_kings_cup_config_config_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_kings_cup_config_config_proto_depIdxs,
		MessageInfos:      file_pb_game_cwsx_kings_cup_config_config_proto_msgTypes,
	}.Build()
	File_pb_game_cwsx_kings_cup_config_config_proto = out.File
	file_pb_game_cwsx_kings_cup_config_config_proto_rawDesc = nil
	file_pb_game_cwsx_kings_cup_config_config_proto_goTypes = nil
	file_pb_game_cwsx_kings_cup_config_config_proto_depIdxs = nil
}
