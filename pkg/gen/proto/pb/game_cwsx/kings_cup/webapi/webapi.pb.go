// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/kings_cup/webapi/webapi.proto

package webapi

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	device "kugou_adapter_service/pkg/gen/proto/pb/device"
	game_api "kugou_adapter_service/pkg/gen/proto/pb/game_api"
	inlet "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/inlet"
	common "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/kings_cup/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WebQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId        string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`               // 可以放在header里,不显式传
	AppId         string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`                 // 可以放在header里,不显式传
	StageId       uint32 `protobuf:"varint,3,opt,name=stageId,proto3" json:"stageId,omitempty"`            // 当前的闯关id
	CachedBindKey string `protobuf:"bytes,4,opt,name=cachedBindKey,proto3" json:"cachedBindKey,omitempty"` // 前端本地缓存的bindKey, 如果后端发现缓存的bindKey和用户当前进行的bindKey不一致, 则返回当前上一轮的数据
}

func (x *WebQueryReq) Reset() {
	*x = WebQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebQueryReq) ProtoMessage() {}

func (x *WebQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebQueryReq.ProtoReflect.Descriptor instead.
func (*WebQueryReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescGZIP(), []int{0}
}

func (x *WebQueryReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *WebQueryReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *WebQueryReq) GetStageId() uint32 {
	if x != nil {
		return x.StageId
	}
	return 0
}

func (x *WebQueryReq) GetCachedBindKey() string {
	if x != nil {
		return x.CachedBindKey
	}
	return ""
}

type WebQueryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindKey       string                       `protobuf:"bytes,1,opt,name=bindKey,proto3" json:"bindKey,omitempty"`                                                    // 活动key
	Cups          uint32                       `protobuf:"varint,2,opt,name=cups,proto3" json:"cups,omitempty"`                                                         // 杯子数
	Rank          uint32                       `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`                                                         // 实时排名
	Remain        uint32                       `protobuf:"varint,4,opt,name=remain,proto3" json:"remain,omitempty"`                                                     // 剩余时间(s)
	State         common.UserActivityState     `protobuf:"varint,5,opt,name=state,proto3,enum=game_cwsx_kingscup.UserActivityState" json:"state,omitempty"`             // 用户活动参与状态
	RankState     common.UserActivityRankState `protobuf:"varint,6,opt,name=rankState,proto3,enum=game_cwsx_kingscup.UserActivityRankState" json:"rankState,omitempty"` // 是否达到榜单领奖标准
	LatestBindKey string                       `protobuf:"bytes,7,opt,name=latestBindKey,proto3" json:"latestBindKey,omitempty"`                                        // 当前正在进行的bindKey, 前端缓存起来, 并在下次请求时原封不懂回传给服务端
}

func (x *WebQueryRsp) Reset() {
	*x = WebQueryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebQueryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebQueryRsp) ProtoMessage() {}

func (x *WebQueryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebQueryRsp.ProtoReflect.Descriptor instead.
func (*WebQueryRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescGZIP(), []int{1}
}

func (x *WebQueryRsp) GetBindKey() string {
	if x != nil {
		return x.BindKey
	}
	return ""
}

func (x *WebQueryRsp) GetCups() uint32 {
	if x != nil {
		return x.Cups
	}
	return 0
}

func (x *WebQueryRsp) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *WebQueryRsp) GetRemain() uint32 {
	if x != nil {
		return x.Remain
	}
	return 0
}

func (x *WebQueryRsp) GetState() common.UserActivityState {
	if x != nil {
		return x.State
	}
	return common.UserActivityState(0)
}

func (x *WebQueryRsp) GetRankState() common.UserActivityRankState {
	if x != nil {
		return x.RankState
	}
	return common.UserActivityRankState(0)
}

func (x *WebQueryRsp) GetLatestBindKey() string {
	if x != nil {
		return x.LatestBindKey
	}
	return ""
}

type WebClaimReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId  string         `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`   // 可以放在header里,不显式传
	AppId   string         `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`     // 可以放在header里,不显式传
	BindKey string         `protobuf:"bytes,3,opt,name=bindKey,proto3" json:"bindKey,omitempty"` // 活动key
	Device  *device.Device `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`
}

func (x *WebClaimReq) Reset() {
	*x = WebClaimReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebClaimReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebClaimReq) ProtoMessage() {}

func (x *WebClaimReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebClaimReq.ProtoReflect.Descriptor instead.
func (*WebClaimReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescGZIP(), []int{2}
}

func (x *WebClaimReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *WebClaimReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *WebClaimReq) GetBindKey() string {
	if x != nil {
		return x.BindKey
	}
	return ""
}

func (x *WebClaimReq) GetDevice() *device.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type WebClaimRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cards   []*game_api.TreasureCard `protobuf:"bytes,2,rep,name=cards,proto3" json:"cards,omitempty"`     // 宝藏卡信息, 如果产生宝藏卡领取, 则放在这里
	Rewards []*game_api.RewardItem   `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"` // 奖励信息
}

func (x *WebClaimRsp) Reset() {
	*x = WebClaimRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebClaimRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebClaimRsp) ProtoMessage() {}

func (x *WebClaimRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebClaimRsp.ProtoReflect.Descriptor instead.
func (*WebClaimRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescGZIP(), []int{3}
}

func (x *WebClaimRsp) GetCards() []*game_api.TreasureCard {
	if x != nil {
		return x.Cards
	}
	return nil
}

func (x *WebClaimRsp) GetRewards() []*game_api.RewardItem {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type WebRanksReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId  string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`   // 可以放在header里,不显式传
	AppId   string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`     // 可以放在header里,不显式传
	BindKey string `protobuf:"bytes,3,opt,name=bindKey,proto3" json:"bindKey,omitempty"` // 活动key
}

func (x *WebRanksReq) Reset() {
	*x = WebRanksReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebRanksReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebRanksReq) ProtoMessage() {}

func (x *WebRanksReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebRanksReq.ProtoReflect.Descriptor instead.
func (*WebRanksReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescGZIP(), []int{4}
}

func (x *WebRanksReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *WebRanksReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *WebRanksReq) GetBindKey() string {
	if x != nil {
		return x.BindKey
	}
	return ""
}

type WebRankItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId   string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	Rank     uint32 `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`         // 排名
	Score    uint32 `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`       // 分数,杯子数
	Nick     string `protobuf:"bytes,4,opt,name=nick,proto3" json:"nick,omitempty"`          // 昵称
	UserType uint32 `protobuf:"varint,5,opt,name=userType,proto3" json:"userType,omitempty"` // 用户类型(1正常用户 2机器人)
	Avatar   string `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty"`      // 头像
	Private  uint32 `protobuf:"varint,7,opt,name=private,proto3" json:"private,omitempty"`   // 私密状态, 为1时不能跳转平台个人主页
	Uid      string `protobuf:"bytes,8,opt,name=uid,proto3" json:"uid,omitempty"`            // 平台uid
}

func (x *WebRankItem) Reset() {
	*x = WebRankItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebRankItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebRankItem) ProtoMessage() {}

func (x *WebRankItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebRankItem.ProtoReflect.Descriptor instead.
func (*WebRankItem) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescGZIP(), []int{5}
}

func (x *WebRankItem) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *WebRankItem) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *WebRankItem) GetScore() uint32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *WebRankItem) GetNick() string {
	if x != nil {
		return x.Nick
	}
	return ""
}

func (x *WebRankItem) GetUserType() uint32 {
	if x != nil {
		return x.UserType
	}
	return 0
}

func (x *WebRankItem) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *WebRankItem) GetPrivate() uint32 {
	if x != nil {
		return x.Private
	}
	return 0
}

func (x *WebRankItem) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type WebRanksRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Configs  []*common.RankRewardPackage `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`    // 奖励配置
	Items    []*WebRankItem              `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`        // 排行榜信息
	Capacity uint32                      `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"` // 榜单容量
}

func (x *WebRanksRsp) Reset() {
	*x = WebRanksRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebRanksRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebRanksRsp) ProtoMessage() {}

func (x *WebRanksRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebRanksRsp.ProtoReflect.Descriptor instead.
func (*WebRanksRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescGZIP(), []int{6}
}

func (x *WebRanksRsp) GetConfigs() []*common.RankRewardPackage {
	if x != nil {
		return x.Configs
	}
	return nil
}

func (x *WebRanksRsp) GetItems() []*WebRankItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *WebRanksRsp) GetCapacity() uint32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

var File_pb_game_cwsx_kings_cup_webapi_webapi_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x6b,
	0x69, 0x6e, 0x67, 0x73, 0x5f, 0x63, 0x75, 0x70, 0x2f, 0x77, 0x65, 0x62, 0x61, 0x70, 0x69, 0x2f,
	0x77, 0x65, 0x62, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70,
	0x1a, 0x16, 0x70, 0x62, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x63, 0x75, 0x70, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x7b, 0x0a, 0x0b, 0x57, 0x65, 0x62, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65,
	0x64, 0x42, 0x69, 0x6e, 0x64, 0x4b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x63, 0x61, 0x63, 0x68, 0x65, 0x64, 0x42, 0x69, 0x6e, 0x64, 0x4b, 0x65, 0x79, 0x22, 0x93, 0x02,
	0x0a, 0x0b, 0x57, 0x65, 0x62, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a,
	0x07, 0x62, 0x69, 0x6e, 0x64, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x62, 0x69, 0x6e, 0x64, 0x4b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x75, 0x70, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x75, 0x70, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x72,
	0x61, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x3b, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x47, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x61, 0x6e, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a,
	0x0d, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x4b, 0x65, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x42, 0x69, 0x6e, 0x64,
	0x4b, 0x65, 0x79, 0x22, 0x7d, 0x0a, 0x0b, 0x57, 0x65, 0x62, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52,
	0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x62, 0x69, 0x6e, 0x64, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x62, 0x69, 0x6e, 0x64, 0x4b, 0x65, 0x79, 0x12, 0x26, 0x0a, 0x06, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x22, 0x6b, 0x0a, 0x0b, 0x57, 0x65, 0x62, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x73,
	0x70, 0x12, 0x2c, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x72, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x12,
	0x2e, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22,
	0x55, 0x0a, 0x0b, 0x57, 0x65, 0x62, 0x52, 0x61, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x62, 0x69, 0x6e, 0x64, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62,
	0x69, 0x6e, 0x64, 0x4b, 0x65, 0x79, 0x22, 0xc3, 0x01, 0x0a, 0x0b, 0x57, 0x65, 0x62, 0x52, 0x61,
	0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61,
	0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x69, 0x63, 0x6b,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x69, 0x63, 0x6b, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0xa1, 0x01, 0x0a,
	0x0b, 0x57, 0x65, 0x62, 0x52, 0x61, 0x6e, 0x6b, 0x73, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63,
	0x75, 0x70, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x35, 0x0a,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75,
	0x70, 0x2e, 0x57, 0x65, 0x62, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79,
	0x32, 0xac, 0x02, 0x0a, 0x06, 0x57, 0x65, 0x62, 0x41, 0x70, 0x69, 0x12, 0x49, 0x0a, 0x05, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x12, 0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x05, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x12,
	0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67,
	0x73, 0x63, 0x75, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x71,
	0x1a, 0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e,
	0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x73,
	0x70, 0x12, 0x49, 0x0a, 0x05, 0x52, 0x61, 0x6e, 0x6b, 0x73, 0x12, 0x1f, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e,
	0x57, 0x65, 0x62, 0x52, 0x61, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70,
	0x2e, 0x57, 0x65, 0x62, 0x52, 0x61, 0x6e, 0x6b, 0x73, 0x52, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x0d,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x2e,
	0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x42,
	0x52, 0x5a, 0x50, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x63, 0x75, 0x70, 0x2f, 0x77, 0x65, 0x62,
	0x61, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescOnce sync.Once
	file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescData = file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDesc
)

func file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescGZIP() []byte {
	file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescOnce.Do(func() {
		file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescData)
	})
	return file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDescData
}

var file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_pb_game_cwsx_kings_cup_webapi_webapi_proto_goTypes = []interface{}{
	(*WebQueryReq)(nil),               // 0: game_cwsx_kingscup.WebQueryReq
	(*WebQueryRsp)(nil),               // 1: game_cwsx_kingscup.WebQueryRsp
	(*WebClaimReq)(nil),               // 2: game_cwsx_kingscup.WebClaimReq
	(*WebClaimRsp)(nil),               // 3: game_cwsx_kingscup.WebClaimRsp
	(*WebRanksReq)(nil),               // 4: game_cwsx_kingscup.WebRanksReq
	(*WebRankItem)(nil),               // 5: game_cwsx_kingscup.WebRankItem
	(*WebRanksRsp)(nil),               // 6: game_cwsx_kingscup.WebRanksRsp
	(common.UserActivityState)(0),     // 7: game_cwsx_kingscup.UserActivityState
	(common.UserActivityRankState)(0), // 8: game_cwsx_kingscup.UserActivityRankState
	(*device.Device)(nil),             // 9: device.Device
	(*game_api.TreasureCard)(nil),     // 10: game_api.TreasureCard
	(*game_api.RewardItem)(nil),       // 11: game_api.RewardItem
	(*common.RankRewardPackage)(nil),  // 12: game_cwsx_kingscup.RankRewardPackage
	(*inlet.ActivityStateReq)(nil),    // 13: inlet.ActivityStateReq
	(*inlet.ActivityStateRsp)(nil),    // 14: inlet.ActivityStateRsp
}
var file_pb_game_cwsx_kings_cup_webapi_webapi_proto_depIdxs = []int32{
	7,  // 0: game_cwsx_kingscup.WebQueryRsp.state:type_name -> game_cwsx_kingscup.UserActivityState
	8,  // 1: game_cwsx_kingscup.WebQueryRsp.rankState:type_name -> game_cwsx_kingscup.UserActivityRankState
	9,  // 2: game_cwsx_kingscup.WebClaimReq.device:type_name -> device.Device
	10, // 3: game_cwsx_kingscup.WebClaimRsp.cards:type_name -> game_api.TreasureCard
	11, // 4: game_cwsx_kingscup.WebClaimRsp.rewards:type_name -> game_api.RewardItem
	12, // 5: game_cwsx_kingscup.WebRanksRsp.configs:type_name -> game_cwsx_kingscup.RankRewardPackage
	5,  // 6: game_cwsx_kingscup.WebRanksRsp.items:type_name -> game_cwsx_kingscup.WebRankItem
	0,  // 7: game_cwsx_kingscup.WebApi.Query:input_type -> game_cwsx_kingscup.WebQueryReq
	2,  // 8: game_cwsx_kingscup.WebApi.Claim:input_type -> game_cwsx_kingscup.WebClaimReq
	4,  // 9: game_cwsx_kingscup.WebApi.Ranks:input_type -> game_cwsx_kingscup.WebRanksReq
	13, // 10: game_cwsx_kingscup.WebApi.ActivityState:input_type -> inlet.ActivityStateReq
	1,  // 11: game_cwsx_kingscup.WebApi.Query:output_type -> game_cwsx_kingscup.WebQueryRsp
	3,  // 12: game_cwsx_kingscup.WebApi.Claim:output_type -> game_cwsx_kingscup.WebClaimRsp
	6,  // 13: game_cwsx_kingscup.WebApi.Ranks:output_type -> game_cwsx_kingscup.WebRanksRsp
	14, // 14: game_cwsx_kingscup.WebApi.ActivityState:output_type -> inlet.ActivityStateRsp
	11, // [11:15] is the sub-list for method output_type
	7,  // [7:11] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_kings_cup_webapi_webapi_proto_init() }
func file_pb_game_cwsx_kings_cup_webapi_webapi_proto_init() {
	if File_pb_game_cwsx_kings_cup_webapi_webapi_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebQueryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebClaimReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebClaimRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebRanksReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebRankItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebRanksRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_cwsx_kings_cup_webapi_webapi_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_kings_cup_webapi_webapi_proto_depIdxs,
		MessageInfos:      file_pb_game_cwsx_kings_cup_webapi_webapi_proto_msgTypes,
	}.Build()
	File_pb_game_cwsx_kings_cup_webapi_webapi_proto = out.File
	file_pb_game_cwsx_kings_cup_webapi_webapi_proto_rawDesc = nil
	file_pb_game_cwsx_kings_cup_webapi_webapi_proto_goTypes = nil
	file_pb_game_cwsx_kings_cup_webapi_webapi_proto_depIdxs = nil
}
