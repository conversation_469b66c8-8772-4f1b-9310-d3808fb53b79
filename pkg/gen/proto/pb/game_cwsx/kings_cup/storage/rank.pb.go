// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/kings_cup/storage/rank.proto

package storage

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/kings_cup/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UserStageLog
type UserStageLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts        uint64 `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`               // 闯关时间
	StageId   uint32 `protobuf:"varint,2,opt,name=stageId,proto3" json:"stageId,omitempty"`     // 关卡id
	StageType uint32 `protobuf:"varint,3,opt,name=stageType,proto3" json:"stageType,omitempty"` // 关卡类型
	Progress  uint32 `protobuf:"varint,4,opt,name=progress,proto3" json:"progress,omitempty"`   // 分数进度
	RoundKind uint32 `protobuf:"varint,5,opt,name=roundKind,proto3" json:"roundKind,omitempty"` // 关卡所处的榜单归档
}

func (x *UserStageLog) Reset() {
	*x = UserStageLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserStageLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStageLog) ProtoMessage() {}

func (x *UserStageLog) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStageLog.ProtoReflect.Descriptor instead.
func (*UserStageLog) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDescGZIP(), []int{0}
}

func (x *UserStageLog) GetTs() uint64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *UserStageLog) GetStageId() uint32 {
	if x != nil {
		return x.StageId
	}
	return 0
}

func (x *UserStageLog) GetStageType() uint32 {
	if x != nil {
		return x.StageType
	}
	return 0
}

func (x *UserStageLog) GetProgress() uint32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *UserStageLog) GetRoundKind() uint32 {
	if x != nil {
		return x.RoundKind
	}
	return 0
}

// UserRankLog
type UserRankLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindKey     string                `protobuf:"bytes,1,opt,name=bindKey,proto3" json:"bindKey,omitempty"`
	Score       uint32                `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`                                                  // 分数
	ClaimState  common.UserClaimState `protobuf:"varint,3,opt,name=claimState,proto3,enum=game_cwsx_kingscup.UserClaimState" json:"claimState,omitempty"` // 领取状态
	StageLog    []*UserStageLog       `protobuf:"bytes,4,rep,name=stageLog,proto3" json:"stageLog,omitempty"`                                             // 闯关记录
	ClaimTime   int64                 `protobuf:"varint,5,opt,name=claimTime,proto3" json:"claimTime,omitempty"`                                          // 领取时间
	UpdateTime  int64                 `protobuf:"varint,6,opt,name=updateTime,proto3" json:"updateTime,omitempty"`                                        // 更新时间
	JoinTransId string                `protobuf:"bytes,7,opt,name=joinTransId,proto3" json:"joinTransId,omitempty"`                                       // 加入榜单时发放奖励
	JoinTime    int64                 `protobuf:"varint,8,opt,name=joinTime,proto3" json:"joinTime,omitempty"`                                            // 加入榜单时间
}

func (x *UserRankLog) Reset() {
	*x = UserRankLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserRankLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRankLog) ProtoMessage() {}

func (x *UserRankLog) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRankLog.ProtoReflect.Descriptor instead.
func (*UserRankLog) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDescGZIP(), []int{1}
}

func (x *UserRankLog) GetBindKey() string {
	if x != nil {
		return x.BindKey
	}
	return ""
}

func (x *UserRankLog) GetScore() uint32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *UserRankLog) GetClaimState() common.UserClaimState {
	if x != nil {
		return x.ClaimState
	}
	return common.UserClaimState(0)
}

func (x *UserRankLog) GetStageLog() []*UserStageLog {
	if x != nil {
		return x.StageLog
	}
	return nil
}

func (x *UserRankLog) GetClaimTime() int64 {
	if x != nil {
		return x.ClaimTime
	}
	return 0
}

func (x *UserRankLog) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *UserRankLog) GetJoinTransId() string {
	if x != nil {
		return x.JoinTransId
	}
	return ""
}

func (x *UserRankLog) GetJoinTime() int64 {
	if x != nil {
		return x.JoinTime
	}
	return 0
}

// UserRank
type UserRank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId     string          `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`                                       // 用户
	UserType   common.UserType `protobuf:"varint,2,opt,name=userType,proto3,enum=game_cwsx_kingscup.UserType" json:"userType,omitempty"` // 用户类型
	Ranks      []*UserRankLog  `protobuf:"bytes,3,rep,name=ranks,proto3" json:"ranks,omitempty"`                                         // 参与记录
	UpdateTime int64           `protobuf:"varint,4,opt,name=updateTime,proto3" json:"updateTime,omitempty"`                              // 更新时间
}

func (x *UserRank) Reset() {
	*x = UserRank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRank) ProtoMessage() {}

func (x *UserRank) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRank.ProtoReflect.Descriptor instead.
func (*UserRank) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDescGZIP(), []int{2}
}

func (x *UserRank) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *UserRank) GetUserType() common.UserType {
	if x != nil {
		return x.UserType
	}
	return common.UserType(0)
}

func (x *UserRank) GetRanks() []*UserRankLog {
	if x != nil {
		return x.Ranks
	}
	return nil
}

func (x *UserRank) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

// RankMember 排行榜成员
type RankMember struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId   string          `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`                                       // 用户Id
	UserType common.UserType `protobuf:"varint,2,opt,name=userType,proto3,enum=game_cwsx_kingscup.UserType" json:"userType,omitempty"` // 用户类型
	Cups     uint32          `protobuf:"varint,3,opt,name=cups,proto3" json:"cups,omitempty"`                                          // 杯子数
	Ts       int64           `protobuf:"varint,4,opt,name=ts,proto3" json:"ts,omitempty"`                                              // 时间戳
}

func (x *RankMember) Reset() {
	*x = RankMember{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankMember) ProtoMessage() {}

func (x *RankMember) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankMember.ProtoReflect.Descriptor instead.
func (*RankMember) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDescGZIP(), []int{3}
}

func (x *RankMember) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *RankMember) GetUserType() common.UserType {
	if x != nil {
		return x.UserType
	}
	return common.UserType(0)
}

func (x *RankMember) GetCups() uint32 {
	if x != nil {
		return x.Cups
	}
	return 0
}

func (x *RankMember) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

// Rank 排行榜
type Rank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Members []*RankMember `protobuf:"bytes,1,rep,name=members,proto3" json:"members,omitempty"` // 排行榜成员
}

func (x *Rank) Reset() {
	*x = Rank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rank) ProtoMessage() {}

func (x *Rank) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rank.ProtoReflect.Descriptor instead.
func (*Rank) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDescGZIP(), []int{4}
}

func (x *Rank) GetMembers() []*RankMember {
	if x != nil {
		return x.Members
	}
	return nil
}

type RankIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Idx uint32 `protobuf:"varint,1,opt,name=idx,proto3" json:"idx,omitempty"` // 索引
	Num uint32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"` // 人数
}

func (x *RankIndex) Reset() {
	*x = RankIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankIndex) ProtoMessage() {}

func (x *RankIndex) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankIndex.ProtoReflect.Descriptor instead.
func (*RankIndex) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDescGZIP(), []int{5}
}

func (x *RankIndex) GetIdx() uint32 {
	if x != nil {
		return x.Idx
	}
	return 0
}

func (x *RankIndex) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

var File_pb_game_cwsx_kings_cup_storage_rank_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDesc = []byte{
	0x0a, 0x29, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x6b,
	0x69, 0x6e, 0x67, 0x73, 0x5f, 0x63, 0x75, 0x70, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x1a,
	0x2a, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x6b, 0x69,
	0x6e, 0x67, 0x73, 0x5f, 0x63, 0x75, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x90, 0x01, 0x0a, 0x0c,
	0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x12, 0x0e, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x74, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x1c, 0x0a, 0x09, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x4b, 0x69, 0x6e, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x4b, 0x69, 0x6e, 0x64, 0x22, 0xbb,
	0x02, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x6f, 0x67, 0x12, 0x18,
	0x0a, 0x07, 0x62, 0x69, 0x6e, 0x64, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x62, 0x69, 0x6e, 0x64, 0x4b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x42,
	0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x22, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b,
	0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6c, 0x61, 0x69,
	0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x08, 0x73, 0x74, 0x61, 0x67, 0x65, 0x4c, 0x6f, 0x67,
	0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x6a, 0x6f, 0x69, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6a, 0x6f, 0x69, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x6a, 0x6f, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x6a, 0x6f, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xb3, 0x01, 0x0a,
	0x08, 0x55, 0x73, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x38, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x05, 0x72,
	0x61, 0x6e, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x6f, 0x67, 0x52, 0x05, 0x72, 0x61, 0x6e,
	0x6b, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x0a, 0x52, 0x61, 0x6e, 0x6b, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x63, 0x75, 0x70,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x75, 0x70, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x63, 0x75, 0x70, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x22, 0x40, 0x0a, 0x04, 0x52, 0x61, 0x6e, 0x6b, 0x12,
	0x38, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x6b, 0x69, 0x6e,
	0x67, 0x73, 0x63, 0x75, 0x70, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x22, 0x2f, 0x0a, 0x09, 0x52, 0x61, 0x6e,
	0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x69, 0x64, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x42, 0x53, 0x5a, 0x51, 0x74, 0x63,
	0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e,
	0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x6b, 0x69,
	0x6e, 0x67, 0x73, 0x5f, 0x63, 0x75, 0x70, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDescOnce sync.Once
	file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDescData = file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDesc
)

func file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDescGZIP() []byte {
	file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDescOnce.Do(func() {
		file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDescData)
	})
	return file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDescData
}

var file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pb_game_cwsx_kings_cup_storage_rank_proto_goTypes = []interface{}{
	(*UserStageLog)(nil),       // 0: game_cwsx_kingscup.UserStageLog
	(*UserRankLog)(nil),        // 1: game_cwsx_kingscup.UserRankLog
	(*UserRank)(nil),           // 2: game_cwsx_kingscup.UserRank
	(*RankMember)(nil),         // 3: game_cwsx_kingscup.RankMember
	(*Rank)(nil),               // 4: game_cwsx_kingscup.Rank
	(*RankIndex)(nil),          // 5: game_cwsx_kingscup.RankIndex
	(common.UserClaimState)(0), // 6: game_cwsx_kingscup.UserClaimState
	(common.UserType)(0),       // 7: game_cwsx_kingscup.UserType
}
var file_pb_game_cwsx_kings_cup_storage_rank_proto_depIdxs = []int32{
	6, // 0: game_cwsx_kingscup.UserRankLog.claimState:type_name -> game_cwsx_kingscup.UserClaimState
	0, // 1: game_cwsx_kingscup.UserRankLog.stageLog:type_name -> game_cwsx_kingscup.UserStageLog
	7, // 2: game_cwsx_kingscup.UserRank.userType:type_name -> game_cwsx_kingscup.UserType
	1, // 3: game_cwsx_kingscup.UserRank.ranks:type_name -> game_cwsx_kingscup.UserRankLog
	7, // 4: game_cwsx_kingscup.RankMember.userType:type_name -> game_cwsx_kingscup.UserType
	3, // 5: game_cwsx_kingscup.Rank.members:type_name -> game_cwsx_kingscup.RankMember
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_kings_cup_storage_rank_proto_init() }
func file_pb_game_cwsx_kings_cup_storage_rank_proto_init() {
	if File_pb_game_cwsx_kings_cup_storage_rank_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserStageLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserRankLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserRank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankMember); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_game_cwsx_kings_cup_storage_rank_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_kings_cup_storage_rank_proto_depIdxs,
		MessageInfos:      file_pb_game_cwsx_kings_cup_storage_rank_proto_msgTypes,
	}.Build()
	File_pb_game_cwsx_kings_cup_storage_rank_proto = out.File
	file_pb_game_cwsx_kings_cup_storage_rank_proto_rawDesc = nil
	file_pb_game_cwsx_kings_cup_storage_rank_proto_goTypes = nil
	file_pb_game_cwsx_kings_cup_storage_rank_proto_depIdxs = nil
}
