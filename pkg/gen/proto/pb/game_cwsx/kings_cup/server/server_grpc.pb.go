// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_cwsx/kings_cup/server/server.proto

package server

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	game_api "kugou_adapter_service/pkg/gen/proto/pb/game_api"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Server_AddProgress_FullMethodName    = "/game_cwsx_kingscup.Server/AddProgress"
	Server_Claim_FullMethodName          = "/game_cwsx_kingscup.Server/Claim"
	Server_Clean_FullMethodName          = "/game_cwsx_kingscup.Server/Clean"
	Server_RetentionPopup_FullMethodName = "/game_cwsx_kingscup.Server/RetentionPopup"
)

// ServerClient is the client API for Server service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServerClient interface {
	AddProgress(ctx context.Context, in *AddProgressReq, opts ...grpc.CallOption) (*AddProgressRsp, error)
	Claim(ctx context.Context, in *ClaimReq, opts ...grpc.CallOption) (*ClaimRsp, error)
	Clean(ctx context.Context, in *CleanReq, opts ...grpc.CallOption) (*CleanRsp, error)
	RetentionPopup(ctx context.Context, in *game_api.RetentionPopupReq, opts ...grpc.CallOption) (*game_api.RetentionPopupRsp, error)
}

type serverClient struct {
	cc grpc.ClientConnInterface
}

func NewServerClient(cc grpc.ClientConnInterface) ServerClient {
	return &serverClient{cc}
}

func (c *serverClient) AddProgress(ctx context.Context, in *AddProgressReq, opts ...grpc.CallOption) (*AddProgressRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddProgressRsp)
	err := c.cc.Invoke(ctx, Server_AddProgress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverClient) Claim(ctx context.Context, in *ClaimReq, opts ...grpc.CallOption) (*ClaimRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ClaimRsp)
	err := c.cc.Invoke(ctx, Server_Claim_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverClient) Clean(ctx context.Context, in *CleanReq, opts ...grpc.CallOption) (*CleanRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CleanRsp)
	err := c.cc.Invoke(ctx, Server_Clean_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverClient) RetentionPopup(ctx context.Context, in *game_api.RetentionPopupReq, opts ...grpc.CallOption) (*game_api.RetentionPopupRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(game_api.RetentionPopupRsp)
	err := c.cc.Invoke(ctx, Server_RetentionPopup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerServer is the server API for Server service.
// All implementations should embed UnimplementedServerServer
// for forward compatibility
type ServerServer interface {
	AddProgress(context.Context, *AddProgressReq) (*AddProgressRsp, error)
	Claim(context.Context, *ClaimReq) (*ClaimRsp, error)
	Clean(context.Context, *CleanReq) (*CleanRsp, error)
	RetentionPopup(context.Context, *game_api.RetentionPopupReq) (*game_api.RetentionPopupRsp, error)
}

// UnimplementedServerServer should be embedded to have forward compatible implementations.
type UnimplementedServerServer struct {
}

func (UnimplementedServerServer) AddProgress(context.Context, *AddProgressReq) (*AddProgressRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddProgress not implemented")
}
func (UnimplementedServerServer) Claim(context.Context, *ClaimReq) (*ClaimRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Claim not implemented")
}
func (UnimplementedServerServer) Clean(context.Context, *CleanReq) (*CleanRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Clean not implemented")
}
func (UnimplementedServerServer) RetentionPopup(context.Context, *game_api.RetentionPopupReq) (*game_api.RetentionPopupRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetentionPopup not implemented")
}

// UnsafeServerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerServer will
// result in compilation errors.
type UnsafeServerServer interface {
	mustEmbedUnimplementedServerServer()
}

func RegisterServerServer(s grpc.ServiceRegistrar, srv ServerServer) {
	s.RegisterService(&Server_ServiceDesc, srv)
}

func _Server_AddProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddProgressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServer).AddProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Server_AddProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServer).AddProgress(ctx, req.(*AddProgressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Server_Claim_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClaimReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServer).Claim(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Server_Claim_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServer).Claim(ctx, req.(*ClaimReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Server_Clean_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServer).Clean(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Server_Clean_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServer).Clean(ctx, req.(*CleanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Server_RetentionPopup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_api.RetentionPopupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServer).RetentionPopup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Server_RetentionPopup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServer).RetentionPopup(ctx, req.(*game_api.RetentionPopupReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Server_ServiceDesc is the grpc.ServiceDesc for Server service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Server_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game_cwsx_kingscup.Server",
	HandlerType: (*ServerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddProgress",
			Handler:    _Server_AddProgress_Handler,
		},
		{
			MethodName: "Claim",
			Handler:    _Server_Claim_Handler,
		},
		{
			MethodName: "Clean",
			Handler:    _Server_Clean_Handler,
		},
		{
			MethodName: "RetentionPopup",
			Handler:    _Server_RetentionPopup_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_cwsx/kings_cup/server/server.proto",
}
