// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_cwsx/cert/cert.proto

package cert

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	AntiAddiction_Check_FullMethodName = "/game.AntiAddiction/Check"
)

// AntiAddictionClient is the client API for AntiAddiction service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AntiAddictionClient interface {
	Check(ctx context.Context, in *CheckReq, opts ...grpc.CallOption) (*CheckRsp, error)
}

type antiAddictionClient struct {
	cc grpc.ClientConnInterface
}

func NewAntiAddictionClient(cc grpc.ClientConnInterface) AntiAddictionClient {
	return &antiAddictionClient{cc}
}

func (c *antiAddictionClient) Check(ctx context.Context, in *CheckReq, opts ...grpc.CallOption) (*CheckRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckRsp)
	err := c.cc.Invoke(ctx, AntiAddiction_Check_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AntiAddictionServer is the server API for AntiAddiction service.
// All implementations should embed UnimplementedAntiAddictionServer
// for forward compatibility
type AntiAddictionServer interface {
	Check(context.Context, *CheckReq) (*CheckRsp, error)
}

// UnimplementedAntiAddictionServer should be embedded to have forward compatible implementations.
type UnimplementedAntiAddictionServer struct {
}

func (UnimplementedAntiAddictionServer) Check(context.Context, *CheckReq) (*CheckRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Check not implemented")
}

// UnsafeAntiAddictionServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AntiAddictionServer will
// result in compilation errors.
type UnsafeAntiAddictionServer interface {
	mustEmbedUnimplementedAntiAddictionServer()
}

func RegisterAntiAddictionServer(s grpc.ServiceRegistrar, srv AntiAddictionServer) {
	s.RegisterService(&AntiAddiction_ServiceDesc, srv)
}

func _AntiAddiction_Check_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiAddictionServer).Check(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AntiAddiction_Check_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiAddictionServer).Check(ctx, req.(*CheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AntiAddiction_ServiceDesc is the grpc.ServiceDesc for AntiAddiction service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AntiAddiction_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game.AntiAddiction",
	HandlerType: (*AntiAddictionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Check",
			Handler:    _AntiAddiction_Check_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_cwsx/cert/cert.proto",
}
