// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/adapter_task/comm.proto

package adapter_task

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// TaskType 任务类型
type TaskType int32

const (
	TaskType_Normal    TaskType = 0 // 普通任务
	TaskType_Custom    TaskType = 1 // 自定义任务
	TaskType_Diversion TaskType = 2 // 导流任务
	TaskType_Step      TaskType = 3 // 阶梯任务
	TaskType_Sign      TaskType = 4 // 签到任务
)

// Enum value maps for TaskType.
var (
	TaskType_name = map[int32]string{
		0: "Normal",
		1: "Custom",
		2: "Diversion",
		3: "Step",
		4: "Sign",
	}
	TaskType_value = map[string]int32{
		"Normal":    0,
		"Custom":    1,
		"Diversion": 2,
		"Step":      3,
		"Sign":      4,
	}
)

func (x TaskType) Enum() *TaskType {
	p := new(TaskType)
	*p = x
	return p
}

func (x TaskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_task_comm_proto_enumTypes[0].Descriptor()
}

func (TaskType) Type() protoreflect.EnumType {
	return &file_pb_adapter_task_comm_proto_enumTypes[0]
}

func (x TaskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskType.Descriptor instead.
func (TaskType) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_task_comm_proto_rawDescGZIP(), []int{0}
}

// TaskState 任务状态
type TaskState int32

const (
	TaskState_TaskStateDefault   TaskState = 0 // 未完成, 未领取
	TaskState_TaskStateAvailable TaskState = 1 // 已完成, 可领取奖励, 未领取奖励
	TaskState_TaskStateCompelete TaskState = 2 // 已完成, 可领取奖励, 已领取奖励
	TaskState_TaskStateHidden    TaskState = 3 // 隐藏
	TaskState_TaskStateFinish    TaskState = 4 // 已完成,不可领取奖励
	TaskState_TaskStateOver      TaskState = 5 // 已结束（eg：非循环签到任务已结束，仅可展示）
)

// Enum value maps for TaskState.
var (
	TaskState_name = map[int32]string{
		0: "TaskStateDefault",
		1: "TaskStateAvailable",
		2: "TaskStateCompelete",
		3: "TaskStateHidden",
		4: "TaskStateFinish",
		5: "TaskStateOver",
	}
	TaskState_value = map[string]int32{
		"TaskStateDefault":   0,
		"TaskStateAvailable": 1,
		"TaskStateCompelete": 2,
		"TaskStateHidden":    3,
		"TaskStateFinish":    4,
		"TaskStateOver":      5,
	}
)

func (x TaskState) Enum() *TaskState {
	p := new(TaskState)
	*p = x
	return p
}

func (x TaskState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskState) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_task_comm_proto_enumTypes[1].Descriptor()
}

func (TaskState) Type() protoreflect.EnumType {
	return &file_pb_adapter_task_comm_proto_enumTypes[1]
}

func (x TaskState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskState.Descriptor instead.
func (TaskState) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_task_comm_proto_rawDescGZIP(), []int{1}
}

// TaskClaimState 任务领取状态
type TaskClaimState int32

const (
	TaskClaimState_TaskClaimStateDefault TaskClaimState = 0 // 未领取
	TaskClaimState_TaskClaimStateClaimed TaskClaimState = 1 // 已领取
	TaskClaimState_TaskClaimStateGiveup  TaskClaimState = 2 // 已放弃
	TaskClaimState_TaskClaimStateOver    TaskClaimState = 3 // 不可再接受任务（任务失败）
)

// Enum value maps for TaskClaimState.
var (
	TaskClaimState_name = map[int32]string{
		0: "TaskClaimStateDefault",
		1: "TaskClaimStateClaimed",
		2: "TaskClaimStateGiveup",
		3: "TaskClaimStateOver",
	}
	TaskClaimState_value = map[string]int32{
		"TaskClaimStateDefault": 0,
		"TaskClaimStateClaimed": 1,
		"TaskClaimStateGiveup":  2,
		"TaskClaimStateOver":    3,
	}
)

func (x TaskClaimState) Enum() *TaskClaimState {
	p := new(TaskClaimState)
	*p = x
	return p
}

func (x TaskClaimState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskClaimState) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_task_comm_proto_enumTypes[2].Descriptor()
}

func (TaskClaimState) Type() protoreflect.EnumType {
	return &file_pb_adapter_task_comm_proto_enumTypes[2]
}

func (x TaskClaimState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskClaimState.Descriptor instead.
func (TaskClaimState) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_task_comm_proto_rawDescGZIP(), []int{2}
}

type TaskProgressNodeStatus int32

const (
	TaskProgressNodeStatus_TaskProgressNodeStatusTodo    TaskProgressNodeStatus = 0
	TaskProgressNodeStatus_TaskProgressNodeStatusCanRecv TaskProgressNodeStatus = 1
	TaskProgressNodeStatus_TaskProgressNodeStatusRecved  TaskProgressNodeStatus = 2
)

// Enum value maps for TaskProgressNodeStatus.
var (
	TaskProgressNodeStatus_name = map[int32]string{
		0: "TaskProgressNodeStatusTodo",
		1: "TaskProgressNodeStatusCanRecv",
		2: "TaskProgressNodeStatusRecved",
	}
	TaskProgressNodeStatus_value = map[string]int32{
		"TaskProgressNodeStatusTodo":    0,
		"TaskProgressNodeStatusCanRecv": 1,
		"TaskProgressNodeStatusRecved":  2,
	}
)

func (x TaskProgressNodeStatus) Enum() *TaskProgressNodeStatus {
	p := new(TaskProgressNodeStatus)
	*p = x
	return p
}

func (x TaskProgressNodeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskProgressNodeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_task_comm_proto_enumTypes[3].Descriptor()
}

func (TaskProgressNodeStatus) Type() protoreflect.EnumType {
	return &file_pb_adapter_task_comm_proto_enumTypes[3]
}

func (x TaskProgressNodeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskProgressNodeStatus.Descriptor instead.
func (TaskProgressNodeStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_task_comm_proto_rawDescGZIP(), []int{3}
}

type NormalProperty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title          string          `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`         // 标题
	Desc           string          `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`           // 子标题
	Tag            string          `protobuf:"bytes,3,opt,name=tag,proto3" json:"tag,omitempty"`             // tag标签
	TagColor       string          `protobuf:"bytes,4,opt,name=tagColor,proto3" json:"tagColor,omitempty"`   // tag文案颜色
	Button         string          `protobuf:"bytes,5,opt,name=button,proto3" json:"button,omitempty"`       // 按钮文案
	ButtonUrl      string          `protobuf:"bytes,6,opt,name=buttonUrl,proto3" json:"buttonUrl,omitempty"` // 跳转连接
	Icon           string          `protobuf:"bytes,7,opt,name=icon,proto3" json:"icon,omitempty"`           // 图标
	RewardProgress *RewardProgress `protobuf:"bytes,10,opt,name=rewardProgress,proto3" json:"rewardProgress,omitempty"`
	AchievedTimes  uint32          `protobuf:"varint,11,opt,name=achievedTimes,proto3" json:"achievedTimes,omitempty"` // 达成次数
	TotalTimes     uint32          `protobuf:"varint,12,opt,name=totalTimes,proto3" json:"totalTimes,omitempty"`       // 总次数
}

func (x *NormalProperty) Reset() {
	*x = NormalProperty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_comm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NormalProperty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NormalProperty) ProtoMessage() {}

func (x *NormalProperty) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_comm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NormalProperty.ProtoReflect.Descriptor instead.
func (*NormalProperty) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_comm_proto_rawDescGZIP(), []int{0}
}

func (x *NormalProperty) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *NormalProperty) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *NormalProperty) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *NormalProperty) GetTagColor() string {
	if x != nil {
		return x.TagColor
	}
	return ""
}

func (x *NormalProperty) GetButton() string {
	if x != nil {
		return x.Button
	}
	return ""
}

func (x *NormalProperty) GetButtonUrl() string {
	if x != nil {
		return x.ButtonUrl
	}
	return ""
}

func (x *NormalProperty) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *NormalProperty) GetRewardProgress() *RewardProgress {
	if x != nil {
		return x.RewardProgress
	}
	return nil
}

func (x *NormalProperty) GetAchievedTimes() uint32 {
	if x != nil {
		return x.AchievedTimes
	}
	return 0
}

func (x *NormalProperty) GetTotalTimes() uint32 {
	if x != nil {
		return x.TotalTimes
	}
	return 0
}

type RewardProgressDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Icon string `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	Desc string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
}

func (x *RewardProgressDetail) Reset() {
	*x = RewardProgressDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_comm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardProgressDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardProgressDetail) ProtoMessage() {}

func (x *RewardProgressDetail) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_comm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardProgressDetail.ProtoReflect.Descriptor instead.
func (*RewardProgressDetail) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_comm_proto_rawDescGZIP(), []int{1}
}

func (x *RewardProgressDetail) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *RewardProgressDetail) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type RewardProgressNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Threshold uint32                  `protobuf:"varint,1,opt,name=threshold,proto3" json:"threshold,omitempty"`
	Desc      string                  `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	Details   []*RewardProgressDetail `protobuf:"bytes,3,rep,name=details,proto3" json:"details,omitempty"`
	Status    TaskProgressNodeStatus  `protobuf:"varint,4,opt,name=status,proto3,enum=game.TaskProgressNodeStatus" json:"status,omitempty"`
	WelfareId uint32                  `protobuf:"varint,5,opt,name=welfareId,proto3" json:"welfareId,omitempty"`
}

func (x *RewardProgressNode) Reset() {
	*x = RewardProgressNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_comm_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardProgressNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardProgressNode) ProtoMessage() {}

func (x *RewardProgressNode) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_comm_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardProgressNode.ProtoReflect.Descriptor instead.
func (*RewardProgressNode) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_comm_proto_rawDescGZIP(), []int{2}
}

func (x *RewardProgressNode) GetThreshold() uint32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *RewardProgressNode) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *RewardProgressNode) GetDetails() []*RewardProgressDetail {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *RewardProgressNode) GetStatus() TaskProgressNodeStatus {
	if x != nil {
		return x.Status
	}
	return TaskProgressNodeStatus_TaskProgressNodeStatusTodo
}

func (x *RewardProgressNode) GetWelfareId() uint32 {
	if x != nil {
		return x.WelfareId
	}
	return 0
}

type RewardProgress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Current uint32                `protobuf:"varint,1,opt,name=current,proto3" json:"current,omitempty"`
	Max     uint32                `protobuf:"varint,2,opt,name=max,proto3" json:"max,omitempty"`
	Nodes   []*RewardProgressNode `protobuf:"bytes,3,rep,name=nodes,proto3" json:"nodes,omitempty"`
}

func (x *RewardProgress) Reset() {
	*x = RewardProgress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_comm_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardProgress) ProtoMessage() {}

func (x *RewardProgress) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_comm_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardProgress.ProtoReflect.Descriptor instead.
func (*RewardProgress) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_comm_proto_rawDescGZIP(), []int{3}
}

func (x *RewardProgress) GetCurrent() uint32 {
	if x != nil {
		return x.Current
	}
	return 0
}

func (x *RewardProgress) GetMax() uint32 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *RewardProgress) GetNodes() []*RewardProgressNode {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int32             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                                // 任务id
	Typ        TaskType          `protobuf:"varint,2,opt,name=typ,proto3,enum=game.TaskType" json:"typ,omitempty"`                                                                           // 任务类型
	State      TaskState         `protobuf:"varint,3,opt,name=state,proto3,enum=game.TaskState" json:"state,omitempty"`                                                                      // 任务状态
	RewardId   string            `protobuf:"bytes,4,opt,name=rewardId,proto3" json:"rewardId,omitempty"`                                                                                     // 奖励id
	Normal     *NormalProperty   `protobuf:"bytes,5,opt,name=normal,proto3" json:"normal,omitempty"`                                                                                         // 普通属性
	AbTestStr  string            `protobuf:"bytes,6,opt,name=abTestStr,proto3" json:"abTestStr,omitempty"`                                                                                   // abTestStr "newcenternewtask|1_2665_10689"
	MapExt     map[string]string `protobuf:"bytes,7,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 透传字段
	ClaimState TaskClaimState    `protobuf:"varint,8,opt,name=claimState,proto3,enum=game.TaskClaimState" json:"claimState,omitempty"`                                                       // 领取状态
	ClaimTime  int64             `protobuf:"varint,9,opt,name=claimTime,proto3" json:"claimTime,omitempty"`                                                                                  // 领取时间
}

func (x *Task) Reset() {
	*x = Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_comm_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_comm_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_comm_proto_rawDescGZIP(), []int{4}
}

func (x *Task) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Task) GetTyp() TaskType {
	if x != nil {
		return x.Typ
	}
	return TaskType_Normal
}

func (x *Task) GetState() TaskState {
	if x != nil {
		return x.State
	}
	return TaskState_TaskStateDefault
}

func (x *Task) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

func (x *Task) GetNormal() *NormalProperty {
	if x != nil {
		return x.Normal
	}
	return nil
}

func (x *Task) GetAbTestStr() string {
	if x != nil {
		return x.AbTestStr
	}
	return ""
}

func (x *Task) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

func (x *Task) GetClaimState() TaskClaimState {
	if x != nil {
		return x.ClaimState
	}
	return TaskClaimState_TaskClaimStateDefault
}

func (x *Task) GetClaimTime() int64 {
	if x != nil {
		return x.ClaimTime
	}
	return 0
}

// 命中的模块
type TaskModule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`   // 模块名称
	Desc  string  `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`   // 模块描述
	Jump  string  `protobuf:"bytes,3,opt,name=jump,proto3" json:"jump,omitempty"`   // 模块跳转链接
	Tasks []*Task `protobuf:"bytes,4,rep,name=tasks,proto3" json:"tasks,omitempty"` // 任务列表
	Id    int32   `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`      // 模块id
}

func (x *TaskModule) Reset() {
	*x = TaskModule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_comm_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskModule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskModule) ProtoMessage() {}

func (x *TaskModule) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_comm_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskModule.ProtoReflect.Descriptor instead.
func (*TaskModule) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_comm_proto_rawDescGZIP(), []int{5}
}

func (x *TaskModule) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TaskModule) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *TaskModule) GetJump() string {
	if x != nil {
		return x.Jump
	}
	return ""
}

func (x *TaskModule) GetTasks() []*Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *TaskModule) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type Reward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`    // 奖励id
	Num  int32  `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`  // 数量
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"` // 名称
	Icon string `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"` // 图标
}

func (x *Reward) Reset() {
	*x = Reward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_comm_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reward) ProtoMessage() {}

func (x *Reward) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_comm_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reward.ProtoReflect.Descriptor instead.
func (*Reward) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_comm_proto_rawDescGZIP(), []int{6}
}

func (x *Reward) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Reward) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *Reward) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Reward) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

var File_pb_adapter_task_comm_proto protoreflect.FileDescriptor

var file_pb_adapter_task_comm_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x67, 0x61,
	0x6d, 0x65, 0x22, 0xb6, 0x02, 0x0a, 0x0e, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12,
	0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61,
	0x67, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62,
	0x75, 0x74, 0x74, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x55,
	0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e,
	0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x63, 0x68, 0x69, 0x65, 0x76, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x61, 0x63,
	0x68, 0x69, 0x65, 0x76, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x22, 0x3e, 0x0a, 0x14, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0xd0, 0x01, 0x0a, 0x12,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x6f,
	0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x12, 0x34, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x34, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x6f,
	0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1c, 0x0a, 0x09, 0x77, 0x65, 0x6c, 0x66, 0x61, 0x72, 0x65, 0x49, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x77, 0x65, 0x6c, 0x66, 0x61, 0x72, 0x65, 0x49, 0x64, 0x22, 0x6c,
	0x0a, 0x0e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61,
	0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x12, 0x2e, 0x0a, 0x05,
	0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x86, 0x03, 0x0a,
	0x04, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x03, 0x74, 0x79, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x03, 0x74, 0x79, 0x70, 0x12, 0x25, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x06, 0x6e, 0x6f,
	0x72, 0x6d, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x52, 0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x62, 0x54, 0x65,
	0x73, 0x74, 0x53, 0x74, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x62, 0x54,
	0x65, 0x73, 0x74, 0x53, 0x74, 0x72, 0x12, 0x2e, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06,
	0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x12, 0x34, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x0a, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x63, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61,
	0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7a, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6a,
	0x75, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6a, 0x75, 0x6d, 0x70, 0x12,
	0x20, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0a,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b,
	0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x52, 0x0a, 0x06, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e,
	0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x69, 0x63, 0x6f, 0x6e, 0x2a, 0x45, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x0a, 0x0a,
	0x06, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x69, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x74, 0x65, 0x70,
	0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x69, 0x67, 0x6e, 0x10, 0x04, 0x2a, 0x8e, 0x01, 0x0a,
	0x09, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x10, 0x00,
	0x12, 0x16, 0x0a, 0x12, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x10, 0x02,
	0x12, 0x13, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x69, 0x64,
	0x64, 0x65, 0x6e, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x10, 0x05, 0x2a, 0x78, 0x0a,
	0x0e, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x19, 0x0a, 0x15, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x61,
	0x73, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x69,
	0x6d, 0x65, 0x64, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x47, 0x69, 0x76, 0x65, 0x75, 0x70, 0x10, 0x02, 0x12,
	0x16, 0x0a, 0x12, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x4f, 0x76, 0x65, 0x72, 0x10, 0x03, 0x2a, 0x7d, 0x0a, 0x16, 0x54, 0x61, 0x73, 0x6b, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x6f, 0x64, 0x6f, 0x10,
	0x00, 0x12, 0x21, 0x0a, 0x1d, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x61, 0x6e, 0x52, 0x65,
	0x63, 0x76, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x63, 0x76, 0x65, 0x64, 0x10, 0x02, 0x42, 0x44, 0x5a, 0x42, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_adapter_task_comm_proto_rawDescOnce sync.Once
	file_pb_adapter_task_comm_proto_rawDescData = file_pb_adapter_task_comm_proto_rawDesc
)

func file_pb_adapter_task_comm_proto_rawDescGZIP() []byte {
	file_pb_adapter_task_comm_proto_rawDescOnce.Do(func() {
		file_pb_adapter_task_comm_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_adapter_task_comm_proto_rawDescData)
	})
	return file_pb_adapter_task_comm_proto_rawDescData
}

var file_pb_adapter_task_comm_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_pb_adapter_task_comm_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pb_adapter_task_comm_proto_goTypes = []interface{}{
	(TaskType)(0),                // 0: game.TaskType
	(TaskState)(0),               // 1: game.TaskState
	(TaskClaimState)(0),          // 2: game.TaskClaimState
	(TaskProgressNodeStatus)(0),  // 3: game.TaskProgressNodeStatus
	(*NormalProperty)(nil),       // 4: game.NormalProperty
	(*RewardProgressDetail)(nil), // 5: game.RewardProgressDetail
	(*RewardProgressNode)(nil),   // 6: game.RewardProgressNode
	(*RewardProgress)(nil),       // 7: game.RewardProgress
	(*Task)(nil),                 // 8: game.Task
	(*TaskModule)(nil),           // 9: game.TaskModule
	(*Reward)(nil),               // 10: game.Reward
	nil,                          // 11: game.Task.MapExtEntry
}
var file_pb_adapter_task_comm_proto_depIdxs = []int32{
	7,  // 0: game.NormalProperty.rewardProgress:type_name -> game.RewardProgress
	5,  // 1: game.RewardProgressNode.details:type_name -> game.RewardProgressDetail
	3,  // 2: game.RewardProgressNode.status:type_name -> game.TaskProgressNodeStatus
	6,  // 3: game.RewardProgress.nodes:type_name -> game.RewardProgressNode
	0,  // 4: game.Task.typ:type_name -> game.TaskType
	1,  // 5: game.Task.state:type_name -> game.TaskState
	4,  // 6: game.Task.normal:type_name -> game.NormalProperty
	11, // 7: game.Task.mapExt:type_name -> game.Task.MapExtEntry
	2,  // 8: game.Task.claimState:type_name -> game.TaskClaimState
	8,  // 9: game.TaskModule.tasks:type_name -> game.Task
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_pb_adapter_task_comm_proto_init() }
func file_pb_adapter_task_comm_proto_init() {
	if File_pb_adapter_task_comm_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_adapter_task_comm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NormalProperty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_comm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardProgressDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_comm_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardProgressNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_comm_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardProgress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_comm_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_comm_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskModule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_comm_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_adapter_task_comm_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_adapter_task_comm_proto_goTypes,
		DependencyIndexes: file_pb_adapter_task_comm_proto_depIdxs,
		EnumInfos:         file_pb_adapter_task_comm_proto_enumTypes,
		MessageInfos:      file_pb_adapter_task_comm_proto_msgTypes,
	}.Build()
	File_pb_adapter_task_comm_proto = out.File
	file_pb_adapter_task_comm_proto_rawDesc = nil
	file_pb_adapter_task_comm_proto_goTypes = nil
	file_pb_adapter_task_comm_proto_depIdxs = nil
}
