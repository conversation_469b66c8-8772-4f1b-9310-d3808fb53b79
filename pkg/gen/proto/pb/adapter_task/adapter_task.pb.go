// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/adapter_task/adapter_task.proto

package adapter_task

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 登录态数据,直接按照以下field从cookie中获取出来即可
type CookieFileds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrOpenType string `protobuf:"bytes,1,opt,name=strOpenType,proto3" json:"strOpenType,omitempty"` // cookie["opentype"]
	StrOpenKey  string `protobuf:"bytes,2,opt,name=strOpenKey,proto3" json:"strOpenKey,omitempty"`   // cookie["openkey"]
	StrOpenId   string `protobuf:"bytes,3,opt,name=strOpenId,proto3" json:"strOpenId,omitempty"`     // cookie["openid"], 这里非中台openid,如微信openid
}

func (x *CookieFileds) Reset() {
	*x = CookieFileds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CookieFileds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CookieFileds) ProtoMessage() {}

func (x *CookieFileds) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CookieFileds.ProtoReflect.Descriptor instead.
func (*CookieFileds) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{0}
}

func (x *CookieFileds) GetStrOpenType() string {
	if x != nil {
		return x.StrOpenType
	}
	return ""
}

func (x *CookieFileds) GetStrOpenKey() string {
	if x != nil {
		return x.StrOpenKey
	}
	return ""
}

func (x *CookieFileds) GetStrOpenId() string {
	if x != nil {
		return x.StrOpenId
	}
	return ""
}

// QueryReq 查询任务
type QueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    string        `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId   string        `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	SceneId  int32         `protobuf:"varint,3,opt,name=sceneId,proto3" json:"sceneId,omitempty"`   // 场景id
	ModuleId int32         `protobuf:"varint,4,opt,name=moduleId,proto3" json:"moduleId,omitempty"` // 模块id
	Qua      string        `protobuf:"bytes,5,opt,name=qua,proto3" json:"qua,omitempty"`            // qua参数
	Filter   string        `protobuf:"bytes,6,opt,name=filter,proto3" json:"filter,omitempty"`      // 自定义filter过滤
	Ua       string        `protobuf:"bytes,7,opt,name=ua,proto3" json:"ua,omitempty"`              // ua参数
	Cookies  *CookieFileds `protobuf:"bytes,8,opt,name=cookies,proto3" json:"cookies,omitempty"`
}

func (x *QueryReq) Reset() {
	*x = QueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryReq) ProtoMessage() {}

func (x *QueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryReq.ProtoReflect.Descriptor instead.
func (*QueryReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{1}
}

func (x *QueryReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *QueryReq) GetSceneId() int32 {
	if x != nil {
		return x.SceneId
	}
	return 0
}

func (x *QueryReq) GetModuleId() int32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *QueryReq) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *QueryReq) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *QueryReq) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *QueryReq) GetCookies() *CookieFileds {
	if x != nil {
		return x.Cookies
	}
	return nil
}

// QueryRsp 查询任务
type QueryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Module *TaskModule `protobuf:"bytes,1,opt,name=module,proto3" json:"module,omitempty"` // 模块
}

func (x *QueryRsp) Reset() {
	*x = QueryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRsp) ProtoMessage() {}

func (x *QueryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRsp.ProtoReflect.Descriptor instead.
func (*QueryRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{2}
}

func (x *QueryRsp) GetModule() *TaskModule {
	if x != nil {
		return x.Module
	}
	return nil
}

// ClaimReq 领取任务
type ClaimReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string        `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId  string        `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	TaskId  int32         `protobuf:"varint,3,opt,name=taskId,proto3" json:"taskId,omitempty"` // 任务ID
	Qua     string        `protobuf:"bytes,4,opt,name=qua,proto3" json:"qua,omitempty"`        // 设备信息
	Ua      string        `protobuf:"bytes,5,opt,name=ua,proto3" json:"ua,omitempty"`          // ua参数
	Cookies *CookieFileds `protobuf:"bytes,6,opt,name=cookies,proto3" json:"cookies,omitempty"`
}

func (x *ClaimReq) Reset() {
	*x = ClaimReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimReq) ProtoMessage() {}

func (x *ClaimReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimReq.ProtoReflect.Descriptor instead.
func (*ClaimReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{3}
}

func (x *ClaimReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ClaimReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ClaimReq) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *ClaimReq) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *ClaimReq) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *ClaimReq) GetCookies() *CookieFileds {
	if x != nil {
		return x.Cookies
	}
	return nil
}

// ClaimRsp 领取任务
type ClaimRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrMsg string `protobuf:"bytes,1,opt,name=strMsg,proto3" json:"strMsg,omitempty"`
}

func (x *ClaimRsp) Reset() {
	*x = ClaimRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimRsp) ProtoMessage() {}

func (x *ClaimRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimRsp.ProtoReflect.Descriptor instead.
func (*ClaimRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{4}
}

func (x *ClaimRsp) GetStrMsg() string {
	if x != nil {
		return x.StrMsg
	}
	return ""
}

// CompleteReq 完成任务,领取奖励
type CompleteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId           string            `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId          string            `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	TaskId          int32             `protobuf:"varint,3,opt,name=taskId,proto3" json:"taskId,omitempty"` // 任务ID
	Qua             string            `protobuf:"bytes,4,opt,name=qua,proto3" json:"qua,omitempty"`
	RewardRate      int32             `protobuf:"varint,5,opt,name=rewardRate,proto3" json:"rewardRate,omitempty"`  // 领取倍率
	RewardToken     string            `protobuf:"bytes,6,opt,name=rewardToken,proto3" json:"rewardToken,omitempty"` // 校验token
	Ua              string            `protobuf:"bytes,7,opt,name=ua,proto3" json:"ua,omitempty"`                   // userAgent
	Cookies         *CookieFileds     `protobuf:"bytes,8,opt,name=cookies,proto3" json:"cookies,omitempty"`
	NormalTask      bool              `protobuf:"varint,9,opt,name=normalTask,proto3" json:"normalTask,omitempty"`
	MapBusiPassback map[string]string `protobuf:"bytes,10,rep,name=mapBusiPassback,proto3" json:"mapBusiPassback,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 业务透传参数-仅q音
}

func (x *CompleteReq) Reset() {
	*x = CompleteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteReq) ProtoMessage() {}

func (x *CompleteReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteReq.ProtoReflect.Descriptor instead.
func (*CompleteReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{5}
}

func (x *CompleteReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CompleteReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *CompleteReq) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *CompleteReq) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *CompleteReq) GetRewardRate() int32 {
	if x != nil {
		return x.RewardRate
	}
	return 0
}

func (x *CompleteReq) GetRewardToken() string {
	if x != nil {
		return x.RewardToken
	}
	return ""
}

func (x *CompleteReq) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *CompleteReq) GetCookies() *CookieFileds {
	if x != nil {
		return x.Cookies
	}
	return nil
}

func (x *CompleteReq) GetNormalTask() bool {
	if x != nil {
		return x.NormalTask
	}
	return false
}

func (x *CompleteReq) GetMapBusiPassback() map[string]string {
	if x != nil {
		return x.MapBusiPassback
	}
	return nil
}

// CompleteRsp 完成任务,领取奖励
type CompleteRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrMsg  string    `protobuf:"bytes,1,opt,name=strMsg,proto3" json:"strMsg,omitempty"`
	Rewards []*Reward `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"`
}

func (x *CompleteRsp) Reset() {
	*x = CompleteRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteRsp) ProtoMessage() {}

func (x *CompleteRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteRsp.ProtoReflect.Descriptor instead.
func (*CompleteRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{6}
}

func (x *CompleteRsp) GetStrMsg() string {
	if x != nil {
		return x.StrMsg
	}
	return ""
}

func (x *CompleteRsp) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

// GiveupReq 放弃任务
type GiveupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string        `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId  string        `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	TaskId  int32         `protobuf:"varint,3,opt,name=taskId,proto3" json:"taskId,omitempty"` // 任务ID
	Qua     string        `protobuf:"bytes,4,opt,name=qua,proto3" json:"qua,omitempty"`
	Ua      string        `protobuf:"bytes,5,opt,name=ua,proto3" json:"ua,omitempty"` // ua参数
	Cookies *CookieFileds `protobuf:"bytes,6,opt,name=cookies,proto3" json:"cookies,omitempty"`
}

func (x *GiveupReq) Reset() {
	*x = GiveupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiveupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiveupReq) ProtoMessage() {}

func (x *GiveupReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiveupReq.ProtoReflect.Descriptor instead.
func (*GiveupReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{7}
}

func (x *GiveupReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GiveupReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *GiveupReq) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *GiveupReq) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *GiveupReq) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *GiveupReq) GetCookies() *CookieFileds {
	if x != nil {
		return x.Cookies
	}
	return nil
}

// GiveupRsp 放弃任务
type GiveupRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrMsg string `protobuf:"bytes,1,opt,name=strMsg,proto3" json:"strMsg,omitempty"`
}

func (x *GiveupRsp) Reset() {
	*x = GiveupRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiveupRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiveupRsp) ProtoMessage() {}

func (x *GiveupRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiveupRsp.ProtoReflect.Descriptor instead.
func (*GiveupRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{8}
}

func (x *GiveupRsp) GetStrMsg() string {
	if x != nil {
		return x.StrMsg
	}
	return ""
}

type CompleteCheckReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId       string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	Uid         int64  `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	TaskId      int32  `protobuf:"varint,3,opt,name=taskId,proto3" json:"taskId,omitempty"`          // 任务id
	RewardToken string `protobuf:"bytes,4,opt,name=rewardToken,proto3" json:"rewardToken,omitempty"` // token
	RewardRate  int32  `protobuf:"varint,5,opt,name=rewardRate,proto3" json:"rewardRate,omitempty"`  // 领奖倍数
}

func (x *CompleteCheckReq) Reset() {
	*x = CompleteCheckReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteCheckReq) ProtoMessage() {}

func (x *CompleteCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteCheckReq.ProtoReflect.Descriptor instead.
func (*CompleteCheckReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{9}
}

func (x *CompleteCheckReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CompleteCheckReq) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *CompleteCheckReq) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *CompleteCheckReq) GetRewardToken() string {
	if x != nil {
		return x.RewardToken
	}
	return ""
}

func (x *CompleteCheckReq) GetRewardRate() int32 {
	if x != nil {
		return x.RewardRate
	}
	return 0
}

type CompleteCheckRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 校验成功返回0,否则返回其他
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *CompleteCheckRsp) Reset() {
	*x = CompleteCheckRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteCheckRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteCheckRsp) ProtoMessage() {}

func (x *CompleteCheckRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteCheckRsp.ProtoReflect.Descriptor instead.
func (*CompleteCheckRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{10}
}

func (x *CompleteCheckRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CompleteCheckRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CompleteNotifyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId       string            `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	Uid         int64             `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	TaskId      int32             `protobuf:"varint,3,opt,name=taskId,proto3" json:"taskId,omitempty"`                                                                                  // 任务id
	RewardToken string            `protobuf:"bytes,4,opt,name=rewardToken,proto3" json:"rewardToken,omitempty"`                                                                         // token
	RewardRate  int32             `protobuf:"varint,5,opt,name=rewardRate,proto3" json:"rewardRate,omitempty"`                                                                          // 领奖倍数
	Ext         map[string]string `protobuf:"bytes,6,rep,name=ext,proto3" json:"ext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 透传参数
	ConsumeId   string            `protobuf:"bytes,7,opt,name=consumeId,proto3" json:"consumeId,omitempty"`                                                                             // 幂等ID
}

func (x *CompleteNotifyReq) Reset() {
	*x = CompleteNotifyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteNotifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteNotifyReq) ProtoMessage() {}

func (x *CompleteNotifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteNotifyReq.ProtoReflect.Descriptor instead.
func (*CompleteNotifyReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{11}
}

func (x *CompleteNotifyReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CompleteNotifyReq) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *CompleteNotifyReq) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *CompleteNotifyReq) GetRewardToken() string {
	if x != nil {
		return x.RewardToken
	}
	return ""
}

func (x *CompleteNotifyReq) GetRewardRate() int32 {
	if x != nil {
		return x.RewardRate
	}
	return 0
}

func (x *CompleteNotifyReq) GetExt() map[string]string {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *CompleteNotifyReq) GetConsumeId() string {
	if x != nil {
		return x.ConsumeId
	}
	return ""
}

type CompleteNotifyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 校验成功返回0,否则返回其他
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *CompleteNotifyRsp) Reset() {
	*x = CompleteNotifyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteNotifyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteNotifyRsp) ProtoMessage() {}

func (x *CompleteNotifyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteNotifyRsp.ProtoReflect.Descriptor instead.
func (*CompleteNotifyRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{12}
}

func (x *CompleteNotifyRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CompleteNotifyRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type QueryStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	TaskId int32  `protobuf:"varint,3,opt,name=taskId,proto3" json:"taskId,omitempty"`
}

func (x *QueryStatusReq) Reset() {
	*x = QueryStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStatusReq) ProtoMessage() {}

func (x *QueryStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStatusReq.ProtoReflect.Descriptor instead.
func (*QueryStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{13}
}

func (x *QueryStatusReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryStatusReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *QueryStatusReq) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

type QueryStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Task *Task `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
}

func (x *QueryStatusRsp) Reset() {
	*x = QueryStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStatusRsp) ProtoMessage() {}

func (x *QueryStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStatusRsp.ProtoReflect.Descriptor instead.
func (*QueryStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{14}
}

func (x *QueryStatusRsp) GetTask() *Task {
	if x != nil {
		return x.Task
	}
	return nil
}

type CompleteCheckCallbackReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId      string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	TaskId      int32  `protobuf:"varint,2,opt,name=taskId,proto3" json:"taskId,omitempty"`          // 任务id
	RewardToken string `protobuf:"bytes,3,opt,name=rewardToken,proto3" json:"rewardToken,omitempty"` // token
	RewardRate  int32  `protobuf:"varint,4,opt,name=rewardRate,proto3" json:"rewardRate,omitempty"`  // 领奖倍数
}

func (x *CompleteCheckCallbackReq) Reset() {
	*x = CompleteCheckCallbackReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteCheckCallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteCheckCallbackReq) ProtoMessage() {}

func (x *CompleteCheckCallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteCheckCallbackReq.ProtoReflect.Descriptor instead.
func (*CompleteCheckCallbackReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{15}
}

func (x *CompleteCheckCallbackReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *CompleteCheckCallbackReq) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *CompleteCheckCallbackReq) GetRewardToken() string {
	if x != nil {
		return x.RewardToken
	}
	return ""
}

func (x *CompleteCheckCallbackReq) GetRewardRate() int32 {
	if x != nil {
		return x.RewardRate
	}
	return 0
}

type CompleteCheckCallbackRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 校验成功返回0,否则返回其他
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *CompleteCheckCallbackRsp) Reset() {
	*x = CompleteCheckCallbackRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteCheckCallbackRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteCheckCallbackRsp) ProtoMessage() {}

func (x *CompleteCheckCallbackRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteCheckCallbackRsp.ProtoReflect.Descriptor instead.
func (*CompleteCheckCallbackRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{16}
}

func (x *CompleteCheckCallbackRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CompleteCheckCallbackRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CompleteNotifyCallbackReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId      string            `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	TaskId      int32             `protobuf:"varint,2,opt,name=taskId,proto3" json:"taskId,omitempty"`                                                                                  // 任务id
	RewardToken string            `protobuf:"bytes,3,opt,name=rewardToken,proto3" json:"rewardToken,omitempty"`                                                                         // token
	RewardRate  int32             `protobuf:"varint,4,opt,name=rewardRate,proto3" json:"rewardRate,omitempty"`                                                                          // 领奖倍数
	Ext         map[string]string `protobuf:"bytes,5,rep,name=ext,proto3" json:"ext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 透传参数
	ConsumeId   string            `protobuf:"bytes,6,opt,name=consumeId,proto3" json:"consumeId,omitempty"`                                                                             // 幂等ID
}

func (x *CompleteNotifyCallbackReq) Reset() {
	*x = CompleteNotifyCallbackReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteNotifyCallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteNotifyCallbackReq) ProtoMessage() {}

func (x *CompleteNotifyCallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteNotifyCallbackReq.ProtoReflect.Descriptor instead.
func (*CompleteNotifyCallbackReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{17}
}

func (x *CompleteNotifyCallbackReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *CompleteNotifyCallbackReq) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *CompleteNotifyCallbackReq) GetRewardToken() string {
	if x != nil {
		return x.RewardToken
	}
	return ""
}

func (x *CompleteNotifyCallbackReq) GetRewardRate() int32 {
	if x != nil {
		return x.RewardRate
	}
	return 0
}

func (x *CompleteNotifyCallbackReq) GetExt() map[string]string {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *CompleteNotifyCallbackReq) GetConsumeId() string {
	if x != nil {
		return x.ConsumeId
	}
	return ""
}

type CompleteNotifyCallbackRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 校验成功返回0,否则返回其他
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *CompleteNotifyCallbackRsp) Reset() {
	*x = CompleteNotifyCallbackRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteNotifyCallbackRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteNotifyCallbackRsp) ProtoMessage() {}

func (x *CompleteNotifyCallbackRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteNotifyCallbackRsp.ProtoReflect.Descriptor instead.
func (*CompleteNotifyCallbackRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{18}
}

func (x *CompleteNotifyCallbackRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CompleteNotifyCallbackRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type QueryModuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string        `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId  string        `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	SceneId int32         `protobuf:"varint,3,opt,name=sceneId,proto3" json:"sceneId,omitempty"` // 场景id
	Qua     string        `protobuf:"bytes,4,opt,name=qua,proto3" json:"qua,omitempty"`          // qua参数
	Ua      string        `protobuf:"bytes,5,opt,name=ua,proto3" json:"ua,omitempty"`            // ua参数
	Cookies *CookieFileds `protobuf:"bytes,6,opt,name=cookies,proto3" json:"cookies,omitempty"`
}

func (x *QueryModuleReq) Reset() {
	*x = QueryModuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryModuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryModuleReq) ProtoMessage() {}

func (x *QueryModuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryModuleReq.ProtoReflect.Descriptor instead.
func (*QueryModuleReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{19}
}

func (x *QueryModuleReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryModuleReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *QueryModuleReq) GetSceneId() int32 {
	if x != nil {
		return x.SceneId
	}
	return 0
}

func (x *QueryModuleReq) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *QueryModuleReq) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *QueryModuleReq) GetCookies() *CookieFileds {
	if x != nil {
		return x.Cookies
	}
	return nil
}

type QueryModuleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Modules []*TaskModule `protobuf:"bytes,1,rep,name=modules,proto3" json:"modules,omitempty"`
}

func (x *QueryModuleRsp) Reset() {
	*x = QueryModuleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryModuleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryModuleRsp) ProtoMessage() {}

func (x *QueryModuleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryModuleRsp.ProtoReflect.Descriptor instead.
func (*QueryModuleRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{20}
}

func (x *QueryModuleRsp) GetModules() []*TaskModule {
	if x != nil {
		return x.Modules
	}
	return nil
}

type GrantBundleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	Uid           int64  `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	TaskId        int32  `protobuf:"varint,3,opt,name=taskId,proto3" json:"taskId,omitempty"`
	BundleId      int64  `protobuf:"varint,4,opt,name=bundleId,proto3" json:"bundleId,omitempty"`
	BundleNum     int64  `protobuf:"varint,5,opt,name=bundleNum,proto3" json:"bundleNum,omitempty"`
	TransactionId string `protobuf:"bytes,6,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
	ReportReason  string `protobuf:"bytes,7,opt,name=reportReason,proto3" json:"reportReason,omitempty"`
	IsPaid        bool   `protobuf:"varint,8,opt,name=isPaid,proto3" json:"isPaid,omitempty"`
}

func (x *GrantBundleReq) Reset() {
	*x = GrantBundleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrantBundleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrantBundleReq) ProtoMessage() {}

func (x *GrantBundleReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrantBundleReq.ProtoReflect.Descriptor instead.
func (*GrantBundleReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{21}
}

func (x *GrantBundleReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GrantBundleReq) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *GrantBundleReq) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *GrantBundleReq) GetBundleId() int64 {
	if x != nil {
		return x.BundleId
	}
	return 0
}

func (x *GrantBundleReq) GetBundleNum() int64 {
	if x != nil {
		return x.BundleNum
	}
	return 0
}

func (x *GrantBundleReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *GrantBundleReq) GetReportReason() string {
	if x != nil {
		return x.ReportReason
	}
	return ""
}

func (x *GrantBundleReq) GetIsPaid() bool {
	if x != nil {
		return x.IsPaid
	}
	return false
}

type GrantBundleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 校验成功返回0,否则返回其他
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *GrantBundleRsp) Reset() {
	*x = GrantBundleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrantBundleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrantBundleRsp) ProtoMessage() {}

func (x *GrantBundleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_task_adapter_task_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrantBundleRsp.ProtoReflect.Descriptor instead.
func (*GrantBundleRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_task_adapter_task_proto_rawDescGZIP(), []int{22}
}

func (x *GrantBundleRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GrantBundleRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

var File_pb_adapter_task_adapter_task_proto protoreflect.FileDescriptor

var file_pb_adapter_task_adapter_task_proto_rawDesc = []byte{
	0x0a, 0x22, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x1a, 0x70, 0x62, 0x2f, 0x61,
	0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6e, 0x0a, 0x0c, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x72,
	0x4f, 0x70, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x4f,
	0x70, 0x65, 0x6e, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74,
	0x72, 0x4f, 0x70, 0x65, 0x6e, 0x4b, 0x65, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72,
	0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0xd6, 0x01, 0x0a, 0x08, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x71, 0x75, 0x61, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x71, 0x75, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75,
	0x61, 0x12, 0x2c, 0x0a, 0x07, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x64, 0x73, 0x52, 0x07, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x73, 0x22,
	0x34, 0x0a, 0x08, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x28, 0x0a, 0x06, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x06, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x22, 0xa0, 0x01, 0x0a, 0x08, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52,
	0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x71, 0x75, 0x61, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x71, 0x75, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x2c, 0x0a, 0x07, 0x63, 0x6f,
	0x6f, 0x6b, 0x69, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x64, 0x73, 0x52,
	0x07, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x73, 0x22, 0x22, 0x0a, 0x08, 0x43, 0x6c, 0x61, 0x69,
	0x6d, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x72, 0x4d, 0x73, 0x67, 0x22, 0x9b, 0x03, 0x0a,
	0x0b, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x71, 0x75, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x71, 0x75, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x61,
	0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x2c, 0x0a, 0x07, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43,
	0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x64, 0x73, 0x52, 0x07, 0x63, 0x6f, 0x6f,
	0x6b, 0x69, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x54, 0x61,
	0x73, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c,
	0x54, 0x61, 0x73, 0x6b, 0x12, 0x50, 0x0a, 0x0f, 0x6d, 0x61, 0x70, 0x42, 0x75, 0x73, 0x69, 0x50,
	0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x2e, 0x4d, 0x61, 0x70, 0x42, 0x75, 0x73, 0x69, 0x50, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x42, 0x75, 0x73, 0x69, 0x50, 0x61,
	0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x42, 0x75, 0x73,
	0x69, 0x50, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x4d, 0x0a, 0x0b, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x72,
	0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x72, 0x4d, 0x73,
	0x67, 0x12, 0x26, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0xa1, 0x01, 0x0a, 0x09, 0x47, 0x69,
	0x76, 0x65, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x71, 0x75, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x71, 0x75, 0x61, 0x12,
	0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12,
	0x2c, 0x0a, 0x07, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x64, 0x73, 0x52, 0x07, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x73, 0x22, 0x23, 0x0a,
	0x09, 0x47, 0x69, 0x76, 0x65, 0x75, 0x70, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x72, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x72, 0x4d,
	0x73, 0x67, 0x22, 0x94, 0x01, 0x0a, 0x10, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x22, 0x38, 0x0a, 0x10, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x22, 0x9f, 0x02, 0x0a, 0x11, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x03, 0x65,
	0x78, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65,
	0x71, 0x2e, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x1a, 0x36, 0x0a,
	0x08, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x39, 0x0a, 0x11, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x22, 0x56, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x30, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x04, 0x74, 0x61,
	0x73, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x22, 0x8c, 0x01, 0x0a, 0x18, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x22, 0x40, 0x0a, 0x18, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x9f, 0x02, 0x0a, 0x19,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x12, 0x3a, 0x0a, 0x03, 0x65,
	0x78, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x2e, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x49, 0x64, 0x1a, 0x36, 0x0a, 0x08, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x41, 0x0a,
	0x19, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x22, 0xa8, 0x01, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x71,
	0x75, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x71, 0x75, 0x61, 0x12, 0x0e, 0x0a,
	0x02, 0x75, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x2c, 0x0a,
	0x07, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x64, 0x73, 0x52, 0x07, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x73, 0x22, 0x3c, 0x0a, 0x0e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x2a, 0x0a,
	0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x52, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x22, 0xec, 0x01, 0x0a, 0x0e, 0x47, 0x72,
	0x61, 0x6e, 0x74, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x62, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x50, 0x61, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x69, 0x73, 0x50, 0x61, 0x69, 0x64, 0x22, 0x36, 0x0a, 0x0e, 0x47, 0x72, 0x61, 0x6e,
	0x74, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x32, 0xf4, 0x03, 0x0a, 0x0b, 0x41, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b,
	0x12, 0x39, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x05, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x12, 0x0e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x1a, 0x0e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x05, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x0e, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x0e, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x30, 0x0a,
	0x08, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x2a, 0x0a, 0x06, 0x47, 0x69, 0x76, 0x65, 0x75, 0x70, 0x12, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x47, 0x69, 0x76, 0x65, 0x75, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x0f, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x47, 0x69, 0x76, 0x65, 0x75, 0x70, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x0e, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x12, 0x16, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x42, 0x0a,
	0x0e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12,
	0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x73,
	0x70, 0x12, 0x39, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x12, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x0b,
	0x47, 0x72, 0x61, 0x6e, 0x74, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x14, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x42, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x42, 0x44, 0x5a, 0x42, 0x74, 0x63, 0x6f, 0x64, 0x65,
	0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62,
	0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_adapter_task_adapter_task_proto_rawDescOnce sync.Once
	file_pb_adapter_task_adapter_task_proto_rawDescData = file_pb_adapter_task_adapter_task_proto_rawDesc
)

func file_pb_adapter_task_adapter_task_proto_rawDescGZIP() []byte {
	file_pb_adapter_task_adapter_task_proto_rawDescOnce.Do(func() {
		file_pb_adapter_task_adapter_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_adapter_task_adapter_task_proto_rawDescData)
	})
	return file_pb_adapter_task_adapter_task_proto_rawDescData
}

var file_pb_adapter_task_adapter_task_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_pb_adapter_task_adapter_task_proto_goTypes = []interface{}{
	(*CookieFileds)(nil),              // 0: game.CookieFileds
	(*QueryReq)(nil),                  // 1: game.QueryReq
	(*QueryRsp)(nil),                  // 2: game.QueryRsp
	(*ClaimReq)(nil),                  // 3: game.ClaimReq
	(*ClaimRsp)(nil),                  // 4: game.ClaimRsp
	(*CompleteReq)(nil),               // 5: game.CompleteReq
	(*CompleteRsp)(nil),               // 6: game.CompleteRsp
	(*GiveupReq)(nil),                 // 7: game.GiveupReq
	(*GiveupRsp)(nil),                 // 8: game.GiveupRsp
	(*CompleteCheckReq)(nil),          // 9: game.CompleteCheckReq
	(*CompleteCheckRsp)(nil),          // 10: game.CompleteCheckRsp
	(*CompleteNotifyReq)(nil),         // 11: game.CompleteNotifyReq
	(*CompleteNotifyRsp)(nil),         // 12: game.CompleteNotifyRsp
	(*QueryStatusReq)(nil),            // 13: game.QueryStatusReq
	(*QueryStatusRsp)(nil),            // 14: game.QueryStatusRsp
	(*CompleteCheckCallbackReq)(nil),  // 15: game.CompleteCheckCallbackReq
	(*CompleteCheckCallbackRsp)(nil),  // 16: game.CompleteCheckCallbackRsp
	(*CompleteNotifyCallbackReq)(nil), // 17: game.CompleteNotifyCallbackReq
	(*CompleteNotifyCallbackRsp)(nil), // 18: game.CompleteNotifyCallbackRsp
	(*QueryModuleReq)(nil),            // 19: game.QueryModuleReq
	(*QueryModuleRsp)(nil),            // 20: game.QueryModuleRsp
	(*GrantBundleReq)(nil),            // 21: game.GrantBundleReq
	(*GrantBundleRsp)(nil),            // 22: game.GrantBundleRsp
	nil,                               // 23: game.CompleteReq.MapBusiPassbackEntry
	nil,                               // 24: game.CompleteNotifyReq.ExtEntry
	nil,                               // 25: game.CompleteNotifyCallbackReq.ExtEntry
	(*TaskModule)(nil),                // 26: game.TaskModule
	(*Reward)(nil),                    // 27: game.Reward
	(*Task)(nil),                      // 28: game.Task
}
var file_pb_adapter_task_adapter_task_proto_depIdxs = []int32{
	0,  // 0: game.QueryReq.cookies:type_name -> game.CookieFileds
	26, // 1: game.QueryRsp.module:type_name -> game.TaskModule
	0,  // 2: game.ClaimReq.cookies:type_name -> game.CookieFileds
	0,  // 3: game.CompleteReq.cookies:type_name -> game.CookieFileds
	23, // 4: game.CompleteReq.mapBusiPassback:type_name -> game.CompleteReq.MapBusiPassbackEntry
	27, // 5: game.CompleteRsp.rewards:type_name -> game.Reward
	0,  // 6: game.GiveupReq.cookies:type_name -> game.CookieFileds
	24, // 7: game.CompleteNotifyReq.ext:type_name -> game.CompleteNotifyReq.ExtEntry
	28, // 8: game.QueryStatusRsp.task:type_name -> game.Task
	25, // 9: game.CompleteNotifyCallbackReq.ext:type_name -> game.CompleteNotifyCallbackReq.ExtEntry
	0,  // 10: game.QueryModuleReq.cookies:type_name -> game.CookieFileds
	26, // 11: game.QueryModuleRsp.modules:type_name -> game.TaskModule
	13, // 12: game.AdapterTask.QueryStatus:input_type -> game.QueryStatusReq
	1,  // 13: game.AdapterTask.Query:input_type -> game.QueryReq
	3,  // 14: game.AdapterTask.Claim:input_type -> game.ClaimReq
	5,  // 15: game.AdapterTask.Complete:input_type -> game.CompleteReq
	7,  // 16: game.AdapterTask.Giveup:input_type -> game.GiveupReq
	9,  // 17: game.AdapterTask.CompleteVerify:input_type -> game.CompleteCheckReq
	11, // 18: game.AdapterTask.CompleteNotify:input_type -> game.CompleteNotifyReq
	19, // 19: game.AdapterTask.QueryModule:input_type -> game.QueryModuleReq
	21, // 20: game.AdapterTask.GrantBundle:input_type -> game.GrantBundleReq
	14, // 21: game.AdapterTask.QueryStatus:output_type -> game.QueryStatusRsp
	2,  // 22: game.AdapterTask.Query:output_type -> game.QueryRsp
	4,  // 23: game.AdapterTask.Claim:output_type -> game.ClaimRsp
	6,  // 24: game.AdapterTask.Complete:output_type -> game.CompleteRsp
	8,  // 25: game.AdapterTask.Giveup:output_type -> game.GiveupRsp
	10, // 26: game.AdapterTask.CompleteVerify:output_type -> game.CompleteCheckRsp
	12, // 27: game.AdapterTask.CompleteNotify:output_type -> game.CompleteNotifyRsp
	20, // 28: game.AdapterTask.QueryModule:output_type -> game.QueryModuleRsp
	22, // 29: game.AdapterTask.GrantBundle:output_type -> game.GrantBundleRsp
	21, // [21:30] is the sub-list for method output_type
	12, // [12:21] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_pb_adapter_task_adapter_task_proto_init() }
func file_pb_adapter_task_adapter_task_proto_init() {
	if File_pb_adapter_task_adapter_task_proto != nil {
		return
	}
	file_pb_adapter_task_comm_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_adapter_task_adapter_task_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CookieFileds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiveupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiveupRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteCheckReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteCheckRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteNotifyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteNotifyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteCheckCallbackReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteCheckCallbackRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteNotifyCallbackReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteNotifyCallbackRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryModuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryModuleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GrantBundleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_task_adapter_task_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GrantBundleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_adapter_task_adapter_task_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_adapter_task_adapter_task_proto_goTypes,
		DependencyIndexes: file_pb_adapter_task_adapter_task_proto_depIdxs,
		MessageInfos:      file_pb_adapter_task_adapter_task_proto_msgTypes,
	}.Build()
	File_pb_adapter_task_adapter_task_proto = out.File
	file_pb_adapter_task_adapter_task_proto_rawDesc = nil
	file_pb_adapter_task_adapter_task_proto_goTypes = nil
	file_pb_adapter_task_adapter_task_proto_depIdxs = nil
}
