{"swagger": "2.0", "info": {"title": "pb/game_packapi/game_packapi.proto", "version": "version not set"}, "tags": [{"name": "Api"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game_packapi.Api/GamePackapi": {"post": {"operationId": "Api_GamePackapi", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_packapiGamePackapiRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_packapiGamePackapiReq"}}], "tags": ["Api"]}}, "/game_packapi.Api/GetOpenId": {"post": {"operationId": "Api_GetOpenId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_packapiGetOpenIdRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_packapiGetOpenIdReq"}}], "tags": ["Api"]}}}, "definitions": {"game_packapiGamePackapiReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "app_id"}, "openId": {"type": "string", "title": "操作 open_id"}, "toOpenId": {"type": "string", "title": "动作朝向 open_id"}, "cmdId": {"type": "integer", "format": "int64", "title": "cmd类型 游戏自己定义"}, "msgId": {"type": "string", "title": "消息唯一ID"}, "ts": {"type": "integer", "format": "int64", "title": "时间时间戳"}, "payload": {"type": "string", "title": "app_id + cmd_id对接的结构体 json.Marshal 之后的结果"}}}, "game_packapiGamePackapiRsp": {"type": "object", "properties": {"msgRtn": {"type": "string", "title": "回包"}}}, "game_packapiGetOpenIdReq": {"type": "object", "properties": {"strAppID": {"type": "string", "title": "app_id"}, "lUid": {"type": "string", "format": "int64", "title": "用户Id"}, "lPlatID": {"type": "string", "format": "int64", "title": "平台ID"}}}, "game_packapiGetOpenIdRsp": {"type": "object", "properties": {"strOpenid": {"type": "string", "title": "openid"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}