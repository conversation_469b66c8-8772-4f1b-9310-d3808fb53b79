// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_packapi/game_packapi.proto

package game_packapi

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GamePackapiReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`            // app_id
	OpenId   string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`         // 操作 open_id
	ToOpenId string `protobuf:"bytes,3,opt,name=to_open_id,json=toOpenId,proto3" json:"to_open_id,omitempty"` // 动作朝向 open_id
	CmdId    uint32 `protobuf:"varint,4,opt,name=cmd_id,json=cmdId,proto3" json:"cmd_id,omitempty"`           // cmd类型 游戏自己定义
	MsgId    string `protobuf:"bytes,5,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`            // 消息唯一ID
	Ts       uint32 `protobuf:"varint,6,opt,name=ts,proto3" json:"ts,omitempty"`                              // 时间时间戳
	Payload  string `protobuf:"bytes,7,opt,name=payload,proto3" json:"payload,omitempty"`                     // app_id + cmd_id对接的结构体 json.Marshal 之后的结果
}

func (x *GamePackapiReq) Reset() {
	*x = GamePackapiReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_packapi_game_packapi_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GamePackapiReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GamePackapiReq) ProtoMessage() {}

func (x *GamePackapiReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_packapi_game_packapi_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GamePackapiReq.ProtoReflect.Descriptor instead.
func (*GamePackapiReq) Descriptor() ([]byte, []int) {
	return file_pb_game_packapi_game_packapi_proto_rawDescGZIP(), []int{0}
}

func (x *GamePackapiReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GamePackapiReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *GamePackapiReq) GetToOpenId() string {
	if x != nil {
		return x.ToOpenId
	}
	return ""
}

func (x *GamePackapiReq) GetCmdId() uint32 {
	if x != nil {
		return x.CmdId
	}
	return 0
}

func (x *GamePackapiReq) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *GamePackapiReq) GetTs() uint32 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *GamePackapiReq) GetPayload() string {
	if x != nil {
		return x.Payload
	}
	return ""
}

type GamePackapiRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgRtn string `protobuf:"bytes,1,opt,name=msg_rtn,json=msgRtn,proto3" json:"msg_rtn,omitempty"` //回包
}

func (x *GamePackapiRsp) Reset() {
	*x = GamePackapiRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_packapi_game_packapi_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GamePackapiRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GamePackapiRsp) ProtoMessage() {}

func (x *GamePackapiRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_packapi_game_packapi_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GamePackapiRsp.ProtoReflect.Descriptor instead.
func (*GamePackapiRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_packapi_game_packapi_proto_rawDescGZIP(), []int{1}
}

func (x *GamePackapiRsp) GetMsgRtn() string {
	if x != nil {
		return x.MsgRtn
	}
	return ""
}

type GetOpenIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"` // app_id
	LUid     int64  `protobuf:"varint,2,opt,name=lUid,proto3" json:"lUid,omitempty"`        // 用户Id
	LPlatID  int64  `protobuf:"varint,3,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"`  // 平台ID
}

func (x *GetOpenIdReq) Reset() {
	*x = GetOpenIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_packapi_game_packapi_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpenIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpenIdReq) ProtoMessage() {}

func (x *GetOpenIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_packapi_game_packapi_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpenIdReq.ProtoReflect.Descriptor instead.
func (*GetOpenIdReq) Descriptor() ([]byte, []int) {
	return file_pb_game_packapi_game_packapi_proto_rawDescGZIP(), []int{2}
}

func (x *GetOpenIdReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *GetOpenIdReq) GetLUid() int64 {
	if x != nil {
		return x.LUid
	}
	return 0
}

func (x *GetOpenIdReq) GetLPlatID() int64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

type GetOpenIdRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrOpenid string `protobuf:"bytes,1,opt,name=strOpenid,proto3" json:"strOpenid,omitempty"` //openid
}

func (x *GetOpenIdRsp) Reset() {
	*x = GetOpenIdRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_packapi_game_packapi_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpenIdRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpenIdRsp) ProtoMessage() {}

func (x *GetOpenIdRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_packapi_game_packapi_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpenIdRsp.ProtoReflect.Descriptor instead.
func (*GetOpenIdRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_packapi_game_packapi_proto_rawDescGZIP(), []int{3}
}

func (x *GetOpenIdRsp) GetStrOpenid() string {
	if x != nil {
		return x.StrOpenid
	}
	return ""
}

var File_pb_game_packapi_game_packapi_proto protoreflect.FileDescriptor

var file_pb_game_packapi_game_packapi_proto_rawDesc = []byte{
	0x0a, 0x22, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x70,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x70, 0x69, 0x22, 0xb6, 0x01, 0x0a, 0x0e, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x70, 0x69, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x74, 0x6f, 0x5f, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x6f, 0x4f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x6d, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6d, 0x64, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6d, 0x73,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49,
	0x64, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x74,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0x29, 0x0a, 0x0e, 0x47,
	0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x70, 0x69, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a,
	0x07, 0x6d, 0x73, 0x67, 0x5f, 0x72, 0x74, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6d, 0x73, 0x67, 0x52, 0x74, 0x6e, 0x22, 0x58, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70,
	0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x55, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x6c, 0x55, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49,
	0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44,
	0x22, 0x2c, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x52, 0x73, 0x70,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x95,
	0x01, 0x0a, 0x03, 0x41, 0x70, 0x69, 0x12, 0x49, 0x0a, 0x0b, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x70, 0x69, 0x12, 0x1c, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x70, 0x69,
	0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x70, 0x69, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x70, 0x69, 0x52, 0x73,
	0x70, 0x12, 0x43, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1a,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65,
	0x74, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x52, 0x73, 0x70, 0x42, 0x44, 0x5a, 0x42, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_packapi_game_packapi_proto_rawDescOnce sync.Once
	file_pb_game_packapi_game_packapi_proto_rawDescData = file_pb_game_packapi_game_packapi_proto_rawDesc
)

func file_pb_game_packapi_game_packapi_proto_rawDescGZIP() []byte {
	file_pb_game_packapi_game_packapi_proto_rawDescOnce.Do(func() {
		file_pb_game_packapi_game_packapi_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_packapi_game_packapi_proto_rawDescData)
	})
	return file_pb_game_packapi_game_packapi_proto_rawDescData
}

var file_pb_game_packapi_game_packapi_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_pb_game_packapi_game_packapi_proto_goTypes = []interface{}{
	(*GamePackapiReq)(nil), // 0: game_packapi.GamePackapiReq
	(*GamePackapiRsp)(nil), // 1: game_packapi.GamePackapiRsp
	(*GetOpenIdReq)(nil),   // 2: game_packapi.GetOpenIdReq
	(*GetOpenIdRsp)(nil),   // 3: game_packapi.GetOpenIdRsp
}
var file_pb_game_packapi_game_packapi_proto_depIdxs = []int32{
	0, // 0: game_packapi.Api.GamePackapi:input_type -> game_packapi.GamePackapiReq
	2, // 1: game_packapi.Api.GetOpenId:input_type -> game_packapi.GetOpenIdReq
	1, // 2: game_packapi.Api.GamePackapi:output_type -> game_packapi.GamePackapiRsp
	3, // 3: game_packapi.Api.GetOpenId:output_type -> game_packapi.GetOpenIdRsp
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_game_packapi_game_packapi_proto_init() }
func file_pb_game_packapi_game_packapi_proto_init() {
	if File_pb_game_packapi_game_packapi_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_packapi_game_packapi_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GamePackapiReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_packapi_game_packapi_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GamePackapiRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_packapi_game_packapi_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpenIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_packapi_game_packapi_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpenIdRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_packapi_game_packapi_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_packapi_game_packapi_proto_goTypes,
		DependencyIndexes: file_pb_game_packapi_game_packapi_proto_depIdxs,
		MessageInfos:      file_pb_game_packapi_game_packapi_proto_msgTypes,
	}.Build()
	File_pb_game_packapi_game_packapi_proto = out.File
	file_pb_game_packapi_game_packapi_proto_rawDesc = nil
	file_pb_game_packapi_game_packapi_proto_goTypes = nil
	file_pb_game_packapi_game_packapi_proto_depIdxs = nil
}
