// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/exchange_admin/exchange_admin.proto

package exchange_admin

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	ExchangeAdmin_SyncActOne_FullMethodName = "/component.game.ExchangeAdmin/SyncActOne"
	ExchangeAdmin_SyncOne_FullMethodName    = "/component.game.ExchangeAdmin/SyncOne"
)

// ExchangeAdminClient is the client API for ExchangeAdmin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ExchangeAdminClient interface {
	// 同步单个活动
	SyncActOne(ctx context.Context, in *SyncActOneReq, opts ...grpc.CallOption) (*SyncActOneRsp, error)
	// 同步单条记录
	SyncOne(ctx context.Context, in *SyncOneReq, opts ...grpc.CallOption) (*SyncOneRsp, error)
}

type exchangeAdminClient struct {
	cc grpc.ClientConnInterface
}

func NewExchangeAdminClient(cc grpc.ClientConnInterface) ExchangeAdminClient {
	return &exchangeAdminClient{cc}
}

func (c *exchangeAdminClient) SyncActOne(ctx context.Context, in *SyncActOneReq, opts ...grpc.CallOption) (*SyncActOneRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncActOneRsp)
	err := c.cc.Invoke(ctx, ExchangeAdmin_SyncActOne_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeAdminClient) SyncOne(ctx context.Context, in *SyncOneReq, opts ...grpc.CallOption) (*SyncOneRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncOneRsp)
	err := c.cc.Invoke(ctx, ExchangeAdmin_SyncOne_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ExchangeAdminServer is the server API for ExchangeAdmin service.
// All implementations should embed UnimplementedExchangeAdminServer
// for forward compatibility
type ExchangeAdminServer interface {
	// 同步单个活动
	SyncActOne(context.Context, *SyncActOneReq) (*SyncActOneRsp, error)
	// 同步单条记录
	SyncOne(context.Context, *SyncOneReq) (*SyncOneRsp, error)
}

// UnimplementedExchangeAdminServer should be embedded to have forward compatible implementations.
type UnimplementedExchangeAdminServer struct {
}

func (UnimplementedExchangeAdminServer) SyncActOne(context.Context, *SyncActOneReq) (*SyncActOneRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncActOne not implemented")
}
func (UnimplementedExchangeAdminServer) SyncOne(context.Context, *SyncOneReq) (*SyncOneRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncOne not implemented")
}

// UnsafeExchangeAdminServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ExchangeAdminServer will
// result in compilation errors.
type UnsafeExchangeAdminServer interface {
	mustEmbedUnimplementedExchangeAdminServer()
}

func RegisterExchangeAdminServer(s grpc.ServiceRegistrar, srv ExchangeAdminServer) {
	s.RegisterService(&ExchangeAdmin_ServiceDesc, srv)
}

func _ExchangeAdmin_SyncActOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncActOneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeAdminServer).SyncActOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExchangeAdmin_SyncActOne_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeAdminServer).SyncActOne(ctx, req.(*SyncActOneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeAdmin_SyncOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncOneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeAdminServer).SyncOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExchangeAdmin_SyncOne_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeAdminServer).SyncOne(ctx, req.(*SyncOneReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ExchangeAdmin_ServiceDesc is the grpc.ServiceDesc for ExchangeAdmin service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ExchangeAdmin_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "component.game.ExchangeAdmin",
	HandlerType: (*ExchangeAdminServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncActOne",
			Handler:    _ExchangeAdmin_SyncActOne_Handler,
		},
		{
			MethodName: "SyncOne",
			Handler:    _ExchangeAdmin_SyncOne_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/exchange_admin/exchange_admin.proto",
}
