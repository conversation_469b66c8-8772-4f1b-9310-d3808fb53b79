{"swagger": "2.0", "info": {"title": "pb/exchange_admin/exchange_admin.proto", "version": "version not set"}, "tags": [{"name": "ExchangeAdmin"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/exchange_admin/syncActOne": {"post": {"summary": "同步单个活动", "operationId": "ExchangeAdmin_SyncActOne", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameSyncActOneRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameSyncActOneReq"}}], "tags": ["ExchangeAdmin"]}}, "/exchange_admin/syncOne": {"post": {"summary": "同步单条记录", "operationId": "ExchangeAdmin_SyncOne", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameSyncOneRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameSyncOneReq"}}], "tags": ["ExchangeAdmin"]}}}, "definitions": {"gameSyncActOneReq": {"type": "object", "properties": {"actId": {"type": "string", "format": "int64", "title": "act_id 活动ID"}, "platId": {"type": "string", "format": "uint64", "title": "平台id"}}}, "gameSyncActOneRsp": {"type": "object"}, "gameSyncOneReq": {"type": "object", "properties": {"exchangeId": {"type": "string", "format": "int64", "title": "exchange_id 兑换ID"}, "platId": {"type": "string", "format": "uint64", "title": "平台id"}}}, "gameSyncOneRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}