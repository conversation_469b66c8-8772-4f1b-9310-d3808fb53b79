// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/xian_kblb/comm/comm.proto

package comm

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 平台礼物信息
type Gift struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftId          int64  `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`                              //礼物ID
	GiftPrice       int64  `protobuf:"varint,2,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`                     //礼物单价
	GiftName        string `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`                         //礼物名称
	GiftIcon        string `protobuf:"bytes,4,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`                         //礼物图标 160x160
	GiftIcon_360    string `protobuf:"bytes,5,opt,name=gift_icon_360,json=giftIcon360,proto3" json:"gift_icon_360,omitempty"`              //礼物图标 360x360
	Lv              int32  `protobuf:"varint,6,opt,name=lv,proto3" json:"lv,omitempty"`                                                    // 档位, 1,2,3
	Category        int32  `protobuf:"varint,7,opt,name=category,proto3" json:"category,omitempty"`                                        // 分类: 1-珍宝,2-祥瑞,3-神仙,4-秘宝
	IsSuper         bool   `protobuf:"varint,8,opt,name=is_super,json=isSuper,proto3" json:"is_super,omitempty"`                           // 是否超特礼物
	GiftAnimationId int64  `protobuf:"varint,9,opt,name=gift_animation_id,json=giftAnimationId,proto3" json:"gift_animation_id,omitempty"` // 动画字段id
	AnimPrice       int64  `protobuf:"varint,10,opt,name=anim_price,json=animPrice,proto3" json:"anim_price,omitempty"`                    // 动画价值
	GiftDiscount    string `protobuf:"bytes,11,opt,name=gift_discount,json=giftDiscount,proto3" json:"gift_discount,omitempty"`            // 礼物折扣
}

func (x *Gift) Reset() {
	*x = Gift{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_kblb_comm_comm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gift) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gift) ProtoMessage() {}

func (x *Gift) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_kblb_comm_comm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gift.ProtoReflect.Descriptor instead.
func (*Gift) Descriptor() ([]byte, []int) {
	return file_pb_xian_kblb_comm_comm_proto_rawDescGZIP(), []int{0}
}

func (x *Gift) GetGiftId() int64 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

func (x *Gift) GetGiftPrice() int64 {
	if x != nil {
		return x.GiftPrice
	}
	return 0
}

func (x *Gift) GetGiftName() string {
	if x != nil {
		return x.GiftName
	}
	return ""
}

func (x *Gift) GetGiftIcon() string {
	if x != nil {
		return x.GiftIcon
	}
	return ""
}

func (x *Gift) GetGiftIcon_360() string {
	if x != nil {
		return x.GiftIcon_360
	}
	return ""
}

func (x *Gift) GetLv() int32 {
	if x != nil {
		return x.Lv
	}
	return 0
}

func (x *Gift) GetCategory() int32 {
	if x != nil {
		return x.Category
	}
	return 0
}

func (x *Gift) GetIsSuper() bool {
	if x != nil {
		return x.IsSuper
	}
	return false
}

func (x *Gift) GetGiftAnimationId() int64 {
	if x != nil {
		return x.GiftAnimationId
	}
	return 0
}

func (x *Gift) GetAnimPrice() int64 {
	if x != nil {
		return x.AnimPrice
	}
	return 0
}

func (x *Gift) GetGiftDiscount() string {
	if x != nil {
		return x.GiftDiscount
	}
	return ""
}

// 礼包资产
type GGItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardId   string `protobuf:"bytes,1,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`        // 奖励ID
	RewardNum  int64  `protobuf:"varint,2,opt,name=reward_num,json=rewardNum,proto3" json:"reward_num,omitempty"`    // 数量
	RewardType int64  `protobuf:"varint,3,opt,name=reward_type,json=rewardType,proto3" json:"reward_type,omitempty"` // 奖励类型
	UnitPrice  int64  `protobuf:"varint,4,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`    // 单价
	RewardName string `protobuf:"bytes,5,opt,name=reward_name,json=rewardName,proto3" json:"reward_name,omitempty"`  // 资产名称
	RewardLogo string `protobuf:"bytes,6,opt,name=reward_logo,json=rewardLogo,proto3" json:"reward_logo,omitempty"`  // 资产icon
	Universal  string `protobuf:"bytes,7,opt,name=universal,proto3" json:"universal,omitempty"`                      // 万能字段 透传配置系统上配置的信息
}

func (x *GGItem) Reset() {
	*x = GGItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_kblb_comm_comm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GGItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GGItem) ProtoMessage() {}

func (x *GGItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_kblb_comm_comm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GGItem.ProtoReflect.Descriptor instead.
func (*GGItem) Descriptor() ([]byte, []int) {
	return file_pb_xian_kblb_comm_comm_proto_rawDescGZIP(), []int{1}
}

func (x *GGItem) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

func (x *GGItem) GetRewardNum() int64 {
	if x != nil {
		return x.RewardNum
	}
	return 0
}

func (x *GGItem) GetRewardType() int64 {
	if x != nil {
		return x.RewardType
	}
	return 0
}

func (x *GGItem) GetUnitPrice() int64 {
	if x != nil {
		return x.UnitPrice
	}
	return 0
}

func (x *GGItem) GetRewardName() string {
	if x != nil {
		return x.RewardName
	}
	return ""
}

func (x *GGItem) GetRewardLogo() string {
	if x != nil {
		return x.RewardLogo
	}
	return ""
}

func (x *GGItem) GetUniversal() string {
	if x != nil {
		return x.Universal
	}
	return ""
}

// 平台礼物包信息
type GiftGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 礼包ID
	// int64 series_id = 2; // 礼包序列ID，标识礼包ID属于某个系列活动
	Items []*GGItem `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"` // 礼包内包含资产列表
}

func (x *GiftGroup) Reset() {
	*x = GiftGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_kblb_comm_comm_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftGroup) ProtoMessage() {}

func (x *GiftGroup) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_kblb_comm_comm_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftGroup.ProtoReflect.Descriptor instead.
func (*GiftGroup) Descriptor() ([]byte, []int) {
	return file_pb_xian_kblb_comm_comm_proto_rawDescGZIP(), []int{2}
}

func (x *GiftGroup) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GiftGroup) GetItems() []*GGItem {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_pb_xian_kblb_comm_comm_proto protoreflect.FileDescriptor

var file_pb_xian_kblb_comm_comm_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x6b, 0x62, 0x6c, 0x62, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x6b, 0x62, 0x6c, 0x62, 0x22, 0xd3, 0x02, 0x0a, 0x04, 0x47, 0x69,
	0x66, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67,
	0x69, 0x66, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x67, 0x69, 0x66, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x69,
	0x66, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67,
	0x69, 0x66, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x5f,
	0x69, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74,
	0x49, 0x63, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0d, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x69, 0x63, 0x6f,
	0x6e, 0x5f, 0x33, 0x36, 0x30, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x69, 0x66,
	0x74, 0x49, 0x63, 0x6f, 0x6e, 0x33, 0x36, 0x30, 0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x76, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6c, 0x76, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x73, 0x75, 0x70, 0x65, 0x72,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x53, 0x75, 0x70, 0x65, 0x72, 0x12,
	0x2a, 0x0a, 0x11, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x67, 0x69, 0x66, 0x74,
	0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x6e, 0x69, 0x6d, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x61, 0x6e, 0x69, 0x6d, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x67, 0x69,
	0x66, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x67, 0x69, 0x66, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0xe4, 0x01, 0x0a, 0x06, 0x47, 0x47, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x74, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x6e, 0x69,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x6e, 0x69, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x6e, 0x69,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x22, 0x44, 0x0a, 0x09, 0x47, 0x69, 0x66, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x6b, 0x62, 0x6c, 0x62, 0x2e, 0x47,
	0x47, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x42, 0x4a, 0x5a, 0x48,
	0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x63, 0x6f, 0x72, 0x70, 0x2f,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x6b, 0x62, 0x6c, 0x62, 0x2f, 0x70,
	0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x6b,
	0x62, 0x6c, 0x62, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_xian_kblb_comm_comm_proto_rawDescOnce sync.Once
	file_pb_xian_kblb_comm_comm_proto_rawDescData = file_pb_xian_kblb_comm_comm_proto_rawDesc
)

func file_pb_xian_kblb_comm_comm_proto_rawDescGZIP() []byte {
	file_pb_xian_kblb_comm_comm_proto_rawDescOnce.Do(func() {
		file_pb_xian_kblb_comm_comm_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_xian_kblb_comm_comm_proto_rawDescData)
	})
	return file_pb_xian_kblb_comm_comm_proto_rawDescData
}

var file_pb_xian_kblb_comm_comm_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_pb_xian_kblb_comm_comm_proto_goTypes = []interface{}{
	(*Gift)(nil),      // 0: xian_kblb.Gift
	(*GGItem)(nil),    // 1: xian_kblb.GGItem
	(*GiftGroup)(nil), // 2: xian_kblb.GiftGroup
}
var file_pb_xian_kblb_comm_comm_proto_depIdxs = []int32{
	1, // 0: xian_kblb.GiftGroup.items:type_name -> xian_kblb.GGItem
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pb_xian_kblb_comm_comm_proto_init() }
func file_pb_xian_kblb_comm_comm_proto_init() {
	if File_pb_xian_kblb_comm_comm_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_xian_kblb_comm_comm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gift); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_kblb_comm_comm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GGItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_kblb_comm_comm_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_xian_kblb_comm_comm_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_xian_kblb_comm_comm_proto_goTypes,
		DependencyIndexes: file_pb_xian_kblb_comm_comm_proto_depIdxs,
		MessageInfos:      file_pb_xian_kblb_comm_comm_proto_msgTypes,
	}.Build()
	File_pb_xian_kblb_comm_comm_proto = out.File
	file_pb_xian_kblb_comm_comm_proto_rawDesc = nil
	file_pb_xian_kblb_comm_comm_proto_goTypes = nil
	file_pb_xian_kblb_comm_comm_proto_depIdxs = nil
}
