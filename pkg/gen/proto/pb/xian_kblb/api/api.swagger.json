{"swagger": "2.0", "info": {"title": "pb/xian_kblb/api/api.proto", "version": "version not set"}, "tags": [{"name": "Api"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/xian_kblb.Api/ARank": {"post": {"summary": "主播榜 （礼物值）", "operationId": "Api_AR<PERSON>k", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbARankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbARankReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/AddFavorite": {"post": {"operationId": "Api_AddFavorite", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbAddFavoriteRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbAddFavoriteReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/Balance": {"post": {"summary": "查询余额", "operationId": "Api_Balance", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbBalanceRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbBalanceReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/CBLottery": {"post": {"summary": "【中台】扣KB回调", "operationId": "Api_CBLottery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/callbackOrderShipmentRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/callbackOrderShipmentReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/CallbackCheckSend": {"post": {"summary": "中台发福利礼包回调检查", "operationId": "Api_CallbackCheckSend", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/callbackGiftPackageBusinessCheckSendRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/callbackGiftPackageBusinessCheckSendReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/Celebrity": {"post": {"summary": "名人堂 （收藏数量）", "operationId": "Api_Celebrity", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbCelebrityRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbCelebrityReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/Config": {"post": {"operationId": "Api_Config", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbConfigRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbConfigReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/FClaim": {"post": {"summary": "收藏领奖", "operationId": "<PERSON><PERSON>_FClaim", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbFClaimRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbFClaimReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/Favorites": {"post": {"summary": "收藏列表", "operationId": "Api_Favorites", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbFavoritesRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbFavoritesReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/Info": {"post": {"summary": "首页数据", "operationId": "Api_Info", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbInfoReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/Lottery": {"post": {"summary": "抽奖", "operationId": "Api_Lottery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbLotteryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbLotteryReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/LotteryCheck": {"post": {"summary": "抽奖检查", "operationId": "Api_LotteryCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbLotteryCheckRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbLotteryCheckReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/Marquee": {"post": {"summary": "跑马灯", "operationId": "A<PERSON>_<PERSON>e", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbMarqueeRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbMarqueeReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/Records": {"post": {"summary": "获奖记录", "operationId": "Api_Records", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbRecordsRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbRecordsReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/SetAnonymous": {"post": {"summary": "设置匿名开关", "operationId": "Api_SetAnonymous", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbSetAnonymousRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbSetAnonymousReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/TRank": {"post": {"summary": "总榜 (收藏分)", "operationId": "Api_TRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbTRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbTRankReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/WRank": {"post": {"summary": "周榜 (收藏分)", "operationId": "Api_WRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbWRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbWRankReq"}}], "tags": ["Api"]}}, "/xian_kblb.Api/WRankClaim": {"post": {"summary": "周榜领奖", "operationId": "Api_WRank<PERSON>laim", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_kblbWRankClaimRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_kblbWRankClaimReq"}}], "tags": ["Api"]}}}, "definitions": {"adapter_commonGameMiddleInfo": {"type": "object", "properties": {"gameAppId": {"type": "string"}, "gameOpenId": {"type": "string"}, "uid": {"type": "string"}}, "title": "方式二 宿主平台游戏账号体系\n   必填参数：uid"}, "callbackCallInfo": {"type": "object", "properties": {"callBackCmd": {"type": "string"}}}, "callbackCheckInfo": {"type": "object", "properties": {"orderId": {"type": "string", "title": "订单id"}, "giftPackageId": {"type": "string", "title": "礼包id"}, "num": {"type": "integer", "format": "int64", "title": "数量"}, "sendTs": {"type": "integer", "format": "int64", "title": "发放时间 秒"}, "reason": {"type": "string", "title": "发放原因"}, "extId": {"type": "string", "title": "业务方带过来的teaceid 用于链路跟踪，填抽奖ID、任务ID、活动ID"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "透传字段 对应发礼包传入的map_ext"}}, "title": "礼包发货check==================begin\n这里针对订单号需要校验：礼包id、发放人、发放数量是否匹配\n如果正确返回 pass = true\n如果错误返回 pass = false"}, "callbackCommodityItem": {"type": "object", "properties": {"commodityId": {"type": "integer", "format": "int64", "title": "消费的道具id"}, "num": {"type": "integer", "format": "int64", "title": "消费的道具数量"}}, "title": "支付代理delivery==================begin"}, "callbackConsumeInfo": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/callbackCommodityItem"}}, "amount": {"type": "integer", "format": "int64", "title": "总价"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "透传字段"}, "currencyType": {"type": "integer", "format": "int64", "title": "货币类型 0/1=k币"}}}, "callbackGiftPackageBusinessCheckSendReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/adapter_commonGameMiddleInfo"}, "checkInfo": {"$ref": "#/definitions/callbackCheckInfo"}, "callInfo": {"$ref": "#/definitions/callbackCallInfo"}}}, "callbackGiftPackageBusinessCheckSendRsp": {"type": "object", "properties": {"pass": {"type": "boolean", "title": "校验是否通过"}}}, "callbackOrderShipmentReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/adapter_commonGameMiddleInfo", "title": "中台数据"}, "consumeInfo": {"$ref": "#/definitions/callbackConsumeInfo", "title": "消费信息-下单时传入的参数"}, "consumeId": {"type": "string", "title": "订单id"}, "vecData": {"type": "string", "format": "byte", "title": "业务透传数据-下单时传入的参数"}, "payScene": {"type": "integer", "format": "int64", "title": "付费场景 1=直播 2=歌房 3=异步作品"}, "paySceneData": {"type": "string", "format": "byte", "title": "付费场景数据-云上应该暂时用不到，先只透传吧"}, "businessId": {"type": "string", "format": "int64", "title": "支付businessid，支付平台分配"}}}, "callbackOrderShipmentRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "xian_kblbARankReq": {"type": "object", "properties": {"weekId": {"type": "string", "format": "int64", "title": "周期id, 第一页可不传,不传就是当前周期,翻页需要透传rsp中的weekId"}, "passback": {"type": "integer", "format": "int32", "title": "开始索引, 初始为0"}}}, "xian_kblbARankRsp": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_kblbRankItem"}, "title": "排行榜列表"}, "hasNext": {"type": "boolean", "title": "是否还有下一页"}, "passback": {"type": "integer", "format": "int32", "title": "请求下一页把这个在RankReq中传过来"}, "refreshTs": {"type": "string", "format": "int64", "title": "刷新时间点"}, "weekId": {"type": "string", "format": "int64", "title": "周期id, 翻页透传"}, "ts": {"type": "string", "format": "int64", "title": "服务器当前时间戳"}}}, "xian_kblbAddFavoriteReq": {"type": "object", "properties": {"id": {"type": "string", "format": "int64", "title": "礼物id"}, "num": {"type": "string", "format": "int64", "title": "获得数量"}}}, "xian_kblbAddFavoriteRsp": {"type": "object"}, "xian_kblbBalanceReq": {"type": "object"}, "xian_kblbBalanceRsp": {"type": "object", "properties": {"ptBalance": {"type": "string", "format": "int64"}}}, "xian_kblbCategory": {"type": "object", "properties": {"categoryId": {"type": "integer", "format": "int64", "title": "分类: 1-珍宝,2-祥瑞,3-神仙,4-秘宝 id"}, "typeId": {"type": "integer", "format": "int32", "title": "类别id"}, "title": {"type": "string", "title": "分类: 1-珍宝,2-祥瑞,3-神仙,4-秘宝"}, "isAck": {"type": "boolean", "title": "是否领奖"}, "canAck": {"type": "boolean", "title": "是否可领奖"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_kblbXItem"}, "title": "该标签下列表,包含有的和没有的"}, "giftPackId": {"type": "string", "format": "int64", "title": "礼物包id"}, "hadUser": {"type": "integer", "format": "int32", "title": "集齐的用户"}, "hadNum": {"type": "integer", "format": "int32", "title": "收集的数量"}}, "title": "分类"}, "xian_kblbCelebrityReq": {"type": "object", "properties": {"category": {"type": "integer", "format": "int64", "title": "分类: 1-珍宝,2-祥瑞,3-神仙,4-秘宝"}, "passback": {"type": "integer", "format": "int32", "title": "开始索引, 初始为0"}}}, "xian_kblbCelebrityRsp": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_kblbRankItem"}, "title": "排行榜列表"}, "hasNext": {"type": "boolean", "title": "是否还有下一页"}, "passback": {"type": "integer", "format": "int32", "title": "请求下一页把这个在RankReq中传过来"}, "self": {"$ref": "#/definitions/xian_kblbRankItem", "title": "自己的排行榜数据"}, "isClaimed": {"type": "boolean", "title": "该分类是否已领奖"}}}, "xian_kblbConfigReq": {"type": "object", "properties": {"giftVersion": {"type": "integer", "format": "int32", "title": "礼物版本"}, "mapVersion": {"type": "integer", "format": "int32", "title": "映射版本"}, "pkgVersion": {"type": "integer", "format": "int32", "title": "礼物包版本"}}}, "xian_kblbConfigRsp": {"type": "object", "properties": {"giftVersion": {"type": "integer", "format": "int32", "title": "服务器-礼物版本"}, "mapVersion": {"type": "integer", "format": "int32", "title": "服务器-映射版本"}, "pkgVersion": {"type": "integer", "format": "int32", "title": "服务器-礼物包版本"}, "favoritesVersion": {"type": "integer", "format": "int32", "title": "服务器-图鉴版本"}, "announceType": {"type": "integer", "format": "int32", "title": "公告类型:0-关闭,1-预告,2-公告"}, "gifts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_kblbGift"}, "title": "礼物列表"}, "m": {"$ref": "#/definitions/xian_kblbOptSlot", "title": "映射配置"}, "ggs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_kblbGiftGroup"}, "title": "礼物包列表"}}}, "xian_kblbFClaimReq": {"type": "object", "properties": {"typeId": {"type": "integer", "format": "int32"}, "anchorId": {"type": "string", "title": "主播ID"}, "roomId": {"type": "string", "title": "房间ID"}, "showId": {"type": "string"}, "ugcId": {"type": "string"}, "payScene": {"type": "integer", "format": "int32", "title": "支付场景: 0-未知,1-直播,2-歌房"}}}, "xian_kblbFClaimRsp": {"type": "object", "properties": {"giftPackId": {"type": "string", "format": "int64", "title": "礼物包id"}, "hasAckGift": {"type": "boolean", "title": "图鉴是否还有奖可领"}}}, "xian_kblbFavoritesReq": {"type": "object", "properties": {"openid": {"type": "string", "title": "查询图鉴目标, 未传查自己"}}}, "xian_kblbFavoritesRsp": {"type": "object", "properties": {"version": {"type": "integer", "format": "int32", "title": "图鉴版本 - 记ckv"}, "progress": {"type": "integer", "format": "int32", "title": "当前收集到的礼物数量"}, "giftPackId": {"type": "string", "format": "int64", "title": "礼物包id"}, "list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_kblbCategory"}, "title": "套系列表"}, "isAck": {"type": "boolean", "title": "是否领取过收集奖励 - 记ckv"}, "data": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_kblbXItem"}, "title": "用户数据，前端不用 - 记ckv"}, "canAck": {"type": "boolean", "title": "是否可领奖"}, "all": {"type": "integer", "format": "int32", "title": "总数"}}}, "xian_kblbGGItem": {"type": "object", "properties": {"rewardId": {"type": "string", "title": "奖励ID"}, "rewardNum": {"type": "string", "format": "int64", "title": "数量"}, "rewardType": {"type": "string", "format": "int64", "title": "奖励类型"}, "unitPrice": {"type": "string", "format": "int64", "title": "单价"}, "rewardName": {"type": "string", "title": "资产名称"}, "rewardLogo": {"type": "string", "title": "资产icon"}, "universal": {"type": "string", "title": "万能字段 透传配置系统上配置的信息"}}, "title": "礼包资产"}, "xian_kblbGift": {"type": "object", "properties": {"giftId": {"type": "string", "format": "int64", "title": "礼物ID"}, "giftPrice": {"type": "string", "format": "int64", "title": "礼物单价"}, "giftName": {"type": "string", "title": "礼物名称"}, "giftIcon": {"type": "string", "title": "礼物图标 160x160"}, "giftIcon360": {"type": "string", "title": "礼物图标 360x360"}, "lv": {"type": "integer", "format": "int32", "title": "档位, 1,2,3"}, "category": {"type": "integer", "format": "int32", "title": "分类: 1-珍宝,2-祥瑞,3-神仙,4-秘宝"}, "isSuper": {"type": "boolean", "title": "是否超特礼物"}, "giftAnimationId": {"type": "string", "format": "int64", "title": "动画字段id"}, "animPrice": {"type": "string", "format": "int64", "title": "动画价值"}, "giftDiscount": {"type": "string", "title": "礼物折扣"}}, "title": "平台礼物信息"}, "xian_kblbGiftGroup": {"type": "object", "properties": {"id": {"type": "string", "format": "int64", "title": "礼包ID"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_kblbGGItem"}, "description": "礼包内包含资产列表", "title": "int64 series_id = 2; // 礼包序列ID，标识礼包ID属于某个系列活动"}}, "title": "平台礼物包信息"}, "xian_kblbInfoReq": {"type": "object"}, "xian_kblbInfoRsp": {"type": "object", "properties": {"happyMax": {"type": "integer", "format": "int32", "title": "降临时刻最大值"}, "happyValue": {"type": "integer", "format": "int32", "title": "福气值"}, "happyEnd": {"type": "string", "format": "int64", "title": "降临时刻结束时间"}, "anonymous": {"type": "integer", "format": "int32", "title": "匿名开关: 0-关(展示昵称), 1-开"}, "weekId": {"type": "string", "format": "int64", "title": "周id"}, "refreshTs": {"type": "string", "format": "int64", "title": "刷新时间点"}, "st": {"type": "string", "format": "int64", "title": "服务器时间"}, "favoriteIsNew": {"type": "boolean", "title": "是否有新图鉴"}, "favoriteIsAck": {"type": "boolean", "title": "是否新图鉴可领奖"}, "ptBalance": {"type": "string", "format": "int64", "title": "平台货币余额"}}}, "xian_kblbLotteryCheckReq": {"type": "object", "properties": {"tid": {"type": "string", "title": "订单ID"}}}, "xian_kblbLotteryCheckRsp": {"type": "object", "properties": {"status": {"type": "integer", "format": "int32", "title": "订单状态"}}}, "xian_kblbLotteryReq": {"type": "object", "properties": {"tid": {"type": "string", "title": "订单ID"}, "lv": {"type": "integer", "format": "int32", "title": "档位: 1,2,3"}, "mode": {"type": "integer", "format": "int32", "title": "模式: 0-普通, 1-福神"}, "anchorId": {"type": "string", "title": "主播ID"}, "roomId": {"type": "string", "title": "房间ID"}, "showId": {"type": "string"}, "ugcId": {"type": "string"}, "payScene": {"type": "integer", "format": "int32", "title": "支付场景: 0-未知,1-直播,2-歌房"}, "target": {"type": "string", "title": "送礼目标"}, "position": {"type": "integer", "format": "int32", "title": "麦位(Q音)"}}}, "xian_kblbLotteryRsp": {"type": "object", "properties": {"evalue": {"type": "string", "title": "枚举值"}, "rewards": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "奖励列表"}, "happyValue": {"type": "string", "format": "int64", "title": "当前福气值"}, "happyEnd": {"type": "string", "format": "int64", "title": "降临时刻: 结束时间, 作为Info补充, 仅开启的这次返回"}, "happyLvs": {"type": "array", "items": {"type": "integer", "format": "int32"}, "title": "降临时刻: 福神3个档位礼物"}, "version": {"type": "string", "format": "int64", "title": "版本"}, "weekRank": {"type": "string", "format": "int64", "title": "玩家周榜名次"}, "st": {"type": "string", "format": "int64", "title": "服务器时间"}, "hasNewGift": {"type": "boolean", "title": "图鉴是否有新礼物"}, "hasAckGift": {"type": "boolean", "title": "图鉴是否有奖可领"}, "weekId": {"type": "string", "title": "周期id"}, "balance": {"type": "string", "format": "int64", "title": "剩余货币数量"}}}, "xian_kblbMarqueeReq": {"type": "object"}, "xian_kblbMarqueeRsp": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_kblbXMsg"}, "title": "跑马灯列表"}}}, "xian_kblbOptSlot": {"type": "object", "properties": {"slots": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "title": "槽位映射, key=lv-[A-D], value=gift_id"}, "prices": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "title": "档位价格"}}}, "xian_kblbRankItem": {"type": "object", "properties": {"rank": {"type": "integer", "format": "int32", "title": "排行: 从1开始"}, "openid": {"type": "string"}, "name": {"type": "string"}, "avatar": {"type": "string"}, "gender": {"type": "integer", "format": "int32"}, "score": {"type": "string", "format": "int64"}, "freshTime": {"type": "string", "format": "int64"}, "giftPackId": {"type": "string", "format": "int64", "title": "礼物包id"}, "anonymous": {"type": "boolean"}, "level": {"type": "integer", "format": "int32", "title": "奖励级别"}, "uid": {"type": "string", "title": "玩家平台uid"}}}, "xian_kblbRecord": {"type": "object", "properties": {"rt": {"type": "integer", "format": "int32", "title": "记录类型 1-抽奖; 2-周榜领奖; 3-图鉴领奖"}, "createTime": {"type": "string", "format": "int64", "title": "创建时间"}, "rank": {"type": "integer", "format": "int32", "title": "排名"}, "cost": {"type": "integer", "format": "int32", "title": "消耗平台币"}, "fType": {"type": "integer", "format": "int32", "title": "收藏类型"}, "giftPackId": {"type": "string", "format": "int64", "title": "礼物包id"}, "giftIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "礼物id列表"}, "lv": {"type": "integer", "format": "int32", "title": "抽奖lv"}, "happy": {"type": "boolean", "title": "是否是福神时刻, true-是"}}}, "xian_kblbRecordsReq": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int32", "title": "开始索引, 初始为0"}}}, "xian_kblbRecordsRsp": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_kblbRecord"}, "title": "记录列表"}, "hasNext": {"type": "boolean", "title": "是否还有下一页"}, "passback": {"type": "integer", "format": "int32", "title": "请求下一页把这个在RecordsReq中传过来"}, "anonymous": {"type": "integer", "format": "int32", "title": "匿名开关: 0-关(展示昵称), 1-开"}}}, "xian_kblbSetAnonymousReq": {"type": "object", "properties": {"value": {"type": "integer", "format": "int32", "title": "开关: 0-关(展示昵称), 1-开"}}}, "xian_kblbSetAnonymousRsp": {"type": "object"}, "xian_kblbTRankReq": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int32", "title": "开始索引, 初始为0"}, "needSelf": {"type": "boolean", "title": "需要自己的信息"}}}, "xian_kblbTRankRsp": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_kblbRankItem"}, "title": "排行榜列表"}, "self": {"$ref": "#/definitions/xian_kblbRankItem", "title": "自己的排行榜数据"}, "hasNext": {"type": "boolean", "title": "是否还有下一页"}, "passback": {"type": "integer", "format": "int32", "title": "请求下一页把这个在RankReq中传过来"}}}, "xian_kblbWRankClaimReq": {"type": "object"}, "xian_kblbWRankClaimRsp": {"type": "object", "properties": {"weekId": {"type": "string", "format": "int64", "title": "周id"}, "giftPackId": {"type": "string", "format": "int64", "title": "礼物包id"}, "rank": {"type": "integer", "format": "int32", "title": "排名"}}}, "xian_kblbWRankReq": {"type": "object", "properties": {"weekId": {"type": "integer", "format": "int32", "title": "周id, 第一页可不传, 之后翻页透传rsp中返回的"}, "passback": {"type": "integer", "format": "int32", "title": "开始索引, 初始为0"}, "needSelf": {"type": "boolean", "title": "需要自己的信息"}}}, "xian_kblbWRankRsp": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_kblbRankItem"}, "title": "排行榜列表"}, "self": {"$ref": "#/definitions/xian_kblbRankItem", "title": "自己的排行榜数据"}, "hasNext": {"type": "boolean", "title": "是否还有下一页"}, "passback": {"type": "integer", "format": "int32", "title": "请求下一页把这个在RankReq中传过来"}, "weekId": {"type": "string", "format": "int64", "title": "周期id, 翻页透传"}, "refreshTs": {"type": "string", "format": "int64", "title": "刷新时间点"}, "ts": {"type": "string", "format": "int64", "title": "服务器当前时间戳"}, "topn": {"type": "integer", "format": "int32", "title": "前多少名有奖励"}}}, "xian_kblbXItem": {"type": "object", "properties": {"id": {"type": "string", "format": "int64", "title": "礼物id"}, "num": {"type": "string", "format": "int64", "title": "获得数量"}, "isNew": {"type": "boolean", "title": "是否第一次获得"}}, "title": "收藏 - 礼物"}, "xian_kblbXMsg": {"type": "object", "properties": {"name": {"type": "string", "title": "昵称"}, "rname": {"type": "string", "title": "礼物名称"}, "price": {"type": "integer", "format": "int32", "title": "价值"}, "aprice": {"type": "integer", "format": "int32", "title": "动画价值"}}}}}