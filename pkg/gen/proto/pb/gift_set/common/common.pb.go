// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/gift_set/common/common.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GiftSetCombineLocation int32

const (
	GiftSetCombineLocation_GiftSetCombineLocationUnknow GiftSetCombineLocation = 0
	GiftSetCombineLocation_GiftSetCombineLocationClient GiftSetCombineLocation = 1 // 客户端
	GiftSetCombineLocation_GiftSetCombineLocationWeb    GiftSetCombineLocation = 2 // web
)

// Enum value maps for GiftSetCombineLocation.
var (
	GiftSetCombineLocation_name = map[int32]string{
		0: "GiftSetCombineLocationUnknow",
		1: "GiftSetCombineLocationClient",
		2: "GiftSetCombineLocationWeb",
	}
	GiftSetCombineLocation_value = map[string]int32{
		"GiftSetCombineLocationUnknow": 0,
		"GiftSetCombineLocationClient": 1,
		"GiftSetCombineLocationWeb":    2,
	}
)

func (x GiftSetCombineLocation) Enum() *GiftSetCombineLocation {
	p := new(GiftSetCombineLocation)
	*p = x
	return p
}

func (x GiftSetCombineLocation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GiftSetCombineLocation) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_gift_set_common_common_proto_enumTypes[0].Descriptor()
}

func (GiftSetCombineLocation) Type() protoreflect.EnumType {
	return &file_pb_gift_set_common_common_proto_enumTypes[0]
}

func (x GiftSetCombineLocation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GiftSetCombineLocation.Descriptor instead.
func (GiftSetCombineLocation) EnumDescriptor() ([]byte, []int) {
	return file_pb_gift_set_common_common_proto_rawDescGZIP(), []int{0}
}

type HighlightPrivateStatus int32

const (
	HighlightPrivateStatus_HighlightPrivateStatusUnknow     HighlightPrivateStatus = 0
	HighlightPrivateStatus_HighlightPrivateStatusDisclosure HighlightPrivateStatus = 1 // 公开
	HighlightPrivateStatus_HighlightPrivateStatusHidden     HighlightPrivateStatus = 2 // 隐藏
)

// Enum value maps for HighlightPrivateStatus.
var (
	HighlightPrivateStatus_name = map[int32]string{
		0: "HighlightPrivateStatusUnknow",
		1: "HighlightPrivateStatusDisclosure",
		2: "HighlightPrivateStatusHidden",
	}
	HighlightPrivateStatus_value = map[string]int32{
		"HighlightPrivateStatusUnknow":     0,
		"HighlightPrivateStatusDisclosure": 1,
		"HighlightPrivateStatusHidden":     2,
	}
)

func (x HighlightPrivateStatus) Enum() *HighlightPrivateStatus {
	p := new(HighlightPrivateStatus)
	*p = x
	return p
}

func (x HighlightPrivateStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HighlightPrivateStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_gift_set_common_common_proto_enumTypes[1].Descriptor()
}

func (HighlightPrivateStatus) Type() protoreflect.EnumType {
	return &file_pb_gift_set_common_common_proto_enumTypes[1]
}

func (x HighlightPrivateStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HighlightPrivateStatus.Descriptor instead.
func (HighlightPrivateStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_gift_set_common_common_proto_rawDescGZIP(), []int{1}
}

type HighlightRecordItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sender       *HighlightRecordItem_UserInfo `protobuf:"bytes,1,opt,name=sender,proto3" json:"sender,omitempty"`             // 送礼人
	Receiver     *HighlightRecordItem_UserInfo `protobuf:"bytes,2,opt,name=receiver,proto3" json:"receiver,omitempty"`         // 收礼人
	ResourceInfo *ResourceInfo                 `protobuf:"bytes,3,opt,name=resourceInfo,proto3" json:"resourceInfo,omitempty"` // 动画
	SetId        uint32                        `protobuf:"varint,4,opt,name=setId,proto3" json:"setId,omitempty"`              // 套系id
	Num          uint32                        `protobuf:"varint,5,opt,name=num,proto3" json:"num,omitempty"`                  // 数量
	UTs          uint32                        `protobuf:"varint,6,opt,name=uTs,proto3" json:"uTs,omitempty"`                  // 送礼时间
}

func (x *HighlightRecordItem) Reset() {
	*x = HighlightRecordItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gift_set_common_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HighlightRecordItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HighlightRecordItem) ProtoMessage() {}

func (x *HighlightRecordItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gift_set_common_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HighlightRecordItem.ProtoReflect.Descriptor instead.
func (*HighlightRecordItem) Descriptor() ([]byte, []int) {
	return file_pb_gift_set_common_common_proto_rawDescGZIP(), []int{0}
}

func (x *HighlightRecordItem) GetSender() *HighlightRecordItem_UserInfo {
	if x != nil {
		return x.Sender
	}
	return nil
}

func (x *HighlightRecordItem) GetReceiver() *HighlightRecordItem_UserInfo {
	if x != nil {
		return x.Receiver
	}
	return nil
}

func (x *HighlightRecordItem) GetResourceInfo() *ResourceInfo {
	if x != nil {
		return x.ResourceInfo
	}
	return nil
}

func (x *HighlightRecordItem) GetSetId() uint32 {
	if x != nil {
		return x.SetId
	}
	return 0
}

func (x *HighlightRecordItem) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *HighlightRecordItem) GetUTs() uint32 {
	if x != nil {
		return x.UTs
	}
	return 0
}

type ResourceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceId  int32  `protobuf:"varint,1,opt,name=resourceId,proto3" json:"resourceId,omitempty"`  //资源ID
	ResourceUrl string `protobuf:"bytes,2,opt,name=resourceUrl,proto3" json:"resourceUrl,omitempty"` //资源Url
	ResourceMd5 string `protobuf:"bytes,3,opt,name=resourceMd5,proto3" json:"resourceMd5,omitempty"` //资源Md5
}

func (x *ResourceInfo) Reset() {
	*x = ResourceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gift_set_common_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceInfo) ProtoMessage() {}

func (x *ResourceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gift_set_common_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceInfo.ProtoReflect.Descriptor instead.
func (*ResourceInfo) Descriptor() ([]byte, []int) {
	return file_pb_gift_set_common_common_proto_rawDescGZIP(), []int{1}
}

func (x *ResourceInfo) GetResourceId() int32 {
	if x != nil {
		return x.ResourceId
	}
	return 0
}

func (x *ResourceInfo) GetResourceUrl() string {
	if x != nil {
		return x.ResourceUrl
	}
	return ""
}

func (x *ResourceInfo) GetResourceMd5() string {
	if x != nil {
		return x.ResourceMd5
	}
	return ""
}

// 礼物信息
type Gift struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftId       int64         `protobuf:"varint,1,opt,name=giftId,proto3" json:"giftId,omitempty"`            // 礼物ID
	GiftLogo     string        `protobuf:"bytes,2,opt,name=giftLogo,proto3" json:"giftLogo,omitempty"`         // 礼物logo
	GiftName     string        `protobuf:"bytes,3,opt,name=giftName,proto3" json:"giftName,omitempty"`         // 礼物名称
	Price        int32         `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`              // 价格
	ResourceInfo *ResourceInfo `protobuf:"bytes,5,opt,name=resourceInfo,proto3" json:"resourceInfo,omitempty"` // 资源信息
}

func (x *Gift) Reset() {
	*x = Gift{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gift_set_common_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gift) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gift) ProtoMessage() {}

func (x *Gift) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gift_set_common_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gift.ProtoReflect.Descriptor instead.
func (*Gift) Descriptor() ([]byte, []int) {
	return file_pb_gift_set_common_common_proto_rawDescGZIP(), []int{2}
}

func (x *Gift) GetGiftId() int64 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

func (x *Gift) GetGiftLogo() string {
	if x != nil {
		return x.GiftLogo
	}
	return ""
}

func (x *Gift) GetGiftName() string {
	if x != nil {
		return x.GiftName
	}
	return ""
}

func (x *Gift) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Gift) GetResourceInfo() *ResourceInfo {
	if x != nil {
		return x.ResourceInfo
	}
	return nil
}

// 套系信息
type GiftSetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SetId           int32             `protobuf:"varint,1,opt,name=setId,proto3" json:"setId,omitempty"`
	Gifts           []*Gift           `protobuf:"bytes,2,rep,name=gifts,proto3" json:"gifts,omitempty"`                                                                                               //礼物信息
	DiscountGifts   []*Gift           `protobuf:"bytes,3,rep,name=discountGifts,proto3" json:"discountGifts,omitempty"`                                                                               //折扣礼物信息
	DiscountBeginTs int32             `protobuf:"varint,4,opt,name=discountBeginTs,proto3" json:"discountBeginTs,omitempty"`                                                                          //折扣开始时间
	DiscountEndTs   int32             `protobuf:"varint,5,opt,name=discountEndTs,proto3" json:"discountEndTs,omitempty"`                                                                              //折扣结束时间
	StrSetName      string            `protobuf:"bytes,6,opt,name=strSetName,proto3" json:"strSetName,omitempty"`                                                                                     //套系名称
	StrSetLogo      string            `protobuf:"bytes,7,opt,name=strSetLogo,proto3" json:"strSetLogo,omitempty"`                                                                                     //套系logo
	Price           int32             `protobuf:"varint,8,opt,name=price,proto3" json:"price,omitempty"`                                                                                              //套系总价格
	MapBonus        map[string]string `protobuf:"bytes,9,rep,name=mapBonus,proto3" json:"mapBonus,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //bonus
	// *************** kg ******************
	HighlightResource *ResourceInfo `protobuf:"bytes,10,opt,name=highlightResource,proto3" json:"highlightResource,omitempty"` //高光资源信息
	Lantern           []string      `protobuf:"bytes,11,rep,name=lantern,proto3" json:"lantern,omitempty"`                     //跑马灯
	SetGift           *Gift         `protobuf:"bytes,12,opt,name=setGift,proto3" json:"setGift,omitempty"`                     //套系礼物信息
	DiscountSetGift   *Gift         `protobuf:"bytes,13,opt,name=discountSetGift,proto3" json:"discountSetGift,omitempty"`     //折扣套系礼物信息
	JumpUrl           string        `protobuf:"bytes,14,opt,name=jumpUrl,proto3" json:"jumpUrl,omitempty"`                     //跳转链接
}

func (x *GiftSetInfo) Reset() {
	*x = GiftSetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gift_set_common_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftSetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftSetInfo) ProtoMessage() {}

func (x *GiftSetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gift_set_common_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftSetInfo.ProtoReflect.Descriptor instead.
func (*GiftSetInfo) Descriptor() ([]byte, []int) {
	return file_pb_gift_set_common_common_proto_rawDescGZIP(), []int{3}
}

func (x *GiftSetInfo) GetSetId() int32 {
	if x != nil {
		return x.SetId
	}
	return 0
}

func (x *GiftSetInfo) GetGifts() []*Gift {
	if x != nil {
		return x.Gifts
	}
	return nil
}

func (x *GiftSetInfo) GetDiscountGifts() []*Gift {
	if x != nil {
		return x.DiscountGifts
	}
	return nil
}

func (x *GiftSetInfo) GetDiscountBeginTs() int32 {
	if x != nil {
		return x.DiscountBeginTs
	}
	return 0
}

func (x *GiftSetInfo) GetDiscountEndTs() int32 {
	if x != nil {
		return x.DiscountEndTs
	}
	return 0
}

func (x *GiftSetInfo) GetStrSetName() string {
	if x != nil {
		return x.StrSetName
	}
	return ""
}

func (x *GiftSetInfo) GetStrSetLogo() string {
	if x != nil {
		return x.StrSetLogo
	}
	return ""
}

func (x *GiftSetInfo) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *GiftSetInfo) GetMapBonus() map[string]string {
	if x != nil {
		return x.MapBonus
	}
	return nil
}

func (x *GiftSetInfo) GetHighlightResource() *ResourceInfo {
	if x != nil {
		return x.HighlightResource
	}
	return nil
}

func (x *GiftSetInfo) GetLantern() []string {
	if x != nil {
		return x.Lantern
	}
	return nil
}

func (x *GiftSetInfo) GetSetGift() *Gift {
	if x != nil {
		return x.SetGift
	}
	return nil
}

func (x *GiftSetInfo) GetDiscountSetGift() *Gift {
	if x != nil {
		return x.DiscountSetGift
	}
	return nil
}

func (x *GiftSetInfo) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

// 组合信息
type CombineInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gifts         []*CombineInfo_GiftItem `protobuf:"bytes,1,rep,name=gifts,proto3" json:"gifts,omitempty"`                  //套系礼物信息
	RemainValidTs int32                   `protobuf:"varint,2,opt,name=remainValidTs,proto3" json:"remainValidTs,omitempty"` //剩余有效时间
}

func (x *CombineInfo) Reset() {
	*x = CombineInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gift_set_common_common_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CombineInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CombineInfo) ProtoMessage() {}

func (x *CombineInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gift_set_common_common_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CombineInfo.ProtoReflect.Descriptor instead.
func (*CombineInfo) Descriptor() ([]byte, []int) {
	return file_pb_gift_set_common_common_proto_rawDescGZIP(), []int{4}
}

func (x *CombineInfo) GetGifts() []*CombineInfo_GiftItem {
	if x != nil {
		return x.Gifts
	}
	return nil
}

func (x *CombineInfo) GetRemainValidTs() int32 {
	if x != nil {
		return x.RemainValidTs
	}
	return 0
}

type CombineBill struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SetId             int32             `protobuf:"varint,1,opt,name=setId,proto3" json:"setId,omitempty"`
	Sender            string            `protobuf:"bytes,2,opt,name=sender,proto3" json:"sender,omitempty"`
	Receiver          string            `protobuf:"bytes,3,opt,name=receiver,proto3" json:"receiver,omitempty"`
	CombinedMs        int64             `protobuf:"varint,4,opt,name=combinedMs,proto3" json:"combinedMs,omitempty"`   //成套时间
	CombinedNum       int32             `protobuf:"varint,5,opt,name=combinedNum,proto3" json:"combinedNum,omitempty"` //成套数量
	OrderId           string            `protobuf:"bytes,6,opt,name=orderId,proto3" json:"orderId,omitempty"`          //订单ID
	RoomId            string            `protobuf:"bytes,7,opt,name=roomId,proto3" json:"roomId,omitempty"`            //房间ID
	ShowId            string            `protobuf:"bytes,8,opt,name=showId,proto3" json:"showId,omitempty"`            //场次ID
	UgcId             string            `protobuf:"bytes,9,opt,name=ugcId,proto3" json:"ugcId,omitempty"`              //作品ID
	SetName           string            `protobuf:"bytes,10,opt,name=setName,proto3" json:"setName,omitempty"`         //套系名称
	SetLogo           string            `protobuf:"bytes,11,opt,name=setLogo,proto3" json:"setLogo,omitempty"`         //套系logo
	SetPrice          int32             `protobuf:"varint,12,opt,name=setPrice,proto3" json:"setPrice,omitempty"`      //套系价格，套系本身的价值
	GiftIds           []int64           `protobuf:"varint,13,rep,packed,name=giftIds,proto3" json:"giftIds,omitempty"` //成套礼物ID
	MapBonus          map[string]string `protobuf:"bytes,14,rep,name=mapBonus,proto3" json:"mapBonus,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	HighlightResource *ResourceInfo     `protobuf:"bytes,15,opt,name=highlightResource,proto3" json:"highlightResource,omitempty"` //高光资源信息
	BFullSet          bool              `protobuf:"varint,16,opt,name=bFullSet,proto3" json:"bFullSet,omitempty"`                  //是否全套送
	PayScene          uint32            `protobuf:"varint,17,opt,name=payScene,proto3" json:"payScene,omitempty"`                  //送礼场景
	Location          uint32            `protobuf:"varint,18,opt,name=location,proto3" json:"location,omitempty"`                  //送礼位置 参考GiftSetCombineLocation
}

func (x *CombineBill) Reset() {
	*x = CombineBill{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gift_set_common_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CombineBill) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CombineBill) ProtoMessage() {}

func (x *CombineBill) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gift_set_common_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CombineBill.ProtoReflect.Descriptor instead.
func (*CombineBill) Descriptor() ([]byte, []int) {
	return file_pb_gift_set_common_common_proto_rawDescGZIP(), []int{5}
}

func (x *CombineBill) GetSetId() int32 {
	if x != nil {
		return x.SetId
	}
	return 0
}

func (x *CombineBill) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *CombineBill) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

func (x *CombineBill) GetCombinedMs() int64 {
	if x != nil {
		return x.CombinedMs
	}
	return 0
}

func (x *CombineBill) GetCombinedNum() int32 {
	if x != nil {
		return x.CombinedNum
	}
	return 0
}

func (x *CombineBill) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *CombineBill) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *CombineBill) GetShowId() string {
	if x != nil {
		return x.ShowId
	}
	return ""
}

func (x *CombineBill) GetUgcId() string {
	if x != nil {
		return x.UgcId
	}
	return ""
}

func (x *CombineBill) GetSetName() string {
	if x != nil {
		return x.SetName
	}
	return ""
}

func (x *CombineBill) GetSetLogo() string {
	if x != nil {
		return x.SetLogo
	}
	return ""
}

func (x *CombineBill) GetSetPrice() int32 {
	if x != nil {
		return x.SetPrice
	}
	return 0
}

func (x *CombineBill) GetGiftIds() []int64 {
	if x != nil {
		return x.GiftIds
	}
	return nil
}

func (x *CombineBill) GetMapBonus() map[string]string {
	if x != nil {
		return x.MapBonus
	}
	return nil
}

func (x *CombineBill) GetHighlightResource() *ResourceInfo {
	if x != nil {
		return x.HighlightResource
	}
	return nil
}

func (x *CombineBill) GetBFullSet() bool {
	if x != nil {
		return x.BFullSet
	}
	return false
}

func (x *CombineBill) GetPayScene() uint32 {
	if x != nil {
		return x.PayScene
	}
	return 0
}

func (x *CombineBill) GetLocation() uint32 {
	if x != nil {
		return x.Location
	}
	return 0
}

type HighlightRecordItem_UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nickname string `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname,omitempty"` // 姓名
	Avatar   string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`     // 头像
	Uid      string `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,omitempty"`           // uid
}

func (x *HighlightRecordItem_UserInfo) Reset() {
	*x = HighlightRecordItem_UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gift_set_common_common_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HighlightRecordItem_UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HighlightRecordItem_UserInfo) ProtoMessage() {}

func (x *HighlightRecordItem_UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gift_set_common_common_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HighlightRecordItem_UserInfo.ProtoReflect.Descriptor instead.
func (*HighlightRecordItem_UserInfo) Descriptor() ([]byte, []int) {
	return file_pb_gift_set_common_common_proto_rawDescGZIP(), []int{0, 0}
}

func (x *HighlightRecordItem_UserInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *HighlightRecordItem_UserInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *HighlightRecordItem_UserInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type CombineInfo_GiftItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gift           *Gift `protobuf:"bytes,1,opt,name=gift,proto3" json:"gift,omitempty"`                      //套系礼物信息
	AlreadySendNum int32 `protobuf:"varint,2,opt,name=alreadySendNum,proto3" json:"alreadySendNum,omitempty"` //已经送出的数量
}

func (x *CombineInfo_GiftItem) Reset() {
	*x = CombineInfo_GiftItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gift_set_common_common_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CombineInfo_GiftItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CombineInfo_GiftItem) ProtoMessage() {}

func (x *CombineInfo_GiftItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gift_set_common_common_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CombineInfo_GiftItem.ProtoReflect.Descriptor instead.
func (*CombineInfo_GiftItem) Descriptor() ([]byte, []int) {
	return file_pb_gift_set_common_common_proto_rawDescGZIP(), []int{4, 0}
}

func (x *CombineInfo_GiftItem) GetGift() *Gift {
	if x != nil {
		return x.Gift
	}
	return nil
}

func (x *CombineInfo_GiftItem) GetAlreadySendNum() int32 {
	if x != nil {
		return x.AlreadySendNum
	}
	return 0
}

var File_pb_gift_set_common_common_proto protoreflect.FileDescriptor

var file_pb_gift_set_common_common_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x70, 0x62, 0x2f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x08, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x22, 0xe1, 0x02, 0x0a, 0x13,
	0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x2e, 0x48,
	0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x73, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x73, 0x65, 0x74,
	0x2e, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x67, 0x69, 0x66, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x54, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x75, 0x54, 0x73, 0x1a, 0x50, 0x0a,
	0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63,
	0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63,
	0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22,
	0x72, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x72,
	0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x64, 0x35,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x64, 0x35, 0x22, 0xa8, 0x01, 0x0a, 0x04, 0x47, 0x69, 0x66, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x67, 0x69,
	0x66, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x69, 0x66, 0x74, 0x4c, 0x6f, 0x67, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x4c, 0x6f, 0x67, 0x6f,
	0x12, 0x1a, 0x0a, 0x08, 0x67, 0x69, 0x66, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x69, 0x66, 0x74, 0x5f,
	0x73, 0x65, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x81,
	0x05, 0x0a, 0x0b, 0x47, 0x69, 0x66, 0x74, 0x53, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x05, 0x67, 0x69, 0x66, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x2e, 0x47,
	0x69, 0x66, 0x74, 0x52, 0x05, 0x67, 0x69, 0x66, 0x74, 0x73, 0x12, 0x34, 0x0a, 0x0d, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x69, 0x66, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x2e, 0x47, 0x69, 0x66,
	0x74, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x69, 0x66, 0x74, 0x73,
	0x12, 0x28, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x67, 0x69,
	0x6e, 0x54, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x53, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x53, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x53, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x53, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x6f,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x6d, 0x61, 0x70, 0x42, 0x6f, 0x6e,
	0x75, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x69, 0x66, 0x74, 0x5f,
	0x73, 0x65, 0x74, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x53, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e,
	0x4d, 0x61, 0x70, 0x42, 0x6f, 0x6e, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d,
	0x61, 0x70, 0x42, 0x6f, 0x6e, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x11, 0x68, 0x69, 0x67, 0x68, 0x6c,
	0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11, 0x68, 0x69, 0x67, 0x68,
	0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6c, 0x61, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x6c, 0x61, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x28, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x47, 0x69,
	0x66, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x67, 0x69, 0x66, 0x74, 0x5f,
	0x73, 0x65, 0x74, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x52, 0x07, 0x73, 0x65, 0x74, 0x47, 0x69, 0x66,
	0x74, 0x12, 0x38, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74,
	0x47, 0x69, 0x66, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x67, 0x69, 0x66,
	0x74, 0x5f, 0x73, 0x65, 0x74, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x47, 0x69, 0x66, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6a,
	0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x75,
	0x6d, 0x70, 0x55, 0x72, 0x6c, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x61, 0x70, 0x42, 0x6f, 0x6e, 0x75,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xc1, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x34, 0x0a, 0x05, 0x67, 0x69, 0x66, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6d,
	0x62, 0x69, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x05, 0x67, 0x69, 0x66, 0x74, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x61,
	0x69, 0x6e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x54, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x54, 0x73, 0x1a, 0x56,
	0x0a, 0x08, 0x47, 0x69, 0x66, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x22, 0x0a, 0x04, 0x67, 0x69,
	0x66, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x67, 0x69, 0x66, 0x74, 0x5f,
	0x73, 0x65, 0x74, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x52, 0x04, 0x67, 0x69, 0x66, 0x74, 0x12, 0x26,
	0x0a, 0x0e, 0x61, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x53, 0x65, 0x6e, 0x64, 0x4e, 0x75, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x61, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x53,
	0x65, 0x6e, 0x64, 0x4e, 0x75, 0x6d, 0x22, 0xfb, 0x04, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x62, 0x69,
	0x6e, 0x65, 0x42, 0x69, 0x6c, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x4d, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x4d, 0x73,
	0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x4e,
	0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f,
	0x6f, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x75, 0x67, 0x63, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x75, 0x67, 0x63,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x65, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x65, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x73, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x07, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x08,
	0x6d, 0x61, 0x70, 0x42, 0x6f, 0x6e, 0x75, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e,
	0x65, 0x42, 0x69, 0x6c, 0x6c, 0x2e, 0x4d, 0x61, 0x70, 0x42, 0x6f, 0x6e, 0x75, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x61, 0x70, 0x42, 0x6f, 0x6e, 0x75, 0x73, 0x12, 0x44, 0x0a,
	0x11, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x69, 0x66, 0x74, 0x5f,
	0x73, 0x65, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x11, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x46, 0x75, 0x6c, 0x6c, 0x53, 0x65, 0x74, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x62, 0x46, 0x75, 0x6c, 0x6c, 0x53, 0x65, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x70, 0x61, 0x79, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x61, 0x70, 0x42, 0x6f,
	0x6e, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x2a, 0x7b, 0x0a, 0x16, 0x47, 0x69, 0x66, 0x74, 0x53, 0x65, 0x74, 0x43,
	0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20,
	0x0a, 0x1c, 0x47, 0x69, 0x66, 0x74, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x10, 0x00,
	0x12, 0x20, 0x0a, 0x1c, 0x47, 0x69, 0x66, 0x74, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x62, 0x69,
	0x6e, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x47, 0x69, 0x66, 0x74, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x62, 0x69, 0x6e, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x65, 0x62, 0x10,
	0x02, 0x2a, 0x82, 0x01, 0x0a, 0x16, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x50,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x1c,
	0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x10, 0x00, 0x12, 0x24,
	0x0a, 0x20, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x50, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68,
	0x74, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x69,
	0x64, 0x64, 0x65, 0x6e, 0x10, 0x02, 0x42, 0x47, 0x5a, 0x45, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x67, 0x69, 0x66, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_gift_set_common_common_proto_rawDescOnce sync.Once
	file_pb_gift_set_common_common_proto_rawDescData = file_pb_gift_set_common_common_proto_rawDesc
)

func file_pb_gift_set_common_common_proto_rawDescGZIP() []byte {
	file_pb_gift_set_common_common_proto_rawDescOnce.Do(func() {
		file_pb_gift_set_common_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_gift_set_common_common_proto_rawDescData)
	})
	return file_pb_gift_set_common_common_proto_rawDescData
}

var file_pb_gift_set_common_common_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pb_gift_set_common_common_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_pb_gift_set_common_common_proto_goTypes = []interface{}{
	(GiftSetCombineLocation)(0),          // 0: gift_set.GiftSetCombineLocation
	(HighlightPrivateStatus)(0),          // 1: gift_set.HighlightPrivateStatus
	(*HighlightRecordItem)(nil),          // 2: gift_set.HighlightRecordItem
	(*ResourceInfo)(nil),                 // 3: gift_set.ResourceInfo
	(*Gift)(nil),                         // 4: gift_set.Gift
	(*GiftSetInfo)(nil),                  // 5: gift_set.GiftSetInfo
	(*CombineInfo)(nil),                  // 6: gift_set.CombineInfo
	(*CombineBill)(nil),                  // 7: gift_set.CombineBill
	(*HighlightRecordItem_UserInfo)(nil), // 8: gift_set.HighlightRecordItem.UserInfo
	nil,                                  // 9: gift_set.GiftSetInfo.MapBonusEntry
	(*CombineInfo_GiftItem)(nil),         // 10: gift_set.CombineInfo.GiftItem
	nil,                                  // 11: gift_set.CombineBill.MapBonusEntry
}
var file_pb_gift_set_common_common_proto_depIdxs = []int32{
	8,  // 0: gift_set.HighlightRecordItem.sender:type_name -> gift_set.HighlightRecordItem.UserInfo
	8,  // 1: gift_set.HighlightRecordItem.receiver:type_name -> gift_set.HighlightRecordItem.UserInfo
	3,  // 2: gift_set.HighlightRecordItem.resourceInfo:type_name -> gift_set.ResourceInfo
	3,  // 3: gift_set.Gift.resourceInfo:type_name -> gift_set.ResourceInfo
	4,  // 4: gift_set.GiftSetInfo.gifts:type_name -> gift_set.Gift
	4,  // 5: gift_set.GiftSetInfo.discountGifts:type_name -> gift_set.Gift
	9,  // 6: gift_set.GiftSetInfo.mapBonus:type_name -> gift_set.GiftSetInfo.MapBonusEntry
	3,  // 7: gift_set.GiftSetInfo.highlightResource:type_name -> gift_set.ResourceInfo
	4,  // 8: gift_set.GiftSetInfo.setGift:type_name -> gift_set.Gift
	4,  // 9: gift_set.GiftSetInfo.discountSetGift:type_name -> gift_set.Gift
	10, // 10: gift_set.CombineInfo.gifts:type_name -> gift_set.CombineInfo.GiftItem
	11, // 11: gift_set.CombineBill.mapBonus:type_name -> gift_set.CombineBill.MapBonusEntry
	3,  // 12: gift_set.CombineBill.highlightResource:type_name -> gift_set.ResourceInfo
	4,  // 13: gift_set.CombineInfo.GiftItem.gift:type_name -> gift_set.Gift
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_pb_gift_set_common_common_proto_init() }
func file_pb_gift_set_common_common_proto_init() {
	if File_pb_gift_set_common_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_gift_set_common_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HighlightRecordItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gift_set_common_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gift_set_common_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gift); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gift_set_common_common_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftSetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gift_set_common_common_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CombineInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gift_set_common_common_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CombineBill); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gift_set_common_common_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HighlightRecordItem_UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gift_set_common_common_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CombineInfo_GiftItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_gift_set_common_common_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_gift_set_common_common_proto_goTypes,
		DependencyIndexes: file_pb_gift_set_common_common_proto_depIdxs,
		EnumInfos:         file_pb_gift_set_common_common_proto_enumTypes,
		MessageInfos:      file_pb_gift_set_common_common_proto_msgTypes,
	}.Build()
	File_pb_gift_set_common_common_proto = out.File
	file_pb_gift_set_common_common_proto_rawDesc = nil
	file_pb_gift_set_common_common_proto_goTypes = nil
	file_pb_gift_set_common_common_proto_depIdxs = nil
}
