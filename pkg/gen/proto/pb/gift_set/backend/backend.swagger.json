{"swagger": "2.0", "info": {"title": "pb/gift_set/backend/backend.proto", "version": "version not set"}, "tags": [{"name": "Backend"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/gift_set.Backend/Combine": {"post": {"summary": "组合", "operationId": "Backend_Combine", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gift_setCombineRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gift_setCombineReq"}}], "tags": ["Backend"]}}, "/gift_set.Backend/GiftSetConfig": {"post": {"summary": "配置", "operationId": "Backend_GiftSetConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gift_setGiftSetConfigRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gift_setGiftSetConfigReq"}}], "tags": ["Backend"]}}, "/gift_set.Backend/UpdateCartoon": {"post": {"summary": "升级彩蛋", "operationId": "Backend_UpdateCartoon", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gift_setUpdateCartoonRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gift_setUpdateCartoonReq"}}], "tags": ["Backend"]}}}, "definitions": {"gift_setCombineReq": {"type": "object", "properties": {"giftId": {"type": "string", "format": "int64", "title": "礼物ID"}, "num": {"type": "integer", "format": "int32", "title": "礼物数量"}, "sendMs": {"type": "string", "format": "int64", "title": "送礼时间"}, "consumeId": {"type": "string", "title": "订单ID"}, "recvUid": {"type": "string", "title": "收礼人"}, "sendUid": {"type": "string", "title": "送礼人"}, "roomId": {"type": "string", "title": "房间ID"}, "showId": {"type": "string", "title": "场次ID"}, "ugcId": {"type": "string", "title": "作品ID"}, "payScene": {"type": "integer", "format": "int64", "title": "支付场景"}, "setId": {"type": "integer", "format": "int32", "description": "套系ID", "title": "全套送额外字段"}, "bFullset": {"type": "boolean", "title": "是否全套送"}, "sign": {"type": "string", "title": "「giftId,num,sendMs,consumeId,recvUid,sendUid,salt」 -> md5"}, "location": {"type": "integer", "format": "int64", "title": "送礼位置 参考GiftSetCombineLocation"}}}, "gift_setCombineRsp": {"type": "object", "properties": {"res": {"type": "integer", "format": "int32"}}}, "gift_setGift": {"type": "object", "properties": {"giftId": {"type": "string", "format": "int64", "title": "礼物ID"}, "giftLogo": {"type": "string", "title": "礼物logo"}, "giftName": {"type": "string", "title": "礼物名称"}, "price": {"type": "integer", "format": "int32", "title": "价格"}, "resourceInfo": {"$ref": "#/definitions/gift_setResourceInfo", "title": "资源信息"}}, "title": "礼物信息"}, "gift_setGiftSetConfigReq": {"type": "object", "properties": {"setId": {"type": "integer", "format": "int32"}, "giftId": {"type": "string", "format": "int64"}, "uid": {"type": "string"}}}, "gift_setGiftSetConfigRsp": {"type": "object", "properties": {"info": {"$ref": "#/definitions/gift_setGiftSetInfo", "title": "套系礼物信息"}}}, "gift_setGiftSetInfo": {"type": "object", "properties": {"setId": {"type": "integer", "format": "int32"}, "gifts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gift_setGift"}, "title": "礼物信息"}, "discountGifts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gift_setGift"}, "title": "折扣礼物信息"}, "discountBeginTs": {"type": "integer", "format": "int32", "title": "折扣开始时间"}, "discountEndTs": {"type": "integer", "format": "int32", "title": "折扣结束时间"}, "strSetName": {"type": "string", "title": "套系名称"}, "strSetLogo": {"type": "string", "title": "套系logo"}, "price": {"type": "integer", "format": "int32", "title": "套系总价格"}, "mapBonus": {"type": "object", "additionalProperties": {"type": "string"}, "title": "bonus"}, "highlightResource": {"$ref": "#/definitions/gift_setResourceInfo", "description": "高光资源信息", "title": "*************** kg ******************"}, "lantern": {"type": "array", "items": {"type": "string"}, "title": "跑马灯"}, "setGift": {"$ref": "#/definitions/gift_setGift", "title": "套系礼物信息"}, "discountSetGift": {"$ref": "#/definitions/gift_setGift", "title": "折扣套系礼物信息"}, "jumpUrl": {"type": "string", "title": "跳转链接"}}, "title": "套系信息"}, "gift_setResourceInfo": {"type": "object", "properties": {"resourceId": {"type": "integer", "format": "int32", "title": "资源ID"}, "resourceUrl": {"type": "string", "title": "资源Url"}, "resourceMd5": {"type": "string", "title": "资源Md5"}}}, "gift_setUpdateCartoonReq": {"type": "object", "properties": {"uid": {"type": "string"}, "setId": {"type": "integer", "format": "int32"}, "stage": {"type": "integer", "format": "int32"}, "resourceInfo": {"$ref": "#/definitions/gift_setResourceInfo", "title": "动画"}}}, "gift_setUpdateCartoonRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}