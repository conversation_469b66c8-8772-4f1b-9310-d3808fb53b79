{"swagger": "2.0", "info": {"title": "pb/gift_set/combine/combine.proto", "version": "version not set"}, "tags": [{"name": "Combine"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/gift_set.Combine/JoinCombine": {"post": {"summary": "加入组合", "operationId": "Co<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gift_setJoinCombineRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gift_setJoinCombineReq"}}], "tags": ["Combine"]}}, "/gift_set.Combine/QueryCombine": {"post": {"summary": "查询有效组合信息", "operationId": "Combine_QueryCombine", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gift_setQueryCombineRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gift_setQueryCombineReq"}}], "tags": ["Combine"]}}}, "definitions": {"CombineInfoGiftItem": {"type": "object", "properties": {"gift": {"$ref": "#/definitions/gift_setGift", "title": "套系礼物信息"}, "alreadySendNum": {"type": "integer", "format": "int32", "title": "已经送出的数量"}}}, "gift_setCombineInfo": {"type": "object", "properties": {"gifts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/CombineInfoGiftItem"}, "title": "套系礼物信息"}, "remainValidTs": {"type": "integer", "format": "int32", "title": "剩余有效时间"}}, "title": "组合信息"}, "gift_setGift": {"type": "object", "properties": {"giftId": {"type": "string", "format": "int64", "title": "礼物ID"}, "giftLogo": {"type": "string", "title": "礼物logo"}, "giftName": {"type": "string", "title": "礼物名称"}, "price": {"type": "integer", "format": "int32", "title": "价格"}, "resourceInfo": {"$ref": "#/definitions/gift_setResourceInfo", "title": "资源信息"}}, "title": "礼物信息"}, "gift_setJoinCombineReq": {"type": "object", "properties": {"giftId": {"type": "string", "format": "int64", "title": "礼物ID"}, "num": {"type": "integer", "format": "int32", "title": "礼物数量"}, "sendMs": {"type": "string", "format": "int64", "title": "送礼时间"}, "consumeId": {"type": "string", "title": "订单ID"}, "recvUid": {"type": "string", "title": "收礼人"}, "sendUid": {"type": "string", "title": "送礼人"}, "roomId": {"type": "string", "title": "房间ID"}, "showId": {"type": "string", "title": "场次ID"}, "ugcId": {"type": "string", "title": "作品ID"}, "payScene": {"type": "integer", "format": "int64", "title": "支付场景"}, "setId": {"type": "integer", "format": "int32", "description": "套系ID", "title": "全套送额外字段"}, "bFullset": {"type": "boolean", "title": "是否全套送"}, "sign": {"type": "string", "title": "「giftId,num,sendMs,consumeId,recvUid,sendUid,salt」 -> md5"}}}, "gift_setJoinCombineRsp": {"type": "object", "properties": {"res": {"type": "integer", "format": "int32"}}}, "gift_setQueryCombineReq": {"type": "object", "properties": {"setId": {"type": "integer", "format": "int32", "title": "套系ID"}, "sendUid": {"type": "string", "title": "赠送人"}}}, "gift_setQueryCombineRsp": {"type": "object", "properties": {"conbineInfo": {"$ref": "#/definitions/gift_setCombineInfo", "title": "匹配信息"}}}, "gift_setResourceInfo": {"type": "object", "properties": {"resourceId": {"type": "integer", "format": "int32", "title": "资源ID"}, "resourceUrl": {"type": "string", "title": "资源Url"}, "resourceMd5": {"type": "string", "title": "资源Md5"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}