{"swagger": "2.0", "info": {"title": "pb/gift_set/api/api.proto", "version": "version not set"}, "tags": [{"name": "Api"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/gift_set.Api/MainPage": {"post": {"summary": "游戏首页", "operationId": "Api_MainPage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gift_setMainPageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gift_setMainPageReq"}}], "tags": ["Api"]}}, "/gift_set.Api/Pay": {"post": {"summary": "送礼", "operationId": "Api_Pay", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gift_setPayRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gift_setPayReq"}}], "tags": ["Api"]}}, "/gift_set.Api/QueryListApi": {"post": {"summary": "查询高光", "operationId": "Api_QueryListApi", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gift_setQueryListApiRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gift_setQueryListApiReq"}}], "tags": ["Api"]}}, "/gift_set.Api/UpdatePrivateApi": {"post": {"summary": "公开/私密 私密则仅自己可见，公开则所有用户可查看", "operationId": "Api_UpdatePrivateApi", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gift_setUpdatePrivateApiRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gift_setUpdatePrivateApiReq"}}], "tags": ["Api"]}}}, "definitions": {"CombineInfoGiftItem": {"type": "object", "properties": {"gift": {"$ref": "#/definitions/gift_setGift", "title": "套系礼物信息"}, "alreadySendNum": {"type": "integer", "format": "int32", "title": "已经送出的数量"}}}, "HighlightRecordItemUserInfo": {"type": "object", "properties": {"nickname": {"type": "string", "title": "姓名"}, "avatar": {"type": "string", "title": "头像"}, "uid": {"type": "string", "title": "uid"}}}, "gift_setCombineInfo": {"type": "object", "properties": {"gifts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/CombineInfoGiftItem"}, "title": "套系礼物信息"}, "remainValidTs": {"type": "integer", "format": "int32", "title": "剩余有效时间"}}, "title": "组合信息"}, "gift_setGift": {"type": "object", "properties": {"giftId": {"type": "string", "format": "int64", "title": "礼物ID"}, "giftLogo": {"type": "string", "title": "礼物logo"}, "giftName": {"type": "string", "title": "礼物名称"}, "price": {"type": "integer", "format": "int32", "title": "价格"}, "resourceInfo": {"$ref": "#/definitions/gift_setResourceInfo", "title": "资源信息"}}, "title": "礼物信息"}, "gift_setGiftSetInfo": {"type": "object", "properties": {"setId": {"type": "integer", "format": "int32"}, "gifts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gift_setGift"}, "title": "礼物信息"}, "discountGifts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gift_setGift"}, "title": "折扣礼物信息"}, "discountBeginTs": {"type": "integer", "format": "int32", "title": "折扣开始时间"}, "discountEndTs": {"type": "integer", "format": "int32", "title": "折扣结束时间"}, "strSetName": {"type": "string", "title": "套系名称"}, "strSetLogo": {"type": "string", "title": "套系logo"}, "price": {"type": "integer", "format": "int32", "title": "套系总价格"}, "mapBonus": {"type": "object", "additionalProperties": {"type": "string"}, "title": "bonus"}, "highlightResource": {"$ref": "#/definitions/gift_setResourceInfo", "description": "高光资源信息", "title": "*************** kg ******************"}, "lantern": {"type": "array", "items": {"type": "string"}, "title": "跑马灯"}, "setGift": {"$ref": "#/definitions/gift_setGift", "title": "套系礼物信息"}, "discountSetGift": {"$ref": "#/definitions/gift_setGift", "title": "折扣套系礼物信息"}, "jumpUrl": {"type": "string", "title": "跳转链接"}}, "title": "套系信息"}, "gift_setGoodsItem": {"type": "object", "properties": {"giftId": {"type": "string", "format": "int64", "title": "礼物ID"}, "num": {"type": "string", "format": "int64", "title": "数量"}}, "title": "商品信息"}, "gift_setHighlightPrivateStatus": {"type": "string", "enum": ["HighlightPrivateStatusUnknow", "HighlightPrivateStatusDisclosure", "HighlightPrivateStatusHidden"], "default": "HighlightPrivateStatusUnknow", "title": "- HighlightPrivateStatusDisclosure: 公开\n - HighlightPrivateStatusHidden: 隐藏"}, "gift_setHighlightRecordItem": {"type": "object", "properties": {"sender": {"$ref": "#/definitions/HighlightRecordItemUserInfo", "title": "送礼人"}, "receiver": {"$ref": "#/definitions/HighlightRecordItemUserInfo", "title": "收礼人"}, "resourceInfo": {"$ref": "#/definitions/gift_setResourceInfo", "title": "动画"}, "setId": {"type": "integer", "format": "int64", "title": "套系id"}, "num": {"type": "integer", "format": "int64", "title": "数量"}, "uTs": {"type": "integer", "format": "int64", "title": "送礼时间"}}}, "gift_setMainPageReq": {"type": "object", "properties": {"setId": {"type": "integer", "format": "int32"}, "hostUid": {"type": "string", "title": "送礼人uid"}, "recvUid": {"type": "string", "title": "收礼人uid"}}}, "gift_setMainPageRsp": {"type": "object", "properties": {"gift": {"$ref": "#/definitions/gift_setGiftSetInfo", "title": "套系礼物信息"}, "combine": {"$ref": "#/definitions/gift_setCombineInfo", "title": "组合信息 --废弃"}, "highlightItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gift_setHighlightRecordItem"}, "title": "高光信息"}}}, "gift_setPayInfo": {"type": "object", "properties": {"goodsItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gift_setGoodsItem"}, "title": "商品列表"}, "amount": {"type": "string", "format": "int64", "title": "总价"}}, "title": "支付信息"}, "gift_setPayReq": {"type": "object", "properties": {"payUserId": {"type": "string", "title": "付费用户Uid"}, "recvUserId": {"type": "string", "title": "收礼用户Uid"}, "payInfo": {"$ref": "#/definitions/gift_setPayInfo", "title": "付费信息"}, "orderId": {"type": "string", "title": "订单id，幂等使用"}, "paySceneInfo": {"$ref": "#/definitions/gift_setPaySceneInfo"}, "setId": {"type": "integer", "format": "int32", "title": "套系ID"}}}, "gift_setPayResult": {"type": "object", "properties": {"orderId": {"type": "string"}, "balance": {"type": "string", "format": "int64"}, "errMsg": {"type": "string"}}, "title": "支付结果"}, "gift_setPayRsp": {"type": "object", "properties": {"payResult": {"$ref": "#/definitions/gift_setPayResult"}}}, "gift_setPaySceneInfo": {"type": "object", "properties": {"paySceneType": {"$ref": "#/definitions/gift_setPaySceneType"}, "anchorId": {"type": "string"}, "roomId": {"type": "string"}, "showId": {"type": "string"}, "ugcId": {"type": "string"}}, "title": "支付场景信息"}, "gift_setPaySceneType": {"type": "string", "enum": ["PAY_SCENE_TYPE_UNKNOWN", "PAY_SCENE_TYPE_LIVE", "PAY_SCENE_TYPE_KTV", "PAY_SCENE_TYPE_ASYNC_UGC", "Pay_SCENE_TYPE_ASYNC_MAIL", "PAY_SCENE_TYPE_ASYNC_HOMEPAGE"], "default": "PAY_SCENE_TYPE_UNKNOWN", "description": "- PAY_SCENE_TYPE_LIVE: 直播\n - PAY_SCENE_TYPE_KTV: 歌房\n - PAY_SCENE_TYPE_ASYNC_UGC: 异步作品\n - Pay_SCENE_TYPE_ASYNC_MAIL: 异步私信 必填参数:无\n - PAY_SCENE_TYPE_ASYNC_HOMEPAGE: 异步个人 必填参数:无", "title": "支付场景"}, "gift_setQueryListApiReq": {"type": "object", "properties": {"uid": {"type": "string", "title": "用户id"}, "receiverUid": {"type": "string", "title": "收礼人uid"}, "passback": {"type": "string", "title": "回传参数"}}}, "gift_setQueryListApiRsp": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gift_setHighlightRecordItem"}}, "passback": {"type": "string"}, "hasMore": {"type": "boolean"}, "receiverStatus": {"type": "integer", "format": "int64", "title": "收礼人隐私状态"}}}, "gift_setResourceInfo": {"type": "object", "properties": {"resourceId": {"type": "integer", "format": "int32", "title": "资源ID"}, "resourceUrl": {"type": "string", "title": "资源Url"}, "resourceMd5": {"type": "string", "title": "资源Md5"}}}, "gift_setUpdatePrivateApiReq": {"type": "object", "properties": {"uid": {"type": "string", "title": "uid"}, "status": {"$ref": "#/definitions/gift_setHighlightPrivateStatus", "title": "隐私状态"}}}, "gift_setUpdatePrivateApiRsp": {"type": "object", "properties": {"status": {"$ref": "#/definitions/gift_setHighlightPrivateStatus", "title": "隐私状态"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}