// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/gift_set/highlight/highlight.proto

package gift_set

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	HighlightSvr_UpdatePrivate_FullMethodName = "/gift_set.HighlightSvr/UpdatePrivate"
	HighlightSvr_QueryList_FullMethodName     = "/gift_set.HighlightSvr/QueryList"
	HighlightSvr_CombineBill_FullMethodName   = "/gift_set.HighlightSvr/CombineBill"
)

// HighlightSvrClient is the client API for HighlightSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HighlightSvrClient interface {
	// 公开/私密 私密则仅自己可见，公开则所有用户可查看
	UpdatePrivate(ctx context.Context, in *UpdatePrivateReq, opts ...grpc.CallOption) (*UpdatePrivateRsp, error)
	// 查询高光
	QueryList(ctx context.Context, in *QueryListReq, opts ...grpc.CallOption) (*QueryListRsp, error)
	// 匹配成功事件
	CombineBill(ctx context.Context, in *CombineBillReq, opts ...grpc.CallOption) (*CombineBillRsp, error)
}

type highlightSvrClient struct {
	cc grpc.ClientConnInterface
}

func NewHighlightSvrClient(cc grpc.ClientConnInterface) HighlightSvrClient {
	return &highlightSvrClient{cc}
}

func (c *highlightSvrClient) UpdatePrivate(ctx context.Context, in *UpdatePrivateReq, opts ...grpc.CallOption) (*UpdatePrivateRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdatePrivateRsp)
	err := c.cc.Invoke(ctx, HighlightSvr_UpdatePrivate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highlightSvrClient) QueryList(ctx context.Context, in *QueryListReq, opts ...grpc.CallOption) (*QueryListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryListRsp)
	err := c.cc.Invoke(ctx, HighlightSvr_QueryList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highlightSvrClient) CombineBill(ctx context.Context, in *CombineBillReq, opts ...grpc.CallOption) (*CombineBillRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CombineBillRsp)
	err := c.cc.Invoke(ctx, HighlightSvr_CombineBill_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HighlightSvrServer is the server API for HighlightSvr service.
// All implementations should embed UnimplementedHighlightSvrServer
// for forward compatibility
type HighlightSvrServer interface {
	// 公开/私密 私密则仅自己可见，公开则所有用户可查看
	UpdatePrivate(context.Context, *UpdatePrivateReq) (*UpdatePrivateRsp, error)
	// 查询高光
	QueryList(context.Context, *QueryListReq) (*QueryListRsp, error)
	// 匹配成功事件
	CombineBill(context.Context, *CombineBillReq) (*CombineBillRsp, error)
}

// UnimplementedHighlightSvrServer should be embedded to have forward compatible implementations.
type UnimplementedHighlightSvrServer struct {
}

func (UnimplementedHighlightSvrServer) UpdatePrivate(context.Context, *UpdatePrivateReq) (*UpdatePrivateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePrivate not implemented")
}
func (UnimplementedHighlightSvrServer) QueryList(context.Context, *QueryListReq) (*QueryListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryList not implemented")
}
func (UnimplementedHighlightSvrServer) CombineBill(context.Context, *CombineBillReq) (*CombineBillRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CombineBill not implemented")
}

// UnsafeHighlightSvrServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HighlightSvrServer will
// result in compilation errors.
type UnsafeHighlightSvrServer interface {
	mustEmbedUnimplementedHighlightSvrServer()
}

func RegisterHighlightSvrServer(s grpc.ServiceRegistrar, srv HighlightSvrServer) {
	s.RegisterService(&HighlightSvr_ServiceDesc, srv)
}

func _HighlightSvr_UpdatePrivate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePrivateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighlightSvrServer).UpdatePrivate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HighlightSvr_UpdatePrivate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighlightSvrServer).UpdatePrivate(ctx, req.(*UpdatePrivateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighlightSvr_QueryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighlightSvrServer).QueryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HighlightSvr_QueryList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighlightSvrServer).QueryList(ctx, req.(*QueryListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighlightSvr_CombineBill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CombineBillReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighlightSvrServer).CombineBill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HighlightSvr_CombineBill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighlightSvrServer).CombineBill(ctx, req.(*CombineBillReq))
	}
	return interceptor(ctx, in, info, handler)
}

// HighlightSvr_ServiceDesc is the grpc.ServiceDesc for HighlightSvr service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HighlightSvr_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "gift_set.HighlightSvr",
	HandlerType: (*HighlightSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdatePrivate",
			Handler:    _HighlightSvr_UpdatePrivate_Handler,
		},
		{
			MethodName: "QueryList",
			Handler:    _HighlightSvr_QueryList_Handler,
		},
		{
			MethodName: "CombineBill",
			Handler:    _HighlightSvr_CombineBill_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/gift_set/highlight/highlight.proto",
}
