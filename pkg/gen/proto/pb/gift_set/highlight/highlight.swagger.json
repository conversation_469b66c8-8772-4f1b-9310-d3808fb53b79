{"swagger": "2.0", "info": {"title": "pb/gift_set/highlight/highlight.proto", "version": "version not set"}, "tags": [{"name": "HighlightSvr"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/gift_set.HighlightSvr/CombineBill": {"post": {"summary": "匹配成功事件", "operationId": "HighlightSvr_CombineBill", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gift_setCombineBillRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gift_setCombineBillReq"}}], "tags": ["HighlightSvr"]}}, "/gift_set.HighlightSvr/QueryList": {"post": {"summary": "查询高光", "operationId": "HighlightSvr_QueryList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gift_setQueryListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gift_setQueryListReq"}}], "tags": ["HighlightSvr"]}}, "/gift_set.HighlightSvr/UpdatePrivate": {"post": {"summary": "公开/私密 私密则仅自己可见，公开则所有用户可查看", "operationId": "HighlightSvr_UpdatePrivate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gift_setUpdatePrivateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gift_setUpdatePrivateReq"}}], "tags": ["HighlightSvr"]}}}, "definitions": {"HighlightRecordItemUserInfo": {"type": "object", "properties": {"nickname": {"type": "string", "title": "姓名"}, "avatar": {"type": "string", "title": "头像"}, "uid": {"type": "string", "title": "uid"}}}, "gift_setCombineBill": {"type": "object", "properties": {"setId": {"type": "integer", "format": "int32"}, "sender": {"type": "string"}, "receiver": {"type": "string"}, "combinedMs": {"type": "string", "format": "int64", "title": "成套时间"}, "combinedNum": {"type": "integer", "format": "int32", "title": "成套数量"}, "orderId": {"type": "string", "title": "订单ID"}, "roomId": {"type": "string", "title": "房间ID"}, "showId": {"type": "string", "title": "场次ID"}, "ugcId": {"type": "string", "title": "作品ID"}, "setName": {"type": "string", "title": "套系名称"}, "setLogo": {"type": "string", "title": "套系logo"}, "setPrice": {"type": "integer", "format": "int32", "title": "套系价格，套系本身的价值"}, "giftIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "成套礼物ID"}, "mapBonus": {"type": "object", "additionalProperties": {"type": "string"}}, "highlightResource": {"$ref": "#/definitions/gift_setResourceInfo", "title": "高光资源信息"}, "bFullSet": {"type": "boolean", "title": "是否全套送"}, "payScene": {"type": "integer", "format": "int64", "title": "送礼场景"}, "location": {"type": "integer", "format": "int64", "title": "送礼位置 参考GiftSetCombineLocation"}}}, "gift_setCombineBillReq": {"type": "object", "properties": {"messageId": {"type": "string"}, "combineBill": {"$ref": "#/definitions/gift_setCombineBill"}}}, "gift_setCombineBillRsp": {"type": "object"}, "gift_setHighlightPrivateStatus": {"type": "string", "enum": ["HighlightPrivateStatusUnknow", "HighlightPrivateStatusDisclosure", "HighlightPrivateStatusHidden"], "default": "HighlightPrivateStatusUnknow", "title": "- HighlightPrivateStatusDisclosure: 公开\n - HighlightPrivateStatusHidden: 隐藏"}, "gift_setHighlightRecordItem": {"type": "object", "properties": {"sender": {"$ref": "#/definitions/HighlightRecordItemUserInfo", "title": "送礼人"}, "receiver": {"$ref": "#/definitions/HighlightRecordItemUserInfo", "title": "收礼人"}, "resourceInfo": {"$ref": "#/definitions/gift_setResourceInfo", "title": "动画"}, "setId": {"type": "integer", "format": "int64", "title": "套系id"}, "num": {"type": "integer", "format": "int64", "title": "数量"}, "uTs": {"type": "integer", "format": "int64", "title": "送礼时间"}}}, "gift_setQueryListReq": {"type": "object", "properties": {"uid": {"type": "string", "title": "用户id"}, "receiverUid": {"type": "string", "title": "收礼人uid"}, "passback": {"type": "string", "title": "回传参数"}}}, "gift_setQueryListRsp": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gift_setHighlightRecordItem"}, "title": "列表"}, "passback": {"type": "string", "title": "回传参数"}, "hasMore": {"type": "boolean", "title": "是否有更多"}, "receiverStatus": {"$ref": "#/definitions/gift_setHighlightPrivateStatus", "title": "收礼人隐私状态"}}}, "gift_setResourceInfo": {"type": "object", "properties": {"resourceId": {"type": "integer", "format": "int32", "title": "资源ID"}, "resourceUrl": {"type": "string", "title": "资源Url"}, "resourceMd5": {"type": "string", "title": "资源Md5"}}}, "gift_setUpdatePrivateReq": {"type": "object", "properties": {"uid": {"type": "string", "title": "uid"}, "status": {"$ref": "#/definitions/gift_setHighlightPrivateStatus", "title": "隐私状态"}}}, "gift_setUpdatePrivateRsp": {"type": "object", "properties": {"status": {"$ref": "#/definitions/gift_setHighlightPrivateStatus", "title": "隐私状态"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}