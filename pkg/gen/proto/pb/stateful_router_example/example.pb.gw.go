// Code generated by protoc-gen-grpc-gateway. DO NOT EDIT.
// source: pb/stateful_router_example/example.proto

/*
Package stateful_router_example is a reverse proxy.

It translates gRPC into RESTful JSON APIs.
*/
package stateful_router_example

import (
	"context"
	"io"
	"kugou_adapter_service/pkg/gen/proto/pb/stateful_router"
	"net/http"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/grpc-ecosystem/grpc-gateway/v2/utilities"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

// Suppress "imported and not used" errors
var _ codes.Code
var _ io.Reader
var _ status.Status
var _ = runtime.String
var _ = utilities.NewDoubleArray
var _ = metadata.Join

func request_StatefulRouterExample_PullLoad_0(ctx context.Context, marshaler runtime.Marshaler, client StatefulRouterExampleClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq stateful_router.LoadPullReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.PullLoad(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_StatefulRouterExample_PullLoad_0(ctx context.Context, marshaler runtime.Marshaler, server StatefulRouterExampleServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq stateful_router.LoadPullReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.PullLoad(ctx, &protoReq)
	return msg, metadata, err

}

func request_StatefulRouterExample_Example_0(ctx context.Context, marshaler runtime.Marshaler, client StatefulRouterExampleClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq ExampleReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.Example(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_StatefulRouterExample_Example_0(ctx context.Context, marshaler runtime.Marshaler, server StatefulRouterExampleServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq ExampleReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.Example(ctx, &protoReq)
	return msg, metadata, err

}

// RegisterStatefulRouterExampleHandlerServer registers the http handlers for service StatefulRouterExample to "mux".
// UnaryRPC     :call StatefulRouterExampleServer directly.
// StreamingRPC :currently unsupported pending https://github.com/grpc/grpc-go/issues/906.
// Note that using this registration option will cause many gRPC library features to stop working. Consider using RegisterStatefulRouterExampleHandlerFromEndpoint instead.
func RegisterStatefulRouterExampleHandlerServer(ctx context.Context, mux *runtime.ServeMux, server StatefulRouterExampleServer) error {

	mux.Handle("POST", pattern_StatefulRouterExample_PullLoad_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/stateful_router.StatefulRouterExample/PullLoad", runtime.WithHTTPPathPattern("/stateful_router.StatefulRouterExample/PullLoad"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_StatefulRouterExample_PullLoad_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_StatefulRouterExample_PullLoad_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_StatefulRouterExample_Example_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/stateful_router.StatefulRouterExample/Example", runtime.WithHTTPPathPattern("/stateful_router.StatefulRouterExample/Example"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_StatefulRouterExample_Example_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_StatefulRouterExample_Example_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

// RegisterStatefulRouterExampleHandlerFromEndpoint is same as RegisterStatefulRouterExampleHandler but
// automatically dials to "endpoint" and closes the connection when "ctx" gets done.
func RegisterStatefulRouterExampleHandlerFromEndpoint(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) (err error) {
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
			return
		}
		go func() {
			<-ctx.Done()
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
		}()
	}()

	return RegisterStatefulRouterExampleHandler(ctx, mux, conn)
}

// RegisterStatefulRouterExampleHandler registers the http handlers for service StatefulRouterExample to "mux".
// The handlers forward requests to the grpc endpoint over "conn".
func RegisterStatefulRouterExampleHandler(ctx context.Context, mux *runtime.ServeMux, conn *grpc.ClientConn) error {
	return RegisterStatefulRouterExampleHandlerClient(ctx, mux, NewStatefulRouterExampleClient(conn))
}

// RegisterStatefulRouterExampleHandlerClient registers the http handlers for service StatefulRouterExample
// to "mux". The handlers forward requests to the grpc endpoint over the given implementation of "StatefulRouterExampleClient".
// Note: the gRPC framework executes interceptors within the gRPC handler. If the passed in "StatefulRouterExampleClient"
// doesn't go through the normal gRPC flow (creating a gRPC client etc.) then it will be up to the passed in
// "StatefulRouterExampleClient" to call the correct interceptors.
func RegisterStatefulRouterExampleHandlerClient(ctx context.Context, mux *runtime.ServeMux, client StatefulRouterExampleClient) error {

	mux.Handle("POST", pattern_StatefulRouterExample_PullLoad_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/stateful_router.StatefulRouterExample/PullLoad", runtime.WithHTTPPathPattern("/stateful_router.StatefulRouterExample/PullLoad"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_StatefulRouterExample_PullLoad_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_StatefulRouterExample_PullLoad_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_StatefulRouterExample_Example_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/stateful_router.StatefulRouterExample/Example", runtime.WithHTTPPathPattern("/stateful_router.StatefulRouterExample/Example"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_StatefulRouterExample_Example_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_StatefulRouterExample_Example_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

var (
	pattern_StatefulRouterExample_PullLoad_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"stateful_router.StatefulRouterExample", "PullLoad"}, ""))

	pattern_StatefulRouterExample_Example_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"stateful_router.StatefulRouterExample", "Example"}, ""))
)

var (
	forward_StatefulRouterExample_PullLoad_0 = runtime.ForwardResponseMessage

	forward_StatefulRouterExample_Example_0 = runtime.ForwardResponseMessage
)
