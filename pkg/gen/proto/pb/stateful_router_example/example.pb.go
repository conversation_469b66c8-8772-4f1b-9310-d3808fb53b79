// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/stateful_router_example/example.proto

package stateful_router_example

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	stateful_router "kugou_adapter_service/pkg/gen/proto/pb/stateful_router"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ExampleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source string `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"` // appid
}

func (x *ExampleReq) Reset() {
	*x = ExampleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_stateful_router_example_example_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExampleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExampleReq) ProtoMessage() {}

func (x *ExampleReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_stateful_router_example_example_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExampleReq.ProtoReflect.Descriptor instead.
func (*ExampleReq) Descriptor() ([]byte, []int) {
	return file_pb_stateful_router_example_example_proto_rawDescGZIP(), []int{0}
}

func (x *ExampleReq) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

type ExampleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *ExampleRsp) Reset() {
	*x = ExampleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_stateful_router_example_example_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExampleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExampleRsp) ProtoMessage() {}

func (x *ExampleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_stateful_router_example_example_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExampleRsp.ProtoReflect.Descriptor instead.
func (*ExampleRsp) Descriptor() ([]byte, []int) {
	return file_pb_stateful_router_example_example_proto_rawDescGZIP(), []int{1}
}

func (x *ExampleRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

var File_pb_stateful_router_example_example_proto protoreflect.FileDescriptor

var file_pb_stateful_router_example_example_proto_rawDesc = []byte{
	0x0a, 0x28, 0x70, 0x62, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x72, 0x5f, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x2f, 0x65, 0x78, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x1a, 0x1d, 0x70, 0x62, 0x2f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x24, 0x0a, 0x0a, 0x45, 0x78,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x22, 0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x32, 0xa4, 0x01, 0x0a, 0x15, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x52, 0x6f, 0x75,
	0x74, 0x65, 0x72, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x46, 0x0a, 0x08, 0x50, 0x75,
	0x6c, 0x6c, 0x4c, 0x6f, 0x61, 0x64, 0x12, 0x1c, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75,
	0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x50, 0x75, 0x6c,
	0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f,
	0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x50, 0x75, 0x6c, 0x6c, 0x52,
	0x73, 0x70, 0x12, 0x43, 0x0a, 0x07, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x1b, 0x2e,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x2e,
	0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x42, 0x4f, 0x5a, 0x4d, 0x74, 0x63, 0x6f, 0x64, 0x65,
	0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72,
	0x5f, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_stateful_router_example_example_proto_rawDescOnce sync.Once
	file_pb_stateful_router_example_example_proto_rawDescData = file_pb_stateful_router_example_example_proto_rawDesc
)

func file_pb_stateful_router_example_example_proto_rawDescGZIP() []byte {
	file_pb_stateful_router_example_example_proto_rawDescOnce.Do(func() {
		file_pb_stateful_router_example_example_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_stateful_router_example_example_proto_rawDescData)
	})
	return file_pb_stateful_router_example_example_proto_rawDescData
}

var file_pb_stateful_router_example_example_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_stateful_router_example_example_proto_goTypes = []interface{}{
	(*ExampleReq)(nil),                  // 0: stateful_router.ExampleReq
	(*ExampleRsp)(nil),                  // 1: stateful_router.ExampleRsp
	(*stateful_router.LoadPullReq)(nil), // 2: stateful_router.LoadPullReq
	(*stateful_router.LoadPullRsp)(nil), // 3: stateful_router.LoadPullRsp
}
var file_pb_stateful_router_example_example_proto_depIdxs = []int32{
	2, // 0: stateful_router.StatefulRouterExample.PullLoad:input_type -> stateful_router.LoadPullReq
	0, // 1: stateful_router.StatefulRouterExample.Example:input_type -> stateful_router.ExampleReq
	3, // 2: stateful_router.StatefulRouterExample.PullLoad:output_type -> stateful_router.LoadPullRsp
	1, // 3: stateful_router.StatefulRouterExample.Example:output_type -> stateful_router.ExampleRsp
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_stateful_router_example_example_proto_init() }
func file_pb_stateful_router_example_example_proto_init() {
	if File_pb_stateful_router_example_example_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_stateful_router_example_example_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExampleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_stateful_router_example_example_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExampleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_stateful_router_example_example_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_stateful_router_example_example_proto_goTypes,
		DependencyIndexes: file_pb_stateful_router_example_example_proto_depIdxs,
		MessageInfos:      file_pb_stateful_router_example_example_proto_msgTypes,
	}.Build()
	File_pb_stateful_router_example_example_proto = out.File
	file_pb_stateful_router_example_example_proto_rawDesc = nil
	file_pb_stateful_router_example_example_proto_goTypes = nil
	file_pb_stateful_router_example_example_proto_depIdxs = nil
}
