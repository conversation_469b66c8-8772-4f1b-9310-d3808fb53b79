// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/stateful_router_example/example.proto

package stateful_router_example

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	stateful_router "kugou_adapter_service/pkg/gen/proto/pb/stateful_router"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	StatefulRouterExample_PullLoad_FullMethodName = "/stateful_router.StatefulRouterExample/PullLoad"
	StatefulRouterExample_Example_FullMethodName  = "/stateful_router.StatefulRouterExample/Example"
)

// StatefulRouterExampleClient is the client API for StatefulRouterExample service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 选择有状态路由
type StatefulRouterExampleClient interface {
	PullLoad(ctx context.Context, in *stateful_router.LoadPullReq, opts ...grpc.CallOption) (*stateful_router.LoadPullRsp, error)
	Example(ctx context.Context, in *ExampleReq, opts ...grpc.CallOption) (*ExampleRsp, error)
}

type statefulRouterExampleClient struct {
	cc grpc.ClientConnInterface
}

func NewStatefulRouterExampleClient(cc grpc.ClientConnInterface) StatefulRouterExampleClient {
	return &statefulRouterExampleClient{cc}
}

func (c *statefulRouterExampleClient) PullLoad(ctx context.Context, in *stateful_router.LoadPullReq, opts ...grpc.CallOption) (*stateful_router.LoadPullRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(stateful_router.LoadPullRsp)
	err := c.cc.Invoke(ctx, StatefulRouterExample_PullLoad_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statefulRouterExampleClient) Example(ctx context.Context, in *ExampleReq, opts ...grpc.CallOption) (*ExampleRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExampleRsp)
	err := c.cc.Invoke(ctx, StatefulRouterExample_Example_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StatefulRouterExampleServer is the server API for StatefulRouterExample service.
// All implementations should embed UnimplementedStatefulRouterExampleServer
// for forward compatibility
//
// 选择有状态路由
type StatefulRouterExampleServer interface {
	PullLoad(context.Context, *stateful_router.LoadPullReq) (*stateful_router.LoadPullRsp, error)
	Example(context.Context, *ExampleReq) (*ExampleRsp, error)
}

// UnimplementedStatefulRouterExampleServer should be embedded to have forward compatible implementations.
type UnimplementedStatefulRouterExampleServer struct {
}

func (UnimplementedStatefulRouterExampleServer) PullLoad(context.Context, *stateful_router.LoadPullReq) (*stateful_router.LoadPullRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PullLoad not implemented")
}
func (UnimplementedStatefulRouterExampleServer) Example(context.Context, *ExampleReq) (*ExampleRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Example not implemented")
}

// UnsafeStatefulRouterExampleServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StatefulRouterExampleServer will
// result in compilation errors.
type UnsafeStatefulRouterExampleServer interface {
	mustEmbedUnimplementedStatefulRouterExampleServer()
}

func RegisterStatefulRouterExampleServer(s grpc.ServiceRegistrar, srv StatefulRouterExampleServer) {
	s.RegisterService(&StatefulRouterExample_ServiceDesc, srv)
}

func _StatefulRouterExample_PullLoad_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(stateful_router.LoadPullReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatefulRouterExampleServer).PullLoad(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatefulRouterExample_PullLoad_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatefulRouterExampleServer).PullLoad(ctx, req.(*stateful_router.LoadPullReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatefulRouterExample_Example_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExampleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatefulRouterExampleServer).Example(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatefulRouterExample_Example_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatefulRouterExampleServer).Example(ctx, req.(*ExampleReq))
	}
	return interceptor(ctx, in, info, handler)
}

// StatefulRouterExample_ServiceDesc is the grpc.ServiceDesc for StatefulRouterExample service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StatefulRouterExample_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "stateful_router.StatefulRouterExample",
	HandlerType: (*StatefulRouterExampleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PullLoad",
			Handler:    _StatefulRouterExample_PullLoad_Handler,
		},
		{
			MethodName: "Example",
			Handler:    _StatefulRouterExample_Example_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/stateful_router_example/example.proto",
}
