syntax = "proto3";

package rte.interface.open_game_open_api;

option go_package = "git.woa.com/rte/rte-service-go/pkg/gen/proto/rte/interface/open_game_open_api";
option java_package = "com.rte.interface_.open_game_open_api";
option objc_class_prefix = "RTEI_OPEN_GAME_OPEN_API";

import "google/api/annotations.proto";
import "pb/adapter_user/adapter_user.proto";

service OpenGameOpenApi {
  // 获取游戏币余额
  rpc GetBalance(GetBalanceReq) returns (GetBalanceRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/getBalance"
      body: "*"
    };
  }
  // 下单
  rpc PlaceOrder(PlaceOrderReq) returns (PlaceOrderRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/placeOrder"
      body: "*"
    };
  }
  // 扣除游戏币
  rpc Pay(PayReq) returns (PayRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/pay"
      body: "*"
    };
  }
  // 奖励游戏币
  rpc Present(PresentReq) returns (PresentRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/present"
      body: "*"
    };
  }
  // 退款游戏币
  rpc Refund(RefundReq) returns (RefundRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/refund"
      body: "*"
    };
  }
  // 获取用户资料
  rpc GetProfile(GetProfileReq) returns (GetProfileRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/getProfile"
      body: "*"
    };
  }
  // 私信
  rpc SendMail(SendMailReq) returns (SendMailRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/sendMail"
      body: "*"
    };
  }
  // 根据指定金额发奖
  rpc Prize(PrizeReq) returns (PrizeRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/prize"
      body: "*"
    };
  }
  // 数据上报
  rpc TDBankReport(TDBankReportReq) returns (TDBankReportRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/tdbankReport"
      body: "*"
    };
  }
  // 批量获取用户资料
  rpc BatchGetProfile(BatchGetProfileReq) returns (BatchGetProfileRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/batchGetProfile"
      body: "*"
    };
  }
  // 消耗免费礼物
  rpc ConsumeFreeGift(ConsumeFreeGiftReq) returns (ConsumeFreeGiftRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/consumeFreeGift"
      body: "*"
    };
  }
  // 房间消息
  rpc RoomMsg(RoomMsgReq) returns (RoomMsgRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/roomMsg"
      body: "*"
    };
  }
  // 发送push
  rpc SendPush(SendPushReq) returns (SendPushRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/sendPush"
      body: "*"
    };
  }
  // 发送大喇叭
  rpc BigHornMsg(BigHornMsgReq) returns (BigHornMsgRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/bigHornMsg"
      body: "*"
    };
  }
  // 发送平台资产
  rpc SendSingleReward(SendSingleRewardReq) returns (SendSingleRewardRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/sendSingleReward"
      body: "*"
    };
  }
  // 礼物信息
  rpc RewardDetail(RewardDetailReq) returns (RewardDetailRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/rewardDetail"
      body: "*"
    };
  }
  // 礼物信息 批量
  rpc RewardDetailBatch(RewardDetailBatchReq) returns (RewardDetailBatchRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/rewardDetailBatch"
      body: "*"
    };
  }
  // 批量获取道具信息
  rpc BatchPropDetail(BatchPropDetailReq) returns (BatchPropDetailRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/prop/batchDetail"
      body: "*"
    };
  }
  // 批量获取礼物信息
  rpc BatchGiftDetail(BatchGiftDetailReq) returns (BatchGiftDetailRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/gift/batchDetail"
      body: "*"
    };
  }
  // 平台货币余额
  rpc GetPlatBalance(GetPlatBalanceReq) returns (GetPlatBalanceRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/getPlatBalance"
      body: "*"
    };
  }
  // 根据指定金额发奖V2 TODO
  rpc PrizeV2(PrizeV2Req) returns (PrizeV2Rsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/prizeV2"
      body: "*"
    };
  }
  rpc GetSendGiftOrder(GetSendGiftOrderReq) returns (GetSendGiftOrderRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/getSendGiftOrder"
      body: "*"
    };
  }
  rpc SafeCheck(SafeCheckReq) returns (SafeCheckRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/safeCheck"
      body: "*"
    };
  }
  rpc QzaReport(QzaReportReq) returns (QzaReportRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/qzaReport"
      body: "*"
    };
  }
  rpc BatchGetUserFeature(BatchGetUserFeatureReq) returns (BatchGetUserFeatureRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/batchGetUserFeature"
      body: "*"
    };
  }
  rpc BatchGetAnonymousStatus(BatchGetAnonymousStatusReq) returns (BatchGetAnonymousStatusRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/batchGetAnonymousStatus"
      body: "*"
    };
  }
  rpc SendHippyMsg(SendHippyMsgReq) returns (SendHippyMsgRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/hippyMsg"
      body: "*"
    };
  }
  rpc HippyMsgUserSwitchEvent(HippyMsgUserSwitchEventReq) returns (HippyMsgUserSwitchEventRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/hippyMsgUserSwitchEvent"
      body: "*"
    };
  }
  rpc UIABTest(UIABTestReq) returns (UIABTestRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/uiABTest"
      body: "*"
    };
  }
  rpc ABTest(ABTestReq) returns (ABTestRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/abTest"
      body: "*"
    };
  }
  rpc ReportTaskConditionBill(ReportTaskConditionBillReq) returns (ReportTaskConditionBillRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/reportTaskConditionBill"
      body: "*"
    };
  }
  rpc FollowOpt(FollowOptReq) returns (FollowOptRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/followOpt"
      body: "*"
    };
  }
  rpc QueryRelation(QueryRelationReq) returns (QueryRelationRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/queryRelation"
      body: "*"
    };
  }
  rpc QueryCertInfo(QueryCertInfoReq) returns (QueryCertInfoRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/queryCertInfo"
      body: "*"
    };
  }
  rpc TmeEvent(TmeEventReq) returns (TmeEventRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/tmeEvent"
      body: "*"
    };
  }
  rpc SafeCheckPic(SafeCheckPicReq) returns (SafeCheckPicRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/safeCheckPic"
      body: "*"
    };
  }
  rpc QueryAsset(QueryAssetReq) returns (QueryAssetRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/queryAsset"
      body: "*"
    };
  }
  rpc AddAsset(AddAssetReq) returns (AddAssetRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/addAsset"
      body: "*"
    };
  }
  rpc SubAsset(SubAssetReq) returns (SubAssetRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/subAsset"
      body: "*"
    };
  }
  // 上报 IDC DC表; 上报公有云DC不可用该接口
  rpc DCReport(DCReportReq) returns (DCReportRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/dcReport"
      body: "*"
    };
  }
  // [通用消息] 查询开关信息
  rpc GetUserSwitch(GetUserSwitchReq) returns (GetUserSwitchRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/getUserSwitch"
      body: "*"
    };
  }
  // [通用消息] 更改开关信息
  rpc SetUserSwitch(SetUserSwitchReq) returns (SetUserSwitchRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/setUserSwitch"
      body: "*"
    };
  }
  // [通用消息]发送消息
  rpc SendUserSwitchMsg(SendUserSwitchMsgReq) returns (SendUserSwitchMsgRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/sendUserSwitchMsg"
      body: "*"
    };
  }
  // 定向
  rpc BatchUserDirect(BatchUserDirectReq) returns (BatchUserDirectRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/user/batchUserDirect"
      body: "*"
    };
  }
  rpc GetUserIntimacy(GetUserIntimacyReq) returns (GetUserIntimacyRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/user/getIntimacy"
      body: "*"
    };
  }
  rpc JudgeOnline(JudgeOnlineReq) returns (JudgeOnlineRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/user/judgeOnline"
      body: "*"
    };
  }
  rpc NoteRank(NoteRankReq) returns (NoteRankRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/user/noteRank"
      body: "*"
    };
  }
  rpc NoteWeekRank(NoteWeekRankReq) returns (NoteWeekRankRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/user/noteWeekRank"
      body: "*"
    };
  }
  rpc WriteGameFeed(WriteGameFeedReq) returns (WriteGameFeedRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/user/writeGameFeed"
      body: "*"
    };
  }
  // 获取游戏快捷送礼的信息，区分快捷送礼是送礼or回礼
  rpc GetGameQuickGiftConfig(GameQuickGiftConfiglReq) returns (GameQuickGiftConfiglRsp) {
    option (google.api.http) = {
      post: "/api/tme_game_quick_gift/server/game_quick_gift_config"
      body: "*"
    };
  }
  // 查询黑名单
  rpc QueryBlacklist(QueryBlacklistReq) returns (QueryBlacklistRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/user/queryBlacklist"
      body: "*"
    };
  }
  // 查询好友
  rpc QueryFriends(QueryFriendsReq) returns (QueryFriendsRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/user/queryFriend"
      body: "*"
    };
  }
  // 验证黑名单
  rpc VerifyFollow(VerifyFollowReq) returns (VerifyFollowRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/user/verifyFollow"
      body: "*"
    };
  }
  // 获取cw游戏信息
  rpc GetCwGameRankInfo(GetCwGameRankInfoReq) returns (GetCwGameRankInfoRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/user/getCwGameRankInfo"
      body: "*"
    };
  }
  // 查询风险等级
  rpc GetRiskLevel(GetRiskLevelReq) returns (GetRiskLevelRsp) {
    option (google.api.http) = {
      post: "/minigame/openapi/user/getRiskLevel"
      body: "*"
    };
  }
  // 创建房间
  rpc InteractiveGameSwitchRoom(InteractiveGameSwitchRoomReq) returns (InteractiveGameSwitchRoomRsp) {
    option (google.api.http) = {
      post: "/miniprogram/interactiveGame/switchRoom"
      body: "*"
    };
  }
  // 查询待邀请好友
  rpc GetInvitateFriend(GetInvitateFriendReq) returns (GetInvitateFriendRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/user/getInvitateFriend"
      body: "*"
    };
  }
  // 图文同步安全审查
  rpc SyncSafeCheckV2(SafeCheckV2Req) returns (SafeCheckV2Rsp) {
    option (google.api.http) = {
      post: "/miniprogram/safev2/sync_check"
      body: "*"
    };
  }
  // 图文同步安全审查
  rpc ASyncSafeCheckV2Callback(SafeCheckCallbackV2Req) returns (SafeCheckCallbackV2Req) {
    option (google.api.http) = {
      post: "/miniprogram/safev2/async_check_callback"
      body: "*"
    };
  }
  // Geo
  rpc QueryGeo(QueryGeoReq) returns (QueryGeoRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/geo/query"
      body: "*"
    };
  }
  // 三消对账 查询平台订单
  rpc CwsxQueryOrders(CwsxQueryOrdersReq) returns (CwsxQueryOrdersRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/data/CwsxQueryOrders"
      body: "*"
    };
  }
  // 支付中台 下单
  rpc CreateTrade(CreateTradeReq) returns (CreateTradeRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/revenue/createTrade"
      body: "*"
    };
  }
  // 三消动态难度
  rpc CwsxGetSuggestion(CwsxGetSuggestionReq) returns (CwsxGetSuggestionRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/data/CwsxGetSuggestion"
      body: "*"
    };
  }
  // 智研上报
  rpc ZhiYanReport(ZhiYanReportReq) returns (ZhiYanReportRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/data/zhiYanReport"
      body: "*"
    };
  }
  // cwsx广告点位
  rpc CwsxAdSpotTimes(CwsxAdSpotTimesReq) returns (CwsxAdSpotTimesRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/data/cwsxAdSpotTimes"
      body: "*"
    };
  }
  // 广告校验
  rpc AdvertCheck(AdvertCheckReq) returns (AdvertCheckRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/advertCheck"
      body: "*"
    };
  }
  // 回调广告通知领奖结果
  rpc AdvertRewardCallback(AdvertRewardCallbackReq) returns (AdvertRewardCallbackRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/advertRewardCallback"
      body: "*"
    };
  }
  // 广告信息
  rpc AdvertInfo(AdvertInfoReq) returns (AdvertInfoRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/advertInfo"
      body: "*"
    };
  }
  // 领取广告奖励
  rpc AdvertReceiveReward(AdvertReceiveRewardReq) returns (AdvertReceiveRewardRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/advertReceiveReward"
      body: "*"
    };
  }
  // 数据上报
  rpc DataReport(DataReportReq) returns (DataReportRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/dataReport"
      body: "*"
    };
  }
  // 游戏请求
  rpc GamePackapi(GamePackapiReq) returns (GamePackapiRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/gamePackapi"
      body: "*"
    };
  }
  // 告警
  rpc AlarmProxy(AlarmProxyReq) returns (AlarmProxyRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/alarmProxy"
      body: "*"
    };
  }
  // 抽奖
  rpc Lottery(LotteryReq) returns (LotteryRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/lottery"
      body: "*"
    };
  }
  // 赠送抽奖券
  rpc AddTicket(AddTicketReq) returns (AddTicketRsp) {
    option (google.api.http) = {
      post: "/miniprogram/openapi/addTicket"
      body: "*"
    };
  }
}

enum SceneType {
  NONE = 0;
  // 直播
  LIVE = 1;
  // KTV
  KTV = 2;
  // UGC
  UGC = 3;
  // 扑通
  PLOP = 4;
}

// 游戏中台数据
message GameMiddleInfo {
  // uid
  uint64 uid = 1;
  // game app id
  string game_appid = 2;
  // encrypt_uid
  string encrypt_uid = 3;
  // 客户端ipv4
  uint32 ipv4 = 4;
  // 客户端ipv6
  string ipv6 = 5;
  // strUid 支持str类型的平台uid
  string strUid = 6;
  // 设备信息
  string device_info = 7;
}

message GetBalanceReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
}

message BalanceData {
  uint32 balance = 1;
  uint32 free_currency_balance = 2; // 赠币余额，joox有用到
}

message GetBalanceRsp {
  // 余额
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  BalanceData data = 3;
}

message Midas {
  string pf = 1;
  string pfKey = 2;
  string session_id = 3;
  string session_type = 4;
  string pay_token = 5;
}

message Device {
  string qua = 1;
  string device_info = 2;
}

message Scene {
  // 参考 SceneType
  uint32 scene_type = 1;
  string room_id = 2;
  string show_id = 3;
  string ugc_id = 4;
  string tar_uid = 5; // 例如ugc作者，ktv房主，LIVE主播
}

message OrderConf {
  // 收入类型 1/计收入 2/不计收入
  uint32 revenue_type = 1;
  // 购买模式 1/赠送礼物获得 2/直接获得 3/赠送道具获得
  //
  //      例如，类型为1：赠送礼物获得，则需送出pay_gift_num个id为pay_gift_id的礼物，赠送目标为Scene中的场景和tarUid（可以将礼物价值和amount进行校验）
  //           类型为3：赠送道具获得，则需送出pay_prop_num个id为pay_prop_id的道具，赠送目标为Scene中的场景和tarUid
  //           类型为2：不送出道具和礼物，直接扣费
  //
  uint32 pay_type = 2;
  // 平台业务ID
  uint32 plat_business_id = 3;
  // 送出礼物id
  uint32 pay_gift_id = 4;
  // 送出礼物个数（建议这里的礼物id+礼物个数算出来的饭票数，和amount校验下是否相等）
  uint32 pay_gift_num = 5;
}

message UserAssetChange {
  int64 assetId = 1; // 资产 id
  int64 assetNum = 2; // 资产数量
}

message PlaceOrderReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 扣除数量, 必须大于0
  uint32 amount = 2;
  // 增加资产列表, 调用delivery时透传
  repeated UserAssetChange assets = 3;
  // 订单配置，平台业务id，是否算收入等
  OrderConf order_conf = 4;
  // 场景
  Scene scene = 5;
  // 设备相关
  Device device = 6;
  // midas
  Midas midas = 7;
  // mapExt
  map<string, string> mapExt = 8;
  // 时间 用于透传
  uint32 sys_ts = 9;
}

message PlaceOrderRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  string bill_no = 3; // 全局唯一
  string sig = 4; // 订单签名，没有忽略
  // 时间 用于透传
  uint32 sys_ts = 5;
}

message PayReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 订单号, 全局唯一
  string bill_no = 2;
  // 扣除数量, 必须大于0
  uint32 amount = 3;
  // 备注, 会写流水
  string app_remark = 4;
  // 增加资产列表, 调用delivery时透传
  repeated UserAssetChange assets = 5;
  // 订单配置，平台业务id，是否算收入等
  OrderConf order_conf = 6;
  // 场景
  Scene scene = 7;
  // 设备相关
  Device device = 8;
  // midas
  Midas midas = 9;
  // mapExt
  map<string, string> mapExt = 10;
  // 订单签名，没有忽略
  string sig = 11;
  // 调用Delivery接口时透传这个id
  string transaction_id = 12;
  // 游戏场次 id
  string round_id = 13;
}

message PayData {
  // 订单号
  string bill_no = 1 [json_name = "bill_no"];
  // 扣除后余额
  uint32 balance = 2;
}

message PayRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  PayData data = 3;
}

message PresentReq {
  message AssetItems {
    string asset_id = 1;
    uint32 amount = 2;
  }
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 订单号
  string bill_no = 2;
  // 奖励个数
  uint32 amount = 3;
  // 备注, 会写流水
  string app_remark = 4;
  // 游戏场次 id
  string round_id = 5;
  // 资产
  repeated AssetItems asset_items = 7;
}

message PresentData {
  // 订单号
  string bill_no = 1 [json_name = "bill_no"];
  // 奖励后余额
  uint32 balance = 2;
}

message PresentRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  PresentData data = 3;
}

message GetProfileReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 头像长, wesing 长宽一致, 0: 原图
  int32 avatar_length = 2;
  // 头像宽
  int32 avatar_width = 3;
}

enum Gender {
  GenderUnknown = 0;
  GenderMan = 1; // 男
  GenderWoman = 2; // 女
}

message ProfileData {
  // 昵称
  string nickname = 1;
  // 头像
  string avatar = 2;
  // 加密 uid
  string encrypt_uid = 3;
  // 财富等级
  uint32 treasure_level = 4;
  // 头像框
  string avatar_frame = 5;
  // 年龄
  uint32 age = 6;
  // vip 等级
  uint32 vip_level = 7;
  // 性别
  Gender gender = 8;
  // 城市
  string city = 9;
  // Q音vip_level>0时，map["vipType"] = "1(绿钻)、2(豪华绿钻)、3(超级会员)"
  //                  map["vipEndTime"] = "(unix时间戳)"
  map<string, string> extra = 10;
}

message GetProfileRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  ProfileData data = 3;
}

message SendMailReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // to uid
  uint64 to_uid = 2;
  // openid
  string to_openid = 3;
  // content, 私信内容, content和content_id填一个
  string content = 4;
  // content_id 私信内容id, 用于多语言
  string content_id = 5;
  // 附加信息
  map<string, string> attachment = 6;
}

message SendMailRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message PrizeReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 金额
  uint32 amount = 2;
  // 订单号
  string bill_no = 3;
}

message PrizeGift {
  // 礼物名称
  string name = 1;
  // 礼物图标
  string icon = 2;
  // 礼物数量
  uint32 num = 3;
  // 礼物ID
  string gift_id = 4;
}

message PrizeRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  // 礼物列表
  repeated PrizeGift gifts = 3;
}

message TDBankReportReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 表名
  string table = 2;
  // 服务名
  string program = 3;
  // 数据
  string message = 4;
  // 业务
  string buss_id = 5;
}

message TDBankReportRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message BatchGetProfileReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // uid 列表
  repeated uint64 uid_list = 2;
  // 头像长, wesing 长宽一致, 0: 原图
  int32 avatar_length = 3;
  // 头像宽
  int32 avatar_width = 4;
  // 酷狗用这个
  repeated string encrypt_uid_list = 5;
}

message BatchGetProfileRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  map<uint64, ProfileData> data = 3;
}

message GameFeedBase {
  map<string, string> params = 1;
  string jump_url = 2; // 底部按钮跳转链接
}

message WriteGameFeedReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  string seq_id = 2; // 唯一id
  uint32 type = 3; // 动态类型, see EnumGameFeedType
  int32 expire_sec = 4; // 有效时长,单位:秒
  GameFeedBase base = 5; // 基础样式
  string roomid = 6; // 房间roomid
}

message WriteGameFeedRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message ConsumeFreeGiftReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 消耗数量 num
  uint32 num = 2;
  // 订单号 bill_no
  string bill_no = 3;
  // 来源文案 from_desc
  string from_desc = 4;
  // 上报 id
  int64 report_id = 5;
}

message ConsumeFreeGiftRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message RoomMsgReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 消息文案 msg_text
  string msg_text = 2;
  // 消息类型
  uint32 msg_type = 3;
  // 房间 ID
  string room_id = 4;
  // to uid
  uint64 to_uid = 5;
  // c2c 消息
  bool is_c2c = 6;
  // 扩展数据
  map<string, string> ext_data = 7;
}

message RoomMsgRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message SendPushReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 文案 content
  string content = 2;
  // 附加内容 attach
  map<string, string> attach = 3;
  uint64 from_uid = 4;
  string flow_config_id = 5;
}

message SendPushRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message BigHornMsgReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 消息ID msg_id
  string msg_id = 2;
  // 文案 content
  string content = 3;
  // 附加内容 attach
  // 可选：跳转房间后打开的链接 map["afterH5URL"] = "url"
  // 可选：大喇叭样式ID，不传用默认的 map["configID"] = "配置ID"
  map<string, string> attach = 4;
  // 打开H5链接，和跳转房间同时传时优先使用
  string open_H5_URL = 5;
  // 跳转房间ID
  string room_id = 6;
  // 主播uid
  uint64 anchor_uid = 7; // 一般在大喇叭的前端展示主播头像
}

message BigHornMsgRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message SingleRewardItem {
  // gift_id 奖品ID
  string gift_id = 1;
  // gift_type 奖品Type
  uint32 gift_type = 2;
  // gift_num 发放数量
  int64 gift_num = 3;
  // gift_reason 发放理由
  string gift_reason = 4;
}

message SendSingleRewardReq {
  message ConsumeAssetItem {
    string asset_id = 1;
    uint32 amount = 2;
  }
  message Device {
    string platform = 1; // 平台信息 kugou、qqmusic、qmkege、kuwo、lanren
    string version = 2; // 客户端版本 1.2.3
    string os = 3; // 系统 android、ios
  }
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 奖励ID 透传用
  int64 reward_id = 2;
  // 数量 num
  uint32 num = 3;
  // 订单号 bill_no
  string bill_no = 4;
  // 奖品信息 reward_item
  SingleRewardItem reward_item = 5;
  // 消耗的资产信息
  ConsumeAssetItem consume_item = 6;
  // 设备信息
  Device device = 7;
  map<string, string> MapExt = 8;
  // 礼包发放原始订单号 bill_no, 仅用于订单回调校验
  string origin_bill_no = 9;
}

message SendSingleRewardRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message RewardDetailReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 平台礼物ID
  string gift_id = 2;
  // 平台礼物type 参考reward_sender_comm.proto GiftType
  uint32 gift_type = 3;
}

// 子礼物信息
message SubGiftDetail {
  // 礼物ID
  string sub_gift_id = 1;
  // 礼物type 参考reward_sender_comm.proto GiftType
  string sub_gift_type = 2;
  // 礼物数量
  uint32 sub_gift_num = 3;
  // 礼物名称
  string sub_gift_name = 4;
  // 礼物logo
  string sub_gift_logo = 5;
  // 礼物单价
  uint32 sub_gift_unit_price = 6;
  // expire_type 过期类型, 1相对过期, 2绝对过期
  uint32 sub_gift_expire_type = 7;
  // expire_sec 过期时间(s), 相对过期
  uint32 sub_gift_expire_time = 8;
}

// 自定义类型
message CustomRewardItem {
  string StrRewardName = 1; // 名称
  string StrData = 2; // 数据
  uint32 URewardType = 3; // 类型
  string StrLogo = 4; // 图片
  uint32 URewardNum = 5; // 数量
  int64 LCustomValue = 6; // 价值
}

message RewardDetailRsp {
  // SubGiftDetail 平台礼物ID 所指向的礼物列表
  repeated SubGiftDetail gift_array = 1;
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
  CustomRewardItem customRewardItem = 4; // 自定义奖励类型
}

message RewardDetailBatchReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 平台礼物ID
  repeated string gift_ids = 2;
  // 平台礼物type 参考reward_sender_comm.proto GiftType
  uint32 gift_type = 3;
}

message RewardDetailBatchRsp {
  message RewardDetail {
    //SubGiftDetail 奖品ID 所指向的礼物列表
    repeated SubGiftDetail gift_array = 1;
    // 自定义类型
    CustomRewardItem customRewardItem = 2;
  }
  map<string, RewardDetail> subGiftDetails = 1;
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

message SendSingleRewardCallbackReq {
  // 游戏appid
  string appId = 1;
  // 游戏用户openid
  string openId = 2;
  // 礼包id
  int64 reward_id = 4;
  // 礼包数量 num
  uint32 num = 5;
  // 礼包发放原始订单号 bill_no
  string origin_bill_no = 6;
}

message SendSingleRewardCallbackRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message PropDetail {
  // 道具名称
  string prop_name = 1;
  // 道具logo
  string prop_logo = 2;
}

message BatchPropDetailReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 道具ID
  repeated int64 prop_ids = 2;
}

message BatchPropDetailRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  map<int64, PropDetail> data = 3;
}

message GiftDetail {
  // 礼物名称
  string gift_name = 1;
  // 礼物logo
  string gift_logo = 2;
  // 礼物单价
  uint32 gift_price = 3;
}

message BatchGiftDetailReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 礼物ID
  repeated int64 gift_ids = 2;
}

message BatchGiftDetailRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  map<int64, GiftDetail> data = 3;
}

message GetPlatBalanceReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // plat_asset_id 平台货币ID
  string plat_asset_id = 2;
  // plat_asset_type 平台货币类型 参考PlatAssetType
  uint32 plat_asset_type = 3;
}

message GetPlatBalanceRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  uint64 balance = 3;
}

message PrizeV2Req {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 金额
  uint32 amount = 2;
  // 订单号
  string bill_no = 3;
  // 待发奖礼物
  repeated PrizeGift gifts = 4;
}

message PrizeV2Rsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message GetSendGiftOrderReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 每次请求访问的唯一标识
  string orderId = 2;
  // 送礼订单下单的时间戳，精确到毫秒(酷狗用到)
  string orderTime = 3;
  // 送礼者用户id
  string userId = 4;
}

message GetSendGiftOrderRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  string data = 3;
}

message safeData {
  int32 safeType = 1; // 非0为被安全打击了
}

message SafeCheckReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 目标用户uid
  uint64 to_uid = 2;
  // 安全 appid
  int32 safe_appid = 3;
  // qua 设备qua
  string qua = 4;
  // content 待检查文案
  string content = 5;
}

message SafeCheckRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  safeData data = 3;
}

message SafeCheckPicReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 目标用户uid
  uint64 to_uid = 2;
  // 安全 appid
  int32 safe_appid = 3;
  // qua 设备qua
  string qua = 4;
  // pic 上报图片url
  string pic_url = 5;
  // 房间id
  string room_id = 6;
  // 扩展参数
  map<string, string> params = 7;
}

message SafeCheckPicRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  safeData data = 3;
}

message DataReportReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // type 数据类型
  string type = 2;
  // data 数据
  string data = 3;
  // 平台id
  uint64 platId = 4;
}

message DataReportRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message QzaReportReq {
  map<string, string> data = 1;
  bool new_report = 2;
  bool new_report_qmusic = 3 [json_name = "new_report_qmusic"];
}

message QzaReportRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message BatchGetUserFeatureReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 目标用户uid
  repeated uint64 uid_list = 2;
}

message Intimacy {
  uint64 uid = 1;
  uint32 intimacy_score = 2; // 亲密度Score*10000
}

message Feature {
  int32 age = 1; // 年龄
  int32 city = 2; // 城市code
  int32 gender = 3; // 1男2女
  int32 level = 4; // 用户等级
  repeated Intimacy intimacy_list = 5; // 亲密度列表，最多50个
}

message UserFeatureData {
  map<uint64, Feature> mapFeature = 8;
}

message BatchGetUserFeatureRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  UserFeatureData data = 3;
}

message BatchGetAnonymousStatusReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 观众列表
  repeated uint64 audiences_list = 2;
}

message AnonymousItem {
  uint32 Anonymous = 1; // 1表示匿名，0表示非匿名
}

message AnonymousData {
  map<uint64, AnonymousItem> mapStatus = 1;
}

message BatchGetAnonymousStatusRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  AnonymousData data = 3;
}

message HippyMsgUserSwitchEventReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  bool is_open = 2;
  uint32 sw_type = 3; // 调用侧指定
}

message HippyMsgUserSwitchEventRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message HippyInfo {
  string msg_txt = 1;
  string btn_txt = 2;
  string bg_pic = 3;
  string btn_pic = 4;
  string jump_url = 5;
  string round_id = 6;
  int32 conf_id = 7;
  string msg_id = 8;
}

message SendHippyMsgReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  string room_id = 2;
  int32 switch_type = 3;
  bool is_switch_filter = 4;
  HippyInfo hippy_info = 5;
}

message SendHippyMsgRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message AbtestLabelItem {
  string label_key = 1;
  string label_value = 2;
}

message UiAbtestRspItem {
  string testId = 1;
  map<string, string> map_params = 2;
}

message UIABTestReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  string business_id = 2; // 业务Id
  string channel_id = 3; // 渠道id
  string module_id = 4; // 模块id
  string qua = 5; // 客户端版本
  string dev_info = 6; // 客户端设备信息
  string caller_svr_name = 7; // 主调服务名
  repeated AbtestLabelItem labels = 8;
}

message UIABTestRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  map<string, UiAbtestRspItem> map_test_info = 3; // testInfo
  int32 interval = 4; // 请求间隔
}

message AbtestPassback {
  string uid_bag_type = 1;
  string uid_bag_id = 2;
}

message ABTestReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  string business_id = 2; // 业务Id
  string channel_id = 3; // 渠道id
  string module_id = 4; // 模块id
  string qua = 5; // 客户端版本
  string dev_info = 6; // 客户端设备信息
  string caller_svr_name = 7; // 主调服务名
  repeated AbtestLabelItem labels = 8;
  string hash_key = 9; // 分流key. hashKey空时,按strId（uid）分流
  repeated string module_keys = 10; // 批量请求
  string version = 11; // 客户端版本
  string platform = 12; // 平台 andriod、ios
}

message AbtestRspItem {
  string test_id = 1; // 实验组id
  string channel_id = 2; // 渠道id
  map<string, string> map_params = 3; // 实验参数
  string str_abtest = 4; // 数据上报用
  AbtestPassback passback = 5; // 透传abtest实验数据
}

message ABTestRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  map<string, AbtestRspItem> map_test_info = 3; // testInfo
  map<string, string> modid_to_modkey = 4;
  string abtest = 5; // 数据上报用
}

message TaskConditionBill {
  GameMiddleInfo game_middle_info = 1;
  uint32 to_uid = 2;
  uint32 condition_id = 3;
  uint32 num = 4;
  uint32 timestamp = 5;
  string consume_id = 6;
  map<string, string> map_ext = 7;
  string room_id = 8;
  string qua = 9;
}

message ReportTaskConditionBillReq {
  repeated TaskConditionBill bill = 1;
}

message ReportTaskConditionBillRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

enum FollowOptType {
  FOLLOW_TYPE_NONE = 0;
  FOLLOW_TYPE_ADD = 1;
  FOLLOW_TYPE_SUB = 2;
}

message FollowOptReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // uid 列表
  repeated uint64 uid_list = 2;
  // qua 设备qua
  string qua = 3;
  // deviceInfo
  string deviceInfo = 4;
  // FollowOptType
  FollowOptType type = 5;
  // souce 业务来源id
  int64 source = 6;
}

enum FollowResult {
  FOLLOW_TYPE_SUCC = 0;
  FOLLOW_TYPE_FAIL = 1;
}

message FollowInfo {
  FollowResult result = 1;
  uint64 uid = 2;
}

message FollowOptRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  // key:uid
  map<uint64, FollowInfo> results = 3;
}

enum RelatioMask {
  RELATION_MASK_ALL = 0; // 所有关系
  RELATION_MASK_FOLLOW = 1; // 拉取关注
}

message QueryRelationReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // uid 列表
  repeated uint64 uid_list = 2;
  // mask
  RelatioMask mask = 3;
}

enum RelationType {
  RELATION_NONE = 0; // 无关系
  RELATION_FOLLOW = 1; // 关注
}

message RalationInfo {
  RelationType type = 1;
  uint64 uid = 2;
}

message QueryRelationRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  // key:uid
  map<uint64, RalationInfo> ralations = 3;
}

enum GameCheckType {
  CheckTypeNone = 0; // 无
  CheckTypeCreate = 1; // 互动游戏创建房间
  CheckTypeJoin = 2; // 互动游戏加入房间
}

enum AdultType {
  AdultTypeUnknown = 0; // 未知
  AdultTypeFalse = 1; // 未成年
  AudltTypeTrue = 2; // 已成年
}

message QueryCertInfoReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 登录态信息
  map<string, string> cookie = 2;
  // 客户端 ip
  string client_ip = 4;
  // 校验类型
  GameCheckType check_type = 5;
  // 房间 id
  string room_id = 6;
}

message QueryCertInfoRsp {
  // 错误码
  int32 error_code = 1;
  // 错误信息
  string error_msg = 2;
  // 是否实名
  bool is_real_name = 3;
  // 是否成年
  AdultType adult_type = 4;
  // 认证链接
  string auth_url = 5;
}

message TmeEventReq {
  string event = 1;
}

message TmeEventRsp {
  // 错误码
  int32 error_code = 1;
  // 错误信息
  string error_msg = 2;
}

//来源渠道
message SourceChannel {
  uint32 source_id = 1; //来源ID, 后台分配
  string reason = 2;
  string app_name = 3; //游戏名称
}

message PlatUserAsset {
  int64 asset_id = 1;
  int64 asset_num = 2;
}

message PlatAssetChange {
  int64 asset_id = 1;
  int64 asset_num = 2;
}

enum PlatAssetType {
  PlatAssetTypeDefault = 0; //默认
  PlatAssetTypeDiamond = 1; //K歌钻石
  PlatAssetTypeDiamondGift = 2; //K歌钻石礼物
  PlatAssetTypeSilverCoin = 3; //wesing银币
  PlatAssetTypeCarrierDogecoin = 4; // 酷狗网赚金币
  PlatAssetTypeKgWZCoin = 5; // K歌网赚金币
  PlatAssetTypeLrTaskCoin = 6; // 懒人网赚金币
}

message QueryAssetReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 资产ID
  repeated int64 asset_ids = 2;
  // 来源配置
  SourceChannel source = 3;
  // 资产类型
  PlatAssetType asset_type = 4;
}

message QueryAssetRsp {
  // 错误码
  int32 error_code = 1;
  // 错误信息
  string error_msg = 2;
  // 结果资产列表
  map<int64, PlatUserAsset> map_result = 3;
}

message AddAssetReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 资产列表
  repeated PlatAssetChange assets = 3;
  // 订单ID
  string bill_no = 4;
  // 来源配置
  SourceChannel source = 5;
  // 操作时间戳 单位毫秒
  int64 timestamp = 6;
  // 资产类型
  PlatAssetType asset_type = 7;
  //场景，透传上报用
  int32 scene = 8;
}

message AddAssetRsp {
  // 错误码
  int32 error_code = 1;
  // 错误信息
  string error_msg = 2;
  // 订单ID
  string bill_no = 3;
  // 结果资产列表
  map<int64, PlatUserAsset> map_result = 4;
}

message SubAssetReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 资产列表
  repeated PlatAssetChange assets = 3;
  // 订单ID
  string bill_no = 4;
  // 来源配置
  SourceChannel source = 5;
  // 操作时间戳 单位毫秒
  int64 timestamp = 6;
  // 资产类型
  PlatAssetType asset_type = 7;
  //场景，透传上报用
  int32 scene = 8;
  // 扩展参数
  map<string, string> map_ext = 9;
}

message SubAssetRsp {
  // 错误码 资产数量不足返回 12001
  int32 error_code = 1;
  // 错误信息
  string error_msg = 2;
  // 订单ID
  string bill_no = 3;
  // 结果资产列表
  map<int64, PlatUserAsset> map_result = 4;
}

message DCReportReq {
  string table_name = 1;
  repeated string data = 2;
}

message DCReportRsp {
  // 错误码
  int32 error_code = 1;
  // 错误信息
  string error_msg = 2;
}

message SwitchInfo {
  int32 sw_type = 1;
  int32 is_open = 2; // 0: 关闭, 1:打开
}

message GetUserSwitchReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
}

message GetUserSwitchRsp {
  // 错误码
  int32 error_code = 1;
  // 错误信息
  string error_msg = 2;
  repeated SwitchInfo sw_info = 3;
}

message SetUserSwitchReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  repeated SwitchInfo sw_info = 2;
}

message SetUserSwitchRsp {
  // 错误码
  int32 error_code = 1;
  // 错误信息
  string error_msg = 2;
}

message SendUserSwitchMsgReq {
  string app_id = 1; // appId
  string room_id = 2; // 如果制定则只发特定房间消息
  int32 room_type = 3; // 房间类型， 0:全部 1:直播间 2:歌房
  int32 sw_type = 4; // 调用侧指定
  int32 msg_type = 5; // 消息类型
  string switch_msg_info = 6; // 消息类型
  string msg_id = 7; // 消息ID
}

message SendUserSwitchMsgRsp {
  // 错误码
  int32 error_code = 1;
  // 错误信息
  string error_msg = 2;
}

message BatchUserDirectReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  string open_type = 2; // open type
  string open_key = 3; // open key
  string open_id = 4; // open id (cookie里的openid)
  repeated int64 targetids = 5; // 定向id
  string qua = 6; // qua
  string device = 7; // 设备信息
}

message DirectRes {
  int64 target_id = 1;
  int32 res = 2; // 结果(1:命中,0:未命中)
}

message BatchUserDirectRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  repeated DirectRes res = 3;
}

message GetRiskLevelReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // uid 列表
  repeated uint64 uid_list = 2;
  // 扩展参数
  map<string, string> mapExt = 3;
}

message GetRiskLevelRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  map<uint64, int64> mapRiskLevel = 3; // uid <-> 风险等级
  map<uint64, int32> mapErrCodes = 4; // uid <-> <-> err code
}

message GetUserIntimacyReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
}

message GetUserIntimacyRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  map<uint64, float> intimacy = 3;
}

message JudgeOnlineReq {
  string app_id = 1;
  repeated uint64 uids = 2;
}

message OnlineInfo {
  uint32 status = 1;
  uint32 last_hb_time = 2; // 上次心跳时间
}

message JudgeOnlineRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  map<uint64, OnlineInfo> online_info = 3;
}

message RefundReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 订单号
  string bill_no = 2;
  // 备注，会写流水日志
  string app_remark = 3;
}

message RefundRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message NoteRankItem {
  uint64 uid = 1; // 用户id
  uint64 notes = 2; // 音符
  string nickname = 3; // 用户昵称
  string avatar = 4; // 用户头像
  CwGameRankInfo cwGameRankInfo = 5; // cw游戏排行榜信息
  int32 extraNote = 6; // 是否有额外音符可收
}

message SelfRankItem {
  int32 rank = 1; // 用户排名
  uint64 notes = 2; // 音符
  string nickname = 3; // 用户昵称
  string avatar = 4; // 用户头像
  string friendPetCover = 5; // 宠物头像
}

message NoteRankReq {
  uint64 uid = 1; // 用户id
  int32 passback = 2; // 首次不传, 服务器返回什么, 传什么
}

message NoteRankRsp {
  int32 error_code = 1;
  string error_msg = 2;
  repeated NoteRankItem list = 3; // 排行榜列表
  bool has_next = 4; // 是否还有下一页
  int32 passback = 5; // 再次翻页的时候需要把这个东西传过来
  int32 friendApiAuth = 6; // 好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆
  int32 bingStatus = 7; // 绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定
  SelfRankItem selfRankItem = 8; // 自己的榜单信息
}

message NoteWeekRankReq {
  uint64 uid = 1; // 用户id
  int32 passback = 2; // 首次不传, 服务器返回什么, 传什么
}

message NoteWeekRankRsp {
  int32 error_code = 1;
  string error_msg = 2;
  repeated NoteRankItem list = 3; // 排行榜列表
  bool has_next = 4; // 是否还有下一页
  int32 passback = 5; // 再次翻页的时候需要把这个东西传过来
  int32 friendApiAuth = 6; // 好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆
  int32 bingStatus = 7; // 绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定
  SelfRankItem selfRankItem = 8; // 自己的榜单信息
}

message GameQuickGiftConfiglReq {
  // 游戏appid
  string game_appid = 1;
}

message GameQuickGiftConfiglRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  map<int64, GiftDetail> send_data = 3; // 送礼列表
  map<int64, GiftDetail> back_data = 4; // 回礼列表
}

message QueryFriendsReq {
  int64 uid = 1;
  string app_id = 2; // 小游戏AppId
  uint32 mask = 3; // 0x1 双向关注 0x2 绑定好友 0x4 单向关注
  map<string, string> cookie = 4; // 登录态信息
}

message QueryFriendsRsp {
  message Data {
    repeated int64 uid_list = 1;
    int32 friendApiAuth = 2; // 好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆
    int32 bingStatus = 3; // 绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定
  }
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  Data data = 3 [json_name = "data"];
}

message QueryBlacklistReq {
  int64 vUid = 1;
  repeated int64 huidList = 2;
  // op=0:
  // 验证vuid是否在huidlist的黑名单当中，blacklist为在其黑名单当中的uid列表
  // op=1:
  // 验证huidlist是否在vuid的黑名单当中，blacklist为在其黑名单当中的uid列表
  int64 op = 3;
}

message QueryBlacklistRsp {
  message Data {
    repeated int64 uidList = 1;
  }
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  Data data = 3 [json_name = "Data"];
}

message VerifyFollowReq {
  enum VerifyFollowType {
    Forward = 0; // 验证uid是否关注了vecUid
    Reverse = 1; // 验证vecUid是否关注了uid
  }
  int64 uid = 1;
  repeated int64 vecUid = 2;
  VerifyFollowType followType = 3;
}

message VerifyFollowRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  map<int64, bool> mapUid = 3 [json_name = "MapUid"];
}

enum PetStatus {
  PetNotAdopt = 0; // 尚未被领取, 所有宠物的初始状态
  PetAdopted = 1; // 已领取孵化中, 用户点击收下宠物蛋后转这个状态
  PetHatched = 2; // 已孵化成功, 成功孵化后转这个状态
  PetIdle = 3; // 在小窝, 用户点击领养宠物/出门回家被查看后转这个状态
  PetOutWaiting = 4; // 点击出门之后, 宠物处于出门等待中状态
  PetOutting = 5; // 出门中, 宠物匹配到后转这个状态
  PetBack = 6; // 宠物刚刚回家
  PetDying = 7; // 濒临死亡状态
  PetRescuing = 8; // 听歌抢救状态
}

enum PetLiveStatus {
  PetLSNormal = 0; // 正常状态
  PetLSHungry = 1; // 饥饿状态
}

enum PetInteractiveStatus {
  PetDefault = 0; // 无
  PetReceivingNote = 1; // 收音符
  PetHunger = 2; // 饿了
  PetDirty = 3; // 脏了
  PetUnHappy = 4; // 不开心
  PetSick = 5; // 生病
}

message CwGameRankInfo {
  int64 friendPetId = 1; // 好友的宠物id
  string friendPetCover = 2; // 宠物头像
  PetStatus friendPetStatus = 3; // 宠物状态
  PetLiveStatus friendPetLiveStatus = 4; // 宠物饥饿状态
  PetInteractiveStatus petInteractiveStatus = 5; // 宠物互动操作状态
  int32 concerned = 6; // 是否关注 1:展示关注icon 2:展示微信 3:展示qq
}

message GetCwGameRankInfoReq {
  string app_id = 1; // app id
  uint64 uid = 2; // 主人态 uid
  repeated uint64 uid_list = 3; // 榜单 uid 列表,支持查自己
}

message GetCwGameRankInfoRsp {
  int32 error_code = 1;
  string error_msg = 2;
  map<uint64, CwGameRankInfo> cwGameRankInfos = 3; // 用户资料 key 为 uid
}

message InteractiveGameSwitchRoomReq {
  string app_id = 1; // app id
  uint64 uid = 2; // 主人态 uid
  string mode_id = 3;
  string pay_mode_id = 4;
  map<string, string> ext = 5;
}

message InteractiveGameSwitchRoomRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  string room_id = 3;
}

// 游戏业务自己实现
// SafeCheckPicCallbackReq 图片安全审查异步结果通知
message SafeCheckPicCallbackReq {
  map<string, string> params = 1; // 透传参数
  string suggestion = 2; // Block：建议屏蔽，Review ：建议人工复审，Pass：建议通过
}

// 游戏业务自己实现
// SafeCheckPicCallbackReq 图片安全审查异步结果通知
message SafeCheckPicCallbackRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message GetInvitateFriendReq {
  GameMiddleInfo game_middle_info = 1;
  uint32 limit = 2; // 限制长度 最多100
}

message GetInvitateFriendRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  repeated uint64 uids = 3; // 好友列表
}

// Family 家族信息
message Family {
  FamilyBaseInfo base = 1; // 基础信息
}

// FamilyBaseInfo 家族基本信息
message FamilyBaseInfo {
  uint64 id = 1; // 家族id
  string name = 2; // 家族名称
  string avatar = 3; // 家族头像
  uint64 adminUid = 4; // 族长uid
  uint32 status = 5; // 家族状态,见game_group.proto#GroupStatus
  uint64 createTime = 6; // 创建时间
  uint32 freeEnter = 7; // 1==免审加入
  uint32 memberCount = 8; // 人数
  uint32 level = 9; // 等级
  uint32 userLevelLimit = 10; // 加入用户等级限制
  uint32 userJoinWealthLevelLimit = 11; // 用户加入财富等级限制
}

// BatchGetByFamilyReq 批量拉取家族信息
message BatchGetFamilyByIdReq {
  string appId = 1;
  uint64 uid = 2;
  repeated uint64 groupIds = 3; // 家族id列表
}

// BatchGetByFamilyRsp 批量拉取家族信息
message BatchGetFamilyByIdRsp {
  map<uint64, FamilyBaseInfo> mapInfo = 1; // groupId --> FamilyBaseInfo
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

message BatchGetFamilyByUidReq {
  string appId = 1;
  repeated uint64 uids = 2;
}

message BatchGetFamilyByUidRsp {
  map<uint64, FamilyBaseInfo> mapInfo = 1; // uid --> FamilyBaseInfo
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

message FamilyConveneReq {
  string appId = 1; // appId
  uint64 uid = 2; // uid
  uint64 groupId = 3; // 家族id
  string content = 4; // 召集文案
  string jumpUrl = 5; // 跳转链接
  string icon = 6; // 展示图片
  string title = 7; // 标题
}

message FamilyConveneRsp {
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

message FamilyRecommandListReq {
  string appId = 1; // appId
  uint64 uid = 2; // uid
  uint64 num = 3; // 拉取的数量
}

message FamilyRecommandListRsp {
  repeated FamilyBaseInfo groups = 1; // 推荐列表
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

message FamilyJoinReq {
  string appId = 1; // appId
  uint64 uid = 2; // uid
  uint64 groupId = 3; // 家族id
  string reason = 4; // 加入理由
}

// 根据grpc code = 0 判断是否申请成功
message FamilyJoinRsp {
  uint32 freeEnter = 1; // 如果grpc code == 0且freeEnter == 1, 表示自动加入成功, 否则等审核
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

enum FamilyGroupScoreEventType {
  FAMILY_GROUP_EVENT_SCORE_TYPE_TOTAL = 0; // 所有
  FAMILY_GROUP_SCORE_EVENT_TYPE_TASK_COMPLETE = 1; // 任务完成
  FAMILY_GROUP_SCORE_EVENT_TYPE_CONSUME_KB = 2; // 送礼
  FAMILY_GROUP_SCORE_EVENT_TYPE_GAME_COMPLETE = 3; // 游戏获取
}

message FamilyGroupScoreEventReq {
  string appId = 1; // appId
  uint64 uid = 2; // openId
  uint64 groupId = 3; // 家族id
  uint64 score = 4; // 积分
  FamilyGroupScoreEventType scoreType = 5; // 积分类型
  string consumeId = 6; // 幂等id
  map<string, string> mapExt = 7;
}

message FamilyGroupScoreEventRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message SafeCheckV2Req {
  SafeCheckV2DeviceInfo device = 1; // 设备信息, 有的尽量填
  SafeCheckV2UserInfo user = 2; // 可选, 用途: 数据分析会用到可能会作为标签生产的依据, 有的尽量填
  SafeCheckV2BasicInfo basic = 3; // 基础信息, 包含业务透传信息
  SafeCheckV2CheckInfo check = 4; // 送审信息
  map<string, string> mapExt = 5; // 透传信息
  string platform = 6; // 平台
  GameMiddleInfo game_middle_info = 7; // 游戏中台数据
}

message SafeCheckV2Rsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  string suggestion = 3; // Block：建议屏蔽，Review ：建议人工复审，Pass：建议通过
  SafeCheckV2BasicInfo basic = 4; // 请求时携带的basic信息
  SafeCheckV2ResultDetail detail = 5; // 审查详情
}

message SafeCheckCallbackV2Req {
  string requestId = 1;
  string taskId = 2;
  string dataId = 3;
  string callback = 4; // 解析CallbackMsg的json字符串
  string suggestion = 5;
  string strategyConclusion = 6;
  uint32 resultType = 7; // 结果方式 1:机审结果，2：人审结果, 3：命中审核结果缓存,把人审结果缓存下来，作为下次审核的结果自动返回block或pass。
}

message SafeCheckCallbackV2Rsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message SafeCheckV2ResultDetail {
  int32 hitType = 1; // //打击方式
  string strPrompt = 2; // 提示语
  int32 hitReason = 3; //
  string strReason = 4; // 打击原因
  string strategyConclusion = 5; // 结论
  map<string, string> mapExt = 6; // 其他信息
}

message SafeCheckV2CheckInfo {
  string textContent = 1;
  string title = 2;
  string atInfo = 3; // 用于保存 @内容;
  string comment = 4; // 分享或转载理由
  repeated string imageUrl = 5; // 图片
  string audioUrl = 6; // 音频
  string videoUrl = 7; // 视频
}

message SafeCheckV2CallbackMsg {
  string callbackService = 1; // 业务自定义回调路由, eg: kg.game.DemoServer/callback
  bytes customData = 2; // 业务自定义回调透传数据
}

message SafeCheckV2BasicInfo {
  string apiVersion = 1; // 填v1.0
  string appId = 2; // 安全侧分配
  string category = 3; // 安全侧分配
  string platform = 4; // 平台(kg/music)
  string sendTimeMillisecond = 5; // 时间戳字符串
  uint32 accountType = 6; //该字段表示业务用户ID对应的账号类型，取值：1-微信uin，2-QQ号，3-微信群uin，4-qq群号，5-微信openid，6-QQopenid，7-其它string。该字段与账号ID参数（UserId）配合使用可确定唯一账号。
  string postUin = 7; // 该字段可传入微信openid、QQopenid、字符串等账号信息，与账号类别参数（accountType）配合使用可确定唯一账号
  string requestId = 8; // 请求id
  string dataId = 9; // 该字段用于返回检测对象对应请求参数中的DataId，与输入的DataId字段中的内容对应。注意：此字段可能返回 null，表示取不到有效值。
  string callback = 10; // 回传参数, 解析CallbackMsg的json字符串
  string callbackUrl = 11; //回调地址, 不用填, 中台会自己填充
}

message SafeCheckV2DeviceInfo {
  string ip = 1;
  string mac = 2;
  string imei = 3;
  string idfa = 4;
  string idfv = 5;
  uint32 mobileFlag = 6; // 是否来自手机
  string mobleQUA = 7; // qua
  string uuid = 8;
  string udid = 9;
  string qimei36 = 10;
  string deviceInfo = 11;
}

// Optional 可选
message SafeCheckV2UserInfo {
  string nickname = 1;
  uint32 gender = 2; // 0 未知 1男 2女
  uint32 age = 3;
  uint32 level = 4; // 等级，0（默认值，代表等级未知）、1（等级较低）、2（等级中等）、3（等级较高），目前暂不支持自定义等级
  string phone = 5;
  string headUrl = 6;
  string signature = 7;
  string userRole = 8; // 用户类型角色，角色与安全中心约定即可
  uint32 isPremium = 9; // 是否有签约付费属性，0为默认值，1为有
  uint32 friendNum = 10; // 好友数量
  uint32 fansNum = 11; // 粉丝数量
  string hostUserRole = 12; // 被动方用户类型角色, 如歌房送礼 接收方的角色
}

message QueryGeoReq {
  string ip = 1;
}

message GeoInfo {
  string countryCode = 1;
  string countryName = 2;
  string provinceCode = 3;
  string provinceName = 4;
  string cityCode = 5;
  string cityName = 6;
}

message QueryGeoRsp {
  GeoInfo geo = 1;
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

message CwsxQueryOrdersReq {
  int64 start_time = 1 [json_name = "start_time"];
  int64 end_time = 2 [json_name = "end_time"];
  int64 passback = 3;
}

message CwsxQueryOrdersRsp {
  message Data {
    string data = 1;
    int64 passback = 2;
    bool has_more = 3 [json_name = "has_more"];
  }
  Data data = 1;
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

message CreateTradeReq {
  message Goods {
    string goods_id = 1;
    int64 goods_num = 2;
    int64 goods_amount = 3;
  }
  GameMiddleInfo game_middle_info = 1;
  string offer_id = 2; // 购买场景 id
  int64 amount = 3; // 订单总金额
  string description = 4; // 订单描述（会展示在微信支付页面）
  string business_data = 5; // 业务透传数据（发货时会回传）
  repeated Goods goods_list = 6; // 物品列表
  int64 expire_time = 7; // 订单过期时间 单位秒 不设置会用默认的
  string user_ip = 8; // 用户 ip
  string device = 9; // 用户设备号
}

message CreateTradeRsp {
  string trade_id = 1; // 订单号
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

message CwsxGetSuggestionReq {
  enum OsType {
    Android = 0;
    Ios = 1;
  }
  enum AppId {
    AppIdKg = 0;
    AppIdQMusic = 1;
    AppIdKugou = 2;
    AppIdWechat = 3;
  }
  message DeviceInfo {
    OsType osType = 1;
  }
  message BeforeItems {
    string itemId = 1;
    int64 itemCnt = 2;
  }
  message GameContext {
    DeviceInfo deviceInfo = 1; // 设备信息
    int64 curLevel = 2; // 当前闯关的关卡id
    int64 totalCoins = 3; // 当前金币数
    int64 totalHealthpoints = 4; // 当前体力值
    repeated BeforeItems beforeItems = 5;
    map<string, string> extMap = 6;
  }
  uint64 uid = 1;
  AppId appId = 2;
  GameContext context = 3;
}

message CwsxGetSuggestionRsp {
  enum OperationType {
    OperationTypeModifyDifficulty = 0;
  }
  enum DifficulutyType {
    DifficulutyTypeEasy = 0;
    DifficulutyTypeNormal = 1;
    DifficulutyTypeHard = 2;
  }
  message OperationValue {
    DifficulutyType difficulty = 1;
    float difficultyValue = 2;
  }
  message Operation {
    OperationType operationType = 1;
    OperationValue operationValue = 2;
  }
  repeated Operation operationSuggestions = 1;
  string algorithmInfo = 2;
  string traceId = 3;
}

message ZhiYanReportReq {
  string appMark = 1;
  string metricGroup = 2;
  map<string, double> reportIndex = 3;
  map<string, string> reportTag = 4;
  int64 reportTs = 5;
}

message ZhiYanReportRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message CwsxAdSpotTimesReq {
  string appId = 1;
  string uid = 2;
}

message CwsxAdSpotTimesRsp {
  map<int64, int64> spotTimes = 1;
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

enum AdCheckMask {
  AdCheckMaskNone = 0; //只需校验主广告曝光
  AdCheckMaskDownloadActive = 1; //需要校验广告点击激活是否通过
}

message AdvertCheckReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 广告曝光token
  string adToken = 2;
  // 广告位id
  string adPosId = 3;
  // qimei36设备号
  string qimei36 = 4;
  // 场景id(可选)
  string sceneId = 5;
  // 广告校验mask
  AdCheckMask adCheckMask = 6;
}

message AdvertCheckRsp {
  // 广告曝光唯一id, 类似订单号的逻辑,也可以用token代替
  string traceId = 1;
  // 曝光校验结果，0是校验通过 1是校验不通过
  int32 result = 2;
  // 广告ecpm数值奖励数量
  uint64 rewardNum = 3;
  int32 error_code = 4 [json_name = "error_code"];
  string error_msg = 5 [json_name = "error_msg"];
  string EncodedEcpm = 6; // ecpm加密值
  uint32 ecpmCoin = 7; // ecpm换算成金币数
}

enum RewardResultType {
  Reward_Default = 0; //默认值
  Reward_Success = 1; //领奖达成
  Reward_Fail = 2; //领奖失败
  Reward_Timeout = 3; //领奖超时
}

message RewardContent {
  string reward_type = 1; // 协商奖励类型标识 1:免费时长(单位秒)，2:k歌金币，3:免广告时长(单位秒) 4: 短剧金币 5：使用次数
  int64 reward_amount = 2; // 奖励数值
  int64 cost = 3; // (非必填) 奖励成本,换算为人民币千分之一分，即人民币分/1000,做风控估算参考
  string task_id = 4; // (非必填) 任务id，如果通过任务平台则填入
  string reward_id = 5; // (非必填) 奖励id，如果奖励有对应唯一id则可以填入，方便定位追踪
}

message AdvertRewardCallbackReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  string adToken = 2; // 广告曝光token
  string adPosId = 3; // 广告位id
  string qimei36 = 4;
  string sceneId = 5; // 场景id(可选)
  RewardResultType result = 6;
  RewardContent reward_content = 7;
}

message AdvertRewardCallbackRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

enum IncentiveType {
  IncentiveTypeNone = 0; //非法类型
  IncentiveTypeBI = 1; //使用后台下发的激励广告奖励数值
  IncentiveTypeECPM = 2; //使用商广返回的激励广告奖励数值（基于ecpm）
}

message AdvertScene {
  // 广告位id
  string adPosId = 1;
  // 场景id(可选)
  string sceneId = 2;
}

message AdvertInfoReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 广告场景
  repeated AdvertScene advertSceneList = 2;
}

message AdvertInfo {
  // 广告奖励数值类型
  IncentiveType incentiveType = 1;
  // 后台计算出的金币值
  uint64 rewardNum = 2;
  // 是否展示广告
  bool showAdvert = 3;
  // 剩余广告次数(仅用于前端展示次数，是否能看广告用showAdvert字段判断)
  uint32 leftAdvert = 4;
}

message AdvertInfoRsp {
  // 广告信息
  repeated AdvertInfo advertInfoList = 1;
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

message AdvertReceiveRewardReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  // 广告曝光token
  string adToken = 2;
  // 广告位id
  string adPosId = 3;
  string qimei36 = 4;
  // 场景id(可选)
  string sceneId = 5; // 场景id(可选)
}

message AdvertReceiveRewardRsp {
  // 广告曝光唯一id
  string traceId = 1;
  // 领取结果，0是成功
  int32 result = 2;
  // 广告ecpm数值奖励数量
  uint64 rewardNum = 3;
  int32 error_code = 4 [json_name = "error_code"];
  string error_msg = 5 [json_name = "error_msg"];
}

message GamePackapiReq {
  // 游戏中台数据
  GameMiddleInfo game_middle_info = 1;
  uint64 to_uid_id = 2; // 动作朝向 uid
  uint32 cmd_id = 3; // cmd类型 游戏自己定义
  string msg_id = 4; // 消息唯一ID
  uint32 ts = 5; // 时间时间戳
  string payload = 6; // app_id + cmd_id对接的结构体 json.Marshal 之后的结果
}

message GamePackapiRsp {
  string msg_rtn = 1; //回包
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

message AlarmProxyReq {
  int32 alarmType = 1;
  string msg = 2;
}

message AlarmProxyRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message LotteryReq {
  GameMiddleInfo game_middle_info = 1;
  string activityID = 2;
  string consumeID = 3;
  map<string, string> mapParams = 4; // 透传扩展参数
}

// 奖项信息，其他字段后续自行补充
message AwardInfo {
  string id = 1; // 奖项id
  string remarks = 2; // 备注信息
}

message SafetyParams {
  string validateURL = 1; // 验证码url，error_code=LuckyDrawNeedSafeVerify时使用
}

message LotteryRsp {
  int32 error_code = 1 [json_name = "error_code"]; // 业务错误码，参考 pb/code.proto LuckyDrawXX
  string error_msg = 2 [json_name = "error_msg"];
  string activityID = 3; // 活动id
  AwardInfo awardInfo = 4; // 中奖奖项信息
  SafetyParams safetyParams = 5; // 安全相关参数
  uint32 ticketCnt = 6; // 用户拥有的券数量
}

message AddTicketReq {
  GameMiddleInfo game_middle_info = 1;
  uint32 cnt = 2; // 增加数量
  string activityID = 3; // 活动id
  string seqID = 4; // 去重id
}

message AddTicketRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  uint32 ticketCnt = 3; // 用户拥有的券数量
}

message GroupGetMemberListReq {
  int32 list_type = 1; //请求类型
  uint32 group_id = 2; // 家族ID
  int32 limit = 3; // 每次返回数量
  string passback = 4; // 回传参数
  string source = 5; // 访问来源
}

message GroupMemberInfo {
  uint32 uid = 1;
  string nickname = 2; // 昵称
}

message GroupGetMemberListRsp {
  repeated GroupMemberInfo vec_list = 1; //返回结果
  uint32 total = 2;  // 总数量
  uint32 has_more = 3; // 是否还有更多
  string passback = 4;
}

message GetUsersRightReq {
  string room_id = 1;
  repeated int64 user_id_list = 2;
}

message GetUsersRightRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  map<int64, adapter_user.RightInfo> uid_to_right_info = 3;
}
