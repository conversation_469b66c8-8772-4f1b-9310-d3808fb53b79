syntax = "proto3";

package game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_segment";

//积分计算方式
enum CalcRuleType {
  CALC_RULE_TYPE_NONE = 0; //无效类型
  CALC_RULE_TYPE_ELO = 1; //elo等级体系
  CALC_RULE_TYPE_SCORE = 2; //直接积分计算
}

//奖励发放方式
enum SendAwardType {
  CALC_RULE_TYPE_DIRECT = 0; //直接发放
  CALC_RULE_TYPE_MANUAL = 1; //手动领取
}

//结算状态
enum SettlementStatus {
  SETTLEMENT_STATUS_NONE = 0; //未知状态
  SETTLEMENT_STATUS_DOING = 1; //结算中
  SETTLEMENT_STATUS_DONE = 2; //结算完成
}

// 赛季参加准入类型
enum SeasonAttendType {
  SEASON_ATTEND_NONE = 0; //未知
  SEASON_ATTEND_All = 1; //所有人可参与
  SEASON_ATTEND_WHITE_LIST = 2; //白名单配置
}

// 赛季信息
message SeasonInfo {
  int32 seasonID = 1; //赛季ID
  string title = 2; //赛季标题
  string desc = 3; //赛季描述
  int64 startTime = 4; //赛季开始时间
  int64 endTime = 5; //赛季结束时间
}

//分组类型
enum GroupType {
  GROUP_TYPE_NONE = 0; //无效类型
  GROUP_TYPE_UP = 1; //升段区
  GROUP_TYPE_KEEP = 2; //保段区
  GROUP_TYPE_DOWN = 3; //降段区
}

//排名上一期/本期
enum RankType {
  RANK_TYPE_NONE = 0; //无效类型
  RANK_TYPE_CURR = 1; //本期
  RANK_TYPE_PREV = 2; //上一期
}

//数据查询标志
enum Mask {
  MASK_NONE = 0; //不需要查任何信息
  MASK_TOP_RANK = 1; //需要巅峰榜topn头像
  MASK_AWARD_RED_POINT = 2; //需要段位奖励红点
}

//分组排名状态
enum RankStatus {
  RANK_STATUS_NONE = 0; //用户未参与
  RANK_STATUS_FOGGING = 1; //迷雾状态
  RANK_STATUS_OPENING = 2; //公开状态
}

//奖励领取状态
enum AwardStatus {
  AWARD_STATUS_NONE = 0; //未知状态
  AWARD_STATUS_LOCK = 1; //未解锁
  AWARD_STATUS_UNLOCK = 2; //已解锁未领取
  AWARD_STATUS_PICK = 3; //已领取
}

//参赛模式
enum PayMode {
  PAY_MODE_FREE = 0; //免费场
  PAY_MODE_REQUIRED = 1; //付费场
  PAY_MODE_PROP = 2; //道具场,比如鲜花、饭团
}

message AwardItem {
  string name = 2; //奖励名称
  int32 num = 3; //奖励数量
  string icon = 4; //奖励Icon
  string desc = 5; //奖励描述
}

enum GameMode {
  GAME_MODE_SINGLE = 0; //
  GAME_MODE_TEAM = 1; // 组队模式
}

enum GameSegmentLevel {
  SEGMENT_LEVEL_NONE = 0; //未知类型
  SEGMENT_LEVEL_BRONZE_III = 1000; //青铜III
  SEGMENT_LEVEL_BRONZE_II = 1100; //青铜II
  SEGMENT_LEVEL_BRONZE_I = 1200; //青铜I
  SEGMENT_LEVEL_SILVER_III = 3000; //白银III
  SEGMENT_LEVEL_SILVER_II = 3100; //白银II
  SEGMENT_LEVEL_SILVER_I = 3200; //白银I
  SEGMENT_LEVEL_GOLD_V = 5000; // '黄金',
  SEGMENT_LEVEL_GOLD_IV = 5100; //黄金IV
  SEGMENT_LEVEL_GOLD_III = 5200; //黄金III
  SEGMENT_LEVEL_GOLD_II = 5300; //黄金II
  SEGMENT_LEVEL_GOLD_I = 5400; // '黄金',
  SEGMENT_LEVEL_PLATINUM_V = 7000; //铂金V
  SEGMENT_LEVEL_PLATINUM_IV = 7100; //铂金IV
  SEGMENT_LEVEL_PLATINUM_III = 7200; //铂金III
  SEGMENT_LEVEL_PLATINUM_II = 7300; //铂金II
  SEGMENT_LEVEL_PLATINUM_I = 7400; //铂金I
  SEGMENT_LEVEL_DIAMOND_V = 9000; //钻石V
  SEGMENT_LEVEL_DIAMOND_IV = 9100; //钻石IV
  SEGMENT_LEVEL_DIAMOND_III = 9200; //钻石III
  SEGMENT_LEVEL_DIAMOND_II = 9300; //钻石II
  SEGMENT_LEVEL_DIAMOND_I = 9400; //钻石I
  SEGMENT_LEVEL_MASTER_V = 11000; //大师V
  SEGMENT_LEVEL_MASTER_IV = 11100; //大师IV
  SEGMENT_LEVEL_MASTER_III = 11200; //大师III
  SEGMENT_LEVEL_MASTER_II = 11300; //大师II
  SEGMENT_LEVEL_MASTER_I = 11400; //大师I
  SEGMENT_LEVEL_KING = 13000; //至尊王者
}
