syntax = "proto3";

package game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_segment";

import "pb/game_segment/game_segment_comm.proto";

// 段位服务
service Segment {
  // 段位积分计算接口（游戏结果流水 -> SegmentCalculate -> 段位体系）
  rpc SegmentCalculate(SegmentCalculateReq) returns (SegmentCalculateRsp);
  // 批量查询用户段位（最多50个）
  rpc BatchGetUserSegment(BatchGetUserSegmentReq) returns (BatchGetUserSegmentRsp);
  // 查询当前赛季段位配置
  rpc GetSegmentLevel(GetSegmentLevelReq) returns (GetSegmentLevelRsp);
}

message SegmentCalculateReq {
  message Player {
    string userID = 1; //userID
    int32 team = 2; //组队模式下，相同的为队友
  }
  message Result {
    string userID = 2; //userID
    uint32 rank = 3; //排名, 从1开始
    int64 score = 4; //获得分数
    int32 team = 5; //组队模式下，相同的为队友
  }
  string bizAppID = 1; //bizAppID
  string billNo = 2; //去重用的，bizAppID+billNo
  int64 ts = 3; //游戏开始时间
  PayMode payMode = 4; //参赛类型
  int64 ticket = 5; //参赛门票
  repeated Player players = 6; //对局玩家
  repeated Result result = 7; //对局结算用户
  GameMode gameMode = 8; //游戏模式，组队/非组队
  int64 gameOverTs = 9; //游戏结束时间
  map<string, string> mapGameLimit = 10; //两两对局限制
}

message SegmentCalculateRsp {
  message SegmentItem {
    string mainName = 2; //主段位名称，eg. 黑铁、青铜、白银、黄金
    string subName = 4; //子段位名称，eg. I、II、III、IV、V、VI
    string icon = 5; //段位Icon
    string avatarFrame = 6; //段位头像框
  }
  message SegmentResult {
    string bizAppID = 1; //bizAppID
    string userID = 3; //userID
    int64 segScore = 4; //当前获得段位分(有正负数情况)
    int64 totalSegScore = 5; //总段位分
    SegmentItem segmentItem = 6; //段位等级信息
  }
  map<string, SegmentResult> mapSegresult = 1;
}

message BatchGetUserSegmentReq {
  message Player {
    string userID = 1; //openID
    uint32 mask = 2; //0:只查询段位 1:同时返回groupType+rank+score（不需要传0,传1耗时会增加）
  }
  string bizAppID = 1; //bizAppID
  repeated Player vecPlayer = 2; //查询列表（最多50个）
}

message BatchGetUserSegmentRsp {
  message SegmentInfo {
    GameSegmentLevel level = 1; //段位等级
    string segmentName = 2; //段位名称
    string segmentIcon = 3; //段位Icon
    GroupType groupType = 4; //分组类型，0表示未参与
    int32 rank = 5; //周期排名，-1表示未参与
    int64 score = 6; //周期积分
  }
  map<string, SegmentInfo> mapSegmentInfo = 1;
}

message GetSegmentLevelReq {
  string bizAppID = 1; //bizAppID
}

message GetSegmentLevelRsp {
  message SegmentConfig {
    GameSegmentLevel level = 1; //段位等级
    string segmentName = 2; //段位名称
    string segmentIcon = 3; //段位Icon
  }
  repeated SegmentConfig vecSegmentConfig = 1; //段位配置
}

message MsgPeriodSettlementResult {
  message SegmentInfo {
    GameSegmentLevel level = 1; //段位等级
    string segmentName = 2; //段位名称
    string segmentIcon = 3; //段位Icon
  }
  string userID = 1; //游戏场景下的openid
  string bizAppID = 2; //bizAppID
  GroupType groupType = 3; //分组类型
  SegmentInfo lastSegment = 4; //周期结算前的段位
  SegmentInfo currSegment = 5; //周期结算后的段位
}
