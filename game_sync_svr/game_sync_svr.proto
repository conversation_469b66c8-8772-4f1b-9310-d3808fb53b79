syntax = "proto3";

package component.game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_sync_svr";

import "pb/asset_admin/asset_admin.proto";

//
//  平台参考
//    enum EPlatID {
//    PLAT_UNKNOW = 0; // 未知
//    PLAT_WESING = 1; // wesing
//    PLAT_KG = 2; // K歌
//    PLAT_QQMUSIC = 4; // Q音
//  }
//

service game_sync_svr {
  rpc GetGameConf(GetGameConfReq) returns (GetGameConfRsp);
  rpc GameConfSync(GameConfSyncReq) returns (GameConfSyncRsp);
}

message GameConfSyncReq {
  string appId = 1;
  repeated GameAsset assets = 2;
  string appName = 3;
  // 游戏配置，key为平台id，参考gopen.EPlatID
  map<uint32, GamePlatConf> gameConfigs = 4;
  map<int64, PackageConfig> packageConfigs = 5;
  string uniAppId = 6;
  string shareAppId = 7;
  string useCustomOpenId = 8;
}

message GameConfSyncRsp {
  string appId = 1;
}

message GetGameConfReq {
  string appId = 1;
}

message GetGameConfRsp {
  AssetConfigMap config = 1;
}
