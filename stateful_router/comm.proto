syntax = "proto3";

package stateful_router;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/stateful_router";

// 新流量: NodeStatus>=2 对于2,可以路由接入,其他屏蔽接入
// 旧流量: NodeStatus>=2 按照原有接入,即使失败
enum NodeStatus {
  Invalid = 0; // 无效
  Pending = 1; // 等待拉取负载
  Running = 2; // 正常
  Isolation = 4; // 被隔离
  PullLoadFail = 8; // 拉取不到负载(为正常提供负载拉取接口/拉取接口异常)
  ClusterCritical = 16; // 所在服务健康状态异常(critical)
  NodeCritical = 32; // 非健康状态(critical/warning)
  TagFail = 64; // 打标签失败
}

// NodeLoad 节点负载量化指标
// 具体获取方式可参照例子:
// app/stateful_router/server/node_example/internal/load/load_monitor.go
// 负载均衡公式,加权平均:
// nodes = [node1, node2, node3, ...]
// 给每个节点计算负载 nodeRank = 2.0 * c + m
// selected = min_nodeRank(nodes)
message NodeLoad {
  float m = 1; // 内存占用 m = 当前进程所占内存(RES) / 容器限制内存(CGroupLimit), 这里特指物理内存
  float c = 2; // CPU占用 c = 当前进程所占用CPU核数  / 容器限制核数(CGroupLimit)
}

message Node {
  string ip = 1; // IP
  string label = 2; // 标签
  int32 port = 3; // port
  NodeLoad load = 4; // 负载详情
  NodeStatus status = 5; // 状态
  repeated NodeSID sids = 6; // 绑定信息
  int64 lastUpdateTs = 7; // 最后更新时间
  float loadRank = 8;
  int32 weight = 9;
}

message NodeSID {
  string sid = 1; // roomid-roundid
  int64 createTs = 2; // 创建时间
}

// 数据结构存储映射
// appId -> node列表
// node -> sid列表(sid可以推出roomId和roundId)
message Nodes {
  string appId = 1;
  repeated Node nodes = 2;
  int64 lastUpdateTs = 6; // 最后更新时间
}

// 游戏侧自己实现http/rpc接口
// 入参: ReportReq
// 出参: ReportRsp
// 有状态路由服务会自己去拉取机器负载
message LoadPullReq {}

message LoadPullRsp {
  NodeLoad load = 1; // 负载信息
}

// 红石配置项
// http://gm.tmeoa.com/?type=stateful_router_config_test
message MonitorCfgItem {
  int32 id = 1; // 配置ID
  string appId = 2; // 小游戏AppID
  string service = 3; // 微服务名
  bool enable = 4; // 是否启用
  int64 updateTs = 5; // 最后一次更新时间
  int64 interval = 6; // 拉取定时器间隔(s)
  string namespace = 7; // namespace(test|production)
  string serviceOwner = 8; // 服务负责人
  string pullLoadAPI = 9; // 配置拉取接口
}
