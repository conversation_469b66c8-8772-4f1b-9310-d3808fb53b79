syntax = "proto3";

package xian_cwsx;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_cwsx/xian_cwsx/pkg/gen/pb/game_cwsx/xian_cwsx/game";

import "pb/device/device.proto";
import "pb/game_cwsx/xian_cwsx/dan/notify/notify.proto";
import "pb/game_cwsx/xian_cwsx/game/achievement.proto";
import "pb/game_cwsx/xian_cwsx/game/ad.proto";
import "pb/game_cwsx/xian_cwsx/game/common.proto";
import "pb/game_cwsx/xian_cwsx/game/game_center.proto";
import "pb/game_cwsx/xian_cwsx/game/minigame.proto";
import "pb/game_cwsx/xian_cwsx/game/opt.proto";
import "pb/game_cwsx/xian_cwsx/game/status.proto";

service Game {
  // --------每次进入游戏调用---------
  // 检查注册游戏
  rpc Register(RegisterReq) returns (RegisterRsp);
  // 老用户检查
  rpc OldUserCheck(OldUserCheckReq) returns (OldUserCheckRsp);
  // // TODO 注销 测试用
  rpc RemoveAccount(RemoveAccountReq) returns (RemoveAccountRsp);
  // TODO 自动补道具
  rpc AutomaticProps(AutomaticPropsReq) returns (AutomaticPropsRsp);
  // TODO 自动跳关
  rpc AutomaticFloor(AutomaticFloorReq) returns (AutomaticFloorRsp);
  // TODO 自动跳关多人
  rpc AutomaticFloorUid(AutomaticFloorUidReq) returns (AutomaticFloorUidRsp);
  // TODO 一键补发/跳关/发奖
  rpc AutomaticSkip(AutomaticSkipReq) returns (AutomaticSkipRsp);
  // TODO 发礼包测试
  rpc AutomaticGift(AutomaticGiftReq) returns (AutomaticGiftRsp);
  // --------游戏状态相关--------
  // 配置
  rpc Config(ConfigReq) returns (ConfigRsp);
  // 关卡配置
  rpc FloorConfig(FloorConfigReq) returns (FloorConfigRsp);
  // 章节配置
  rpc ChapterConfig(ChapterConfigReq) returns (ChapterConfigRsp);
  // 章节明信片补发
  rpc ChapterReissue(ChapterReissueReq) returns (ChapterReissueRsp);
  // 商店配置 需求待定
  //  rpc ShopConfig(ShopConfigReq) returns (ShopConfigRsp);
  // 用户信息
  rpc UserInfo(UserInfoReq) returns (UserInfoRsp);
  // 用户授权信息
  rpc UserAuth(UserAuthReq) returns (UserAuthRsp);
  // 客户端主动查询资产
  rpc LoadAssets(LoadAssetsReq) returns (LoadAssetsRsp);
  // --------新手引导---------
  // 提交新手引导
  rpc SubmitGuide(SubmitGuideReq) returns (SubmitGuideRsp);
  // --------排行榜相关--------
  // 获取排行榜(全国)
  rpc Rank(RankReq) returns (RankRsp);
  // 获取排行榜(好友榜)
  rpc FriendRank(FriendRankReq) returns (FriendRankRsp);
  // 获取排行榜(音符总榜-中台拉取)
  rpc NoteRank(NoteRankReq) returns (NoteRankRsp);
  // 获取排行榜(音符周榜榜-中台拉取)
  rpc NoteWeekRank(NoteWeekRankReq) returns (NoteWeekRankRsp);
  // --------商品购买相关--------
  // 商场记录
  rpc ShopRecord(ShopRecordReq) returns (ShopRecordRsp);
  // 兑换商品--商店(需要策划将兑换体力和兑换消消币,购买体力,星星兑换都配成商品)
  rpc ExItems(ExItemsReq) returns (ExItemsRsp);
  // 礼包购买记录
  rpc GiftRecord(GiftRecordReq) returns (GiftRecordRsp);
  // --------关卡相关--------
  // 进入关卡
  rpc EntryFloor(EntryFloorReq) returns (EntryFloorRsp);
  // 提交一轮结果
  rpc SubmitResult(SubmitResultReq) returns (SubmitResultRsp);
  // 补充提交一轮结果
  rpc SupplementSubmitResult(SupplementSubmitResultReq) returns (SupplementSubmitResultRsp);
  // 连胜检测
  rpc ContinuousCheck(ContinuousCheckReq) returns (ContinuousCheckRsp);
  // 关内曝光
  rpc StageExposure(StageExposureReq) returns (StageExposureRsp);
  // 局内/战前购买
  rpc InlineBuy(InlineBuyReq) returns (InlineBuyRsp);
  // 局内/战前使用道具
  rpc InlineUseItem(InlineUseItemReq) returns (InlineUseItemRsp);
  // 局内/战前使用资产对其
  rpc InlineItemCheck(InlineItemCheckReq) returns (InlineItemCheckRsp);
  // 使用连胜道具通知 -- 仅作上报操作
  rpc UseContinuousItem(UseContinuousItemReq) returns (UseContinuousItemRsp);
  // 关卡状态上报 -- 只做清除连胜
  rpc ReportFloorStatus(ReportFloorStatusReq) returns (ReportFloorStatusRsp);
  // --------活动关卡相关--------
  // 进入关卡
  rpc EntryActivityFloor(EntryActivityFloorReq) returns (EntryActivityFloorRsp);
  // 提交活动关卡结果
  rpc SubmitActivityResult(SubmitActivityResultReq) returns (SubmitActivityResultRsp);
  // 直接跳过活动关卡
  rpc SkipActivityFloor(SkipActivityFloorReq) returns (SkipActivityFloorRsp);
  // ---------广告相关--------
  // 看广告前获得token
  rpc ADBefore(ADBeforeReq) returns (ADBeforeRsp);
  // 看广告领奖 --需要拿token
  rpc AD(ADReq) returns (ADRsp);
  // 查看广告进度
  rpc ADProgress(ADProgressReq) returns (ADProgressRsp);
  // 看广告领奖,中台调用 --需要拿token，不鉴权
  rpc ADReward(ADRewardReq) returns (ADRewardRsp);
  //  // 观看广告领奖
  //  rpc ADViewReward(ADViewRewardReq) returns (ADViewRewardRsp);
  // 结算广告是否可用
  rpc SettleAD(SettleADReq) returns (SettleADRsp);
  // 结算额外广告信息查询
  rpc SettleExtraAD(SettleExtraADReq) returns (SettleExtraADRsp);
  // ---- 收藏相关 ----
  // 获取收藏相关数据
  rpc Favorite(FavoriteReq) returns (FavoriteRsp);
  // 收藏领奖
  rpc FavoriteReward(FavoriteRewardReq) returns (FavoriteRewardRsp);
  // ---- 收藏相关 ----

  // --------体力相关--------
  // 赠送好友体力
  rpc SendHeart(SendHeartReq) returns (SendHeartRsp);
  // 领取好友赠送体力
  rpc DrawHeart(DrawHeartReq) returns (DrawHeartRsp);
  // 客户端主动调用体力刷新
  rpc RefreshHeart(RefreshHeartReq) returns (RefreshHeartRsp);
  // 金币补满体力
  rpc FillUpHeart(FillUpHeartReq) returns (FillUpHeartRsp);
  // 请求好友送体力
  rpc AskHeart(AskHeartReq) returns (AskHeartRsp);
  // 一键请求好友送体力
  rpc AskHeartOneKey(AskHeartOneKeyReq) returns (AskHeartOneKeyRsp);
  // 获取请求送体力好友列表 别人求我
  rpc GetAskList(GetAskListReq) returns (GetAskListRsp);
  // 获取请求送体力好友数 别人求我
  rpc GetAskNum(GetAskNumReq) returns (GetAskNumRsp);
  // 查询好友赠送，请求状态
  rpc GetAskInfo(GetAskInfoReq) returns (GetAskInfoRsp);
  // 获取可请求送体力好友数 我还可以向几个人求
  rpc GetCanAskNum(GetAskNumReq) returns (GetAskNumRsp);
  // --------社交相关--------
  // 好友列表(双关+绑定)
  rpc Friends(FriendsReq) returns (FriendsRsp);
  // 获取可领取体力好友列表
  rpc GetCanReciveList(GetCanReciveListReq) returns (GetCanReciveListRsp);
  // 获取可领取体力次数
  rpc GetCanReciveNum(GetCanReciveNumReq) returns (GetCanReciveNumRsp);
  //  // 关注/取关用户
  //  rpc FollowUser(FollowUserReq) returns (FollowUserRsp);
  //  // 关注/取关状态
  //  rpc FollowState(FollowStateReq) returns (FollowStateRsp);
  // --------章节相关--------
  // 解锁章节
  rpc ChapterUnlock(ChapterUnlockReq) returns (ChapterUnlockRsp);
  // 探索
  rpc ChapterExplore(ChapterExploreReq) returns (ChapterExploreRsp);
  // 一键探索
  rpc ChapterExploreOneKey(ChapterExploreOneKeyReq) returns (ChapterExploreOneKeyRsp);
  // 章节已解锁列表
  rpc ChapterList(ChapterListReq) returns (ChapterListRsp);
  // --------收集任务相关--------
  // 查看
  rpc GetTaskList(GetTaskListReq) returns (GetTaskListRsp);
  //  // 领奖
  //  rpc TaskReward(TaskRewardReq) returns (TaskRewardRsp);
  // --------宝藏相关--------treasure
  // 兑换
  rpc ExchangeTreasure(ExchangeTreasureReq) returns (ExchangeTreasureRsp);
  // 查看列表
  rpc TreasureList(TreasureListReq) returns (TreasureListRsp);
  // 领取奖励
  rpc TreasureReward(TreasureRewardReq) returns (TreasureRewardRsp);
  // 消除new标志,或消除客户端未表现新万能卡
  rpc TreasureClearMark(TreasureClearMarkReq) returns (TreasureClearMarkRsp);
  // 客户端请求激活宝藏系统
  rpc TreasureUnlock(TreasureUnlockReq) returns (TreasureUnlockRsp);
  // --------授权相关--------Authorized
  // 授权
  rpc Authorized(AuthorizedReq) returns (AuthorizedRsp);
  // 绑定
  rpc Binding(BindingReq) returns (BindingRsp);
  // 宠物相关
  rpc PetInfo(PetInfoReq) returns (PetInfoRsp);
  // 生成新的票据id
  rpc BillInfo(BillInfoReq) returns (BillInfoRsp);
  // --------巅峰赛相关--------
  // 查看
  rpc GetPeakInfo(GetPeakInfoReq) returns (GetPeakInfoRsp);
  // 进入巅峰赛关卡
  rpc EntryPeakFloor(EntryPeakFloorReq) returns (EntryPeakFloorRsp);
  // 提交巅峰赛关卡结果
  rpc SubmitPeakResult(SubmitPeakResultReq) returns (SubmitPeakResultRsp);
  // 心跳
  rpc Heartbeat(HeartbeatReq) returns (HeartbeatRsp);
  // -- 签到相关 --
  // 获取签到信息 --每次进页面直接拉,或者明确了签到周期,到点再拉
  rpc CheckinInfo(CheckinInfoReq) returns (CheckinInfoRsp);
  // 签到操作, 领取签到奖励
  rpc Checkin(CheckinReq) returns (CheckinRsp);
  // 领取签到累积奖励
  rpc CheckinProgress(CheckinProgressReq) returns (CheckinProgressRsp);
  // ---战令通行证相关----
  // 获取战令系统信息 --由于红点显示，可以每次回到主界面拉一下刷新，或者到更新时间主动拉
  rpc WarOrderInfo(WarOrderInfoReq) returns (WarOrderInfoRsp);
  // 领取战令系统普通奖励
  rpc WarOrderReward(WarOrderRewardReq) returns (WarOrderRewardRsp);
  // 领取战令系统高级奖励
  rpc WarOrderPayReward(WarOrderPayRewardReq) returns (WarOrderPayRewardRsp);
  // 客户端主动领未领取的上期奖励
  rpc WarOrderPreReward(WarOrderPreRewardReq) returns (WarOrderPreRewardRsp);
  // 领取战令循环奖励
  rpc WarOrderLoopReward(WarOrderLoopRewardReq) returns (WarOrderLoopRewardRsp);
  // 战令查金猪
  rpc WarOrderCoinBox(WarOrderCoinBoxReq) returns (WarOrderCoinBoxRsp);
  // ----中台回调相关-----
  // 中台回调
  rpc AppModule(CustomizeModuleReq) returns (CustomizeModuleRsp);
  // 可过期配置
  rpc ExpireConfig(ExpireConfigReq) returns (ExpireConfigRsp);
  // 查询开关
  rpc Query(QueryReq) returns (QueryRsp); // 查询
  // 批量查开关
  rpc BatchQuery(BatchQueryReq) returns (BatchQueryRsp);
  // 更新开关
  rpc Modify(ModifyReq) returns (ModifyRsp);
  // 老玩家回归领奖
  rpc OldUserReturn(OldUserReturnReq) returns (OldUserReturnRsp);
  // 老玩家回归状态检查
  rpc OldUserReturnCheck(OldUserReturnCheckReq) returns (OldUserReturnCheckRsp);
  // 查询活动状态 --挽回弹窗
  rpc ActivityRetentionPopup(ActivityRetentionPopupReq) returns (ActivityRetentionPopupRsp);
  // 用户超级彩球
  rpc SuperBollCheck(SuperBollCheckReq) returns (SuperBollCheckRsp);
  // 成就领奖
  rpc AckAchievement(AckAchievementReq) returns (AckAchievementRsp);
  // 成就数据
  rpc Achievement(AchievementReq) returns (AchievementRsp);
  // 成就点赞
  rpc AchievementThumbUp(AchievementThumbUpReq) returns (AchievementThumbUpRsp);
  // 任务中心状态查询+领奖
  rpc TaskStatus(TaskStatusReq) returns (TaskStatusRsp);
  // Q音宠物新手引导
  // 查询宠物新手引导进度
  rpc PetNewBieCheck(PetNewBieCheckReq) returns (PetNewBieCheckRsp);
  // 更新宠物新手引导进度
  rpc PetNewBieUpdate(PetNewBieUpdateReq) returns (PetNewBieUpdateRsp);
  // 配置新接口
  rpc ConfigOp(ConfigOpReq) returns (ConfigOpRsp);
  // 查询配置版本号
  rpc ConfigVersion(ConfigVersionReq) returns (ConfigVersionRsp);
  // 配置优化接口
  rpc ConfigOpOptimize(ConfigOpOptimizeReq) returns (ConfigOpOptimizeRsp);
  // 配置优化接口
  rpc ConfigOpOptimizeById(ConfigOpOptimizeByIdReq) returns (ConfigOpOptimizeByIdRsp);
  // 防沉迷检查
  rpc AntiCheck(AntiCheckReq) returns (AntiCheckRsp);
  // -------------------------分享有礼活动-------------------------//
  // 客户端发起分享结束回调 --需要告诉服务器分享结束
  rpc ShareCallback(ShareCallbackReq) returns (ShareCallbackRsp);
  // 拉取玩家当前分享活动进度
  rpc ShareTodayInfo(ShareTodayInfoReq) returns (ShareTodayInfoRsp);
  // 领取每日分享奖励
  rpc ShareTodayReward(ShareTodayRewardReq) returns (ShareTodayRewardRsp);
  // 领取邀请好友进度奖励
  rpc ShareProgressReward(ShareProgressRewardReq) returns (ShareProgressRewardRsp);
  // 推荐
  rpc SuggestCheck(SuggestCheckReq) returns (SuggestCheckRsp);
  // 查询当前装扮系统内容
  rpc DecorateInfo(DecorateInfoReq) returns (DecorateInfoRsp);
  //  设置头像框，称号
  rpc SetDecorate(SetDecorateReq) returns (SetDecorateRsp);
  // 地区总榜
  rpc AreaRank(AreaRankReq) returns (AreaRankRsp);
  // 拉取地区信息
  rpc QueryAreaConfig(QueryConfigReq) returns (QueryConfigRsp);
  rpc CheckPeak(CheckPeakReq) returns (CheckPeakRsp);
  // 领取游戏圈礼包
  rpc AckGameGifts(AckGameGiftsReq) returns (AckGameGiftsRsp);
  // cdk兑换礼包
  rpc CdkGameGifts(CdkGameGiftsReq) returns (CdkGameGiftsRsp);
  // 查询玩家自己的排名
  rpc GetSelfRank(GetSelfRankReq) returns (GetSelfRankRsp);
  // 龙舟补偿
  rpc GetDragonIndemnity(GetDragonIndemnityReq) returns (GetDragonIndemnityRsp);
  // 领取巅峰赛排行奖励
  rpc GetPeakRaceReward(GetPeakRaceRewardReq) returns (GetPeakRaceRewardRsp);
  // 前端热key处理Set。初期放开10个key，超出该内容会造成数据无法生效
  rpc OptSet(OptSetReq) returns (OptSetRsp);
  // 前端热key处理Get
  rpc OptGet(OptGetReq) returns (OptGetRsp);
  // 游戏圈任务
  rpc GameQuanTask(GameQuanTaskReq) returns (GameQuanTaskRsp);
  rpc AckGameQuanTask(AckGameQuanTaskReq) returns (AckGameQuanTaskRsp);
  rpc AckTetrisGame(AckTetrisGameReq) returns (AckTetrisGameRsp);
  rpc UserSVipInfo(UserSVipInfoReq) returns (UserSVipInfoRsp);
  // 步数购买查询
  rpc CheckStepBuy(CheckStepBuyReq) returns (CheckStepBuyRsp);
  // 小游戏接口
  rpc MiniGameReward(MiniGameRewardReq) returns (MiniGameRewardRsp);
}

message Error {
  uint32 code = 1; // 执行结果code码
  string msg = 2; // 错误消息
}

message RegisterReq {}

message RegisterRsp {
  bool is_new = 1; // 是否新用户
}

message OldUserCheckReq {}

message OldUserCheckRsp {
  uint32 old_player_state = 1; // 老用户状态 0,不需要任何操作 1,需要奖励弹窗
}

message RemoveAccountReq {}

message RemoveAccountRsp {}

message AutomaticPropsReq {
  uint32 item_id = 1;
  int64 item_num = 2;
}

message AutomaticPropsRsp {}

message AutomaticFloorReq {
  uint32 floor_id = 1; // 关卡顺序id
}

message FloorData {
  uint64 uid = 1;
  uint32 floor_id = 2;
}

message AutomaticFloorRsp {}

message AutomaticFloorUidReq {
  repeated FloorData floors = 1;
}

message AutomaticFloorUidRsp {}

message AutomaticSkipReq {}

message AutomaticSkipRsp {}

message ConfigReq {
  uint32 version = 1;
}

message ConfigRsp {
  uint32 version = 1;
  string data = 2;
}

message FloorConfigReq {}

message FloorConfigRsp {
  string data = 1; //关卡信息 -- 只会发准备挑战的关卡和当前正在玩的关卡,如果stage_id=0,则表示已经没有可以玩的关
}

message ChapterConfigReq {
  uint32 version = 1;
}

message ChapterConfigRsp {
  uint32 version = 1;
  string data = 2;
}

message ChapterReissueReq {}

message ChapterReissueRsp {
  repeated TreasureCard cards = 1; // 如果发奖产生宝藏奖励这个字段不为空
  repeated uint32 chapter_ids = 2; // 发奖成功的id
  bool reward_finish = 3; // 是否一次性发完了
}

//message ShopConfigReq {}
//
//message ShopConfigRsp {
//  string data = 1;
//}

message UserInfoReq {
  bool is_new_ui = 1; // 是否使用新UI进入游戏
  string access_token = 2; // 透传accessToken
  bool is_v2 = 3; // 是否闯关流程2.0
  bool is_task_center = 4 [deprecated=true]; // 是否是任务中心进入
}

message InfiniteItem {
  uint32 asset_id = 1;
  uint32 expire_time = 2; // 无限道具到期时间
}

message UserAsset {
  int64 assetId = 1; // 资产 id
  int64 assetNum = 2; // 资产数量
  string name = 3; // 资产名称
  string icon = 4; // 资产图标
}

message CollectTask {
  uint32 task_id = 1;
  uint32 progress = 2; // 任务进度
  bool reward_state = 3; // 奖励状态  0表示未完成,1表示已完成
}

message UserInfoRsp {
  bool treasure_status = 1; // true表示已激活
  int32 friend_api_auth = 2; // 好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆
  int32 bing_status = 3; // 绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定
  int32 auth_reward_status = 4; // 授权奖励领取情况 00 所有未领取，1x领取了授权，x1领取了绑定
  int32 pet_id = 5; // 宠物id
  uint32 floor = 6; // 如果当前在关卡内，这个值为关卡id
  uint32 unclaimed_heart = 7; // 待领取爱心 --自动恢复的
  uint32 heart_next_time = 8; // 下次体力恢复时长(秒)
  uint32 unclaimed_send_heart = 9; // 好友赠送未领取次数
  uint32 donate_hearts = 10; // 今日已经领取的好友赠送次数
  uint32 continuous = 11; // 连续闯关次数
  uint32 connect_task_type = 12; // 当前生效的任务类型
  uint32 rank_state_id = 13; // 排行榜显示的关卡id
  uint32 version = 14; // 杂项版本号
  int64 login_order = 15; // 登录次数
  int64 st = 16; // 服务器当前时间戳
  int64 connect_task_begin = 17; // 收集任务开始时间 -- 0表示无活动
  int64 connect_task_end = 18; // 收集任务结束时间 -- 0表示无活动
  PetStatus petStatus = 19; // 宠物状态
  PetLiveStatus petLiveStatus = 20; // 宠物饥饿状态
  PetInteractiveStatus petInteractiveStatus = 21; // 宠物互动操作状态
  repeated uint32 guides = 22; // 新手引导已经操作过的
  repeated UserAsset assets = 23; // 游戏相关资产
  repeated InfiniteItem infinite_items = 24; // 无限战前道具及过期时间
  repeated CollectTask collect_tasks = 25; // 收集任务进度
  repeated AdInfo ads = 26;
  string pet_avatar = 27; // 宠物头像
  string nick_name = 28; // 玩家昵称
  string avatar = 29; // 玩家头像
  string open_id = 30;
  string bill_id = 31; // 使用道具随机id
  string encrypt_uid = 32; // 加密uin
  string floor_config = 33; //关卡信息 -- 只会发准备挑战的关卡,如果stage_id=0,则表示已经没有可以玩的关
  uint32 peak_score = 34; // 当前赛季分数
  uint32 un_send_heart = 35; // 别人向我求体力，我没发的总个数
  uint32 max_heart_conf = 36;
  uint32 team_version = 37; // 战队配置版本
  uint32 win_count = 38; // 胜利次数，获得彩球输一把清0
  bool super_boll = 39; // 彩球活动可见true，不可见false
  bool super_boll_activate = 40; // 彩球活动是否激活了
  map<int64, int64> version_map = 41; // 配置版本号集合
  string restore_billId = 42; // 如果有未结算的关卡，这个表示当时进关时的种子，为""表示没有未结算的
  string ios_version = 43; //ios 提审版本
  string and_version = 44; //and 提审版本
  repeated string ios_id_list = 45; //ios 提审版本屏蔽活动id列表
  repeated string and_id_list = 46; //and 提审版本屏蔽活动id列表
  int32 favorite_status = 47; // 收藏-今日领奖状态, 0-未领奖, 1-已领奖
  int32 ad_left_rewards = 48; //广告分享剩余激励次数
  bytes user_profile = 49; // 玩家游戏内部装饰
  int32 preview_level = 50;
  int32 activate_level = 51;
  int32 dragon_reward_state = 52; // -1 无奖励 0 有奖未领 1 有奖已领
  string dragon_config = 53; // 龙舟补发配置
  uint32 floor_switch_ab = 54; // 前100关ab组,每次登录刷新一次，防止组别改动
  uint32 create_time = 55; //  账号创建时间
  uint32 is_ack_tetris = 56; //是否领取过奖励
  xian_cwsx_dan.DanInfo dan_info = 57; // 段位信息
  int64 svip_end_time = 60; // 超级会员过期时间
  bool continuous_abt = 61; // 连胜abt是否命中
}

message GameUserProfile {
  int64 avatar_frame = 1; // 使用的头像框id 0表示初始默认头像
  int64 avatar_frame_end_ts = 2; // 过期时间
  int64 nickname_title = 3; // 使用的昵称特性id 0表示使用自己头像
  int64 nickname_title_end_ts = 4; // 过期时间
}

// 游戏内玩家设置头像呢称相关
message GameProfile {
  int64 avatar_frame = 1; // 使用的头像框id 0表示初始默认头像
  int64 nickname_title = 2; // 使用的昵称特性id 0表示使用自己头像
}

message UserAuthReq {
  string access_token = 1; // 透传accessToken
}

message UserAuthRsp {
  int32 friend_api_auth = 1; // 好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆
  int32 bing_status = 2; // 绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定
}

message LoadAssetsReq {}

message LoadAssetsRsp {
  repeated UserAsset assets = 1; // 游戏相关资产
  repeated InfiniteItem infinite_items = 2; // 无限战前道具及过期时间
}

// 新手引导
message SubmitGuideReq {
  uint32 guide_id = 1;
  repeated uint32 ach_guide_ids = 2;
}

message SubmitGuideRsp {}

// 消除元素数据
message ElementData {
  uint32 element_id = 1; //元素id
  uint32 element_num = 2; // 元素被消除次数;如果是掉落道具,这个值是掉落个数
}

// 每一次操作造成的消除
message ElementList {
  repeated ElementData element = 1; // 元素列表
}

message EntryFloorReq {
  uint32 floor_id = 1; // 关卡顺序id
  repeated int64 item_ids = 2; // 战前道具(使用配置id),进入游戏服务器只判断是否足够,客户端调用使用后扣除
  string game_seed = 3; // 客户端如果已经有种子，发过来做判断是否同一场
  string schema_fp = 4; // 打开来源,从哪里打开的游戏
  OperatingSystem os = 5; // 操作系统
  int64 ct = 6; // 客户端请求时间
}

message EntryFloorRsp {
  string game_seed = 1; // 种子
  repeated int64 hard_info = 2; // 难度配置[]表示无事发生
  repeated int64 hard_info_new = 3; // 难度新配置[]表示无事发生
  bool cost_heart = 4; // 是否扣了普通体力
  bool step_abt = 5; // 是否触发掉落策略控制AB
  string algorithmInfo = 6; // 上报用, 给到前端, 前端上报到 algorithm_id 字段
  string traceId = 7; // 上报用, 给到前端, 前端上报到trace_id字段
  int64 fail_count = 8; // 连败次数
  int64 fail_step = 9; // 连败次数对应额外步数
}

// 掉落的道具集合 --金币掉落，收集物收集都在这个结构
message ItemOrder {
  uint32 item_id = 1;
  uint32 num = 2;
}

message SubmitResultReq {
  string game_seed = 1; // 游戏种子
  repeated ItemOrder order_list = 2; // 客户端只需要传真实操作掉落的数量，最终结果服务器计算
  bool is_quit = 3; // 是否直接退出 -- 此值为true不再判断参数2,失败挑战也请直接发true
  uint32 used_step = 4; // 用了多少步
  repeated ItemOrder exchange_order_list = 5; // 剩余步数转换的道具
  repeated ItemOrder cleared_order_list = 6; // 游戏中消除操作转换的道具
  repeated ItemOrder pay_item = 7; // 付费道具使用情况
  uint64 lighting_rush_score = 8; // lighting_rush挑战分数
  uint64 star_score = 9; // 明星分数
  bytes star_arg = 10; // 明星字节
  int32 super_ball = 11; //玩家触发超级彩球效果次数
  int32 item2_cnt = 12; //组合消除的次数
  bool is_interrupt = 13; // 是否中途有中断/关卡还原进入
  uint32 max_clear_score = 14; // 最大连消分数-客户端透传
  int32 dan_score = 15; // 段位赛积分
}

message CollectTaskChange {
  uint32 task_id = 1;
  uint32 progress_add = 2; // 进度增长
}

message FloorReward {
  uint32 item_id = 1;
  uint32 num = 2;
  int64 add_time = 3; // 如果为buff道具，这里是增加的时间,=0表示是普通道具 客户端计算 num * add_time
}

message SubmitResultRsp {
  bool is_double = 1; // 是否使用了双倍buff
  repeated CollectTaskChange change = 2; // 收集任务变化
  repeated TreasureCard cards = 3; // 如果发奖产生宝藏奖励这个字段不为空
  repeated FloorReward floor_rewards = 4; // 关卡发的奖 -- 包含局内获得的金币
  repeated FloorReward task_rewards = 5; // 收集任务发的奖
  uint32 continuous = 6; // 连续闯关次数
  bool need_return_heart = 7; // 是否需要返还进入的体力
  string floor_config = 8; //关卡信息 -- 只会发准备挑战的关卡,如果stage_id=0,则表示已经没有可以玩的关
  uint32 version = 9; // 配置版本号
  map<int64, int64> version_map = 10; // 配置版本号集合
  bool is_first = 11; // 是否首次闯关胜利
  repeated ItemOrder plant_item = 12; // 获得植物
}

message SupplementSubmitResultReq {
  string game_seed = 1; // 游戏种子
  repeated ItemOrder order_list = 2; // 客户端只需要传真实操作掉落的数量，最终结果服务器计算
  int64 lighting_rush_score = 3; // lighting_rush挑战分数去掉第一次提交的分数
  uint64 star_score = 4; // 明星分数
  bytes star_arg = 5; // 明星字节
  int32 dan_score = 6; // 段位赛积分
}

message SupplementSubmitResultRsp {
  bool is_double = 1; // 是否使用了双倍buff
  repeated CollectTaskChange change = 2; // 收集任务变化
  repeated TreasureCard cards = 3; // 如果发奖产生宝藏奖励这个字段不为空
  repeated FloorReward task_rewards = 4; // 收集任务发的奖
}

message EntryActivityFloorReq {
  uint32 floor_id = 1; // 关卡顺序id
  string schema_fp = 2; // 打开来源,从哪里打开的游戏
}

message EntryActivityFloorRsp {
  uint32 entry_time = 1; // 进入时间-时间戳(s)
  repeated int64 hard_info = 2; // 难度配置[]表示无事发生
  repeated int64 hard_info_new = 3; // 难度新配置[]表示无事发生
  bool cost_heart = 4; // 是否扣了普通体力
}

message SubmitActivityResultReq {
  repeated ItemOrder order_list = 1;
  bool is_quit = 2; // 是否直接退出或失败 -- 此值为true不再判断参数1
  uint32 used_step = 3; // 用了多少步
  repeated ItemOrder cleared_order_list = 4; // 游戏中消除操作转换的道具
  uint64 lighting_rush_score = 5; // lighting_rush挑战分数
  uint64 star_score = 6; // 明星分数
  bytes star_arg = 7; // 明星字节
  bool is_interrupt = 8; // 是否中途有中断/关卡还原进入
  uint32 max_clear_score = 9; // 最大连消分数-客户端透传
}

message SubmitActivityResultRsp {
  Error err = 1; // 错误信息
  repeated TreasureCard cards = 2; // 如果发奖产生宝藏奖励这个字段不为空
  repeated FloorReward floor_rewards = 3; // 关卡发的奖 -- 包含局内获得的金币
  string floor_config = 4; //关卡信息 -- 只会发准备挑战的关卡,如果stage_id=0,则表示已经没有可以玩的关
  uint32 version = 5; // 配置版本号
  map<int64, int64> version_map = 6; // 配置版本号集合
}

message SkipActivityFloorReq {
  uint32 floor_id = 1; // 关卡id
}

message SkipActivityFloorRsp {
  string floor_config = 1; //关卡信息 -- 只会发准备挑战的关卡,如果stage_id=0,则表示已经没有可以玩的关
  uint32 version = 2; // 配置版本号
  map<int64, int64> version_map = 3; // 配置版本号集合
}

message ContinuousCheckReq {
  bool entry_fail = 1; // 客户端只要传上次是否进关失败就行,只要服务器判断和当前有未结算的就不操作连胜清除
  bool submit_fail = 2; // 客户端只要传上次是否结算失败就行,只要服务器判断和当前有未结算的就不操作连胜清除
  bool is_exposure = 3; // 局内是否已正常曝光
  bool is_success = 4; // 局内是否已经胜利结算/失败不传
  int64 ct = 5; // 客户端请求时间
}

message ContinuousCheckRsp {
  uint32 continuous = 1; // 连续闯关次数
  bool need_heart = 2; // 进关是否需要扣体力，优先判断是否有无限体力
  uint32 super_num = 3; // 超级彩球数量
  int64 heart_num = 4; // 体力
}

message StageExposureReq {
  string game_seed = 1; // 曝光种子-和进关一致 -- 如果客户端发的和玩家真实用的不一致，以玩家正在用的记录
}

message StageExposureRsp {
  int32 heart_num = 1; // 当前体力
}

message RefreshHeartReq {}

message RefreshHeartRsp {
  int32 heart = 1; // 体力总值
  int64 heart_next_time = 2; // 下次体力恢复时间
  int32 ts = 3; // 服务器当前时间
  InfiniteItem infinite_heart = 4; // 无限体力
  uint32 max_heart = 5; // 可恢复到的体力最大值
  uint32 max_recv_heart = 6; // 可领取到的体力最大值
}

message FillUpHeartReq {
  string bill_id = 1; // 票据随机id
}

message FillUpHeartRsp {
  int32 heart = 1; // 体力恢复到的值，最大值
  repeated UserAsset assets = 2; // 游戏相关资产最新列表
}

message AskHeartReq {
  string friend_id = 1;
}

message AskHeartRsp {}

message AskHeartOneKeyReq {}

message AskHeartOneKeyRsp {}

message RankReq {
  int32 passback = 1; // 首次不传, 服务器返回什么, 传什么
}

// 榜单成员信息
message RankItem {
  int32 concerned = 1; // 是否关注 1:展示关注icon 2:展示微信 3:展示qq
  int64 friendPetId = 2; // 好友的宠物id
  uint64 level = 3; // 关卡
  string open_id = 4; // 用户id
  string encrypt_uid = 5; // 加密uin
  string nickname = 6; // 用户昵称
  string avatar = 7; // 用户头像
  string friendPetCover = 8; // 宠物头像
  int64 fresh_time = 9; // 玩家数据刷新时间 --打巅峰赛的时候这个值是巅峰赛分数刷新时间
  uint32 score = 10; // 巅峰赛积分
  string team_name = 11; // 战队名
  bytes profile = 12; // 玩家游戏内部装饰
  xian_cwsx_dan.DanInfo dan_info = 13; // 玩家段位
}

message RankRsp {
  repeated RankItem list = 1; // 排行榜列表
  bool has_next = 2; // 是否还有下一页
  int32 passback = 3; //  再次翻页的时候需要把这个东西传过来
  int32 rank = 4;
}

message FriendRankReq {
  int32 passback = 1; // 首次不传, 服务器返回什么, 传什么
  string access_token = 2; // 透传accessToken
}

message FriendRankRsp {
  repeated RankItem list = 1; // 排行榜列表
  bool has_next = 2; // 是否还有下一页
  int32 passback = 3; //  再次翻页的时候需要把这个东西传过来
  int32 rank = 4;
}

enum PetStatus {
  PetNotAdopt = 0; // 尚未被领取, 所有宠物的初始状态
  PetAdopted = 1; // 已领取孵化中, 用户点击收下宠物蛋后转这个状态
  PetHatched = 2; // 已孵化成功, 成功孵化后转这个状态
  PetIdle = 3; // 在小窝, 用户点击领养宠物/出门回家被查看后转这个状态
  PetOutWaiting = 4; // 点击出门之后, 宠物处于出门等待中状态
  PetOutting = 5; // 出门中, 宠物匹配到后转这个状态
  PetBack = 6; // 宠物刚刚回家
  PetDying = 7; // 濒临死亡状态
  PetRescuing = 8; // 听歌抢救状态
}

enum PetLiveStatus {
  PetLSNormal = 0; // 正常状态
  PetLSHungry = 1; // 饥饿状态
}

enum PetInteractiveStatus {
  PetDefault = 0;
  PetReceivingNote = 1; // 收音符
  PetHunger = 2; // 饿了
  PetDirty = 3; // 脏了
  PetUnHappy = 4; // 不开心
  PetSick = 5; // 生病
}

message CwGameRankInfo {
  int64 friendPetId = 1; // 好友的宠物id
  string friendPetCover = 2; // 宠物头像
  PetStatus friendPetStatus = 3; // 宠物状态
  PetLiveStatus friendPetLiveStatus = 4; // 宠物饥饿状态
  PetInteractiveStatus petInteractiveStatus = 5; // 宠物互动操作状态
  int32 concerned = 6; // 是否关注 1:展示关注icon 2:展示微信 3:展示qq
}

message NoteRankItem {
  string open_id = 1; // 用户id
  string encrypt_uid = 2; // 加密uin
  uint64 notes = 3; // 音符
  string nickname = 4; // 用户昵称
  string avatar = 5; // 用户头像
  CwGameRankInfo cwGameRankInfo = 6; // cw游戏信息
  int32 extraNote = 7; // 是否有额外音符可收
  uint32 vip = 8; // 0 非vip,1 vip 其他:后续扩展
  bytes profile = 9; // 玩家游戏内部装饰
  xian_cwsx_dan.DanInfo dan_info = 10; // 段位信息
}

message SelfRankItem {
  int32 rank = 1; // 用户排名
  uint64 notes = 2; // 音符
  string nickname = 3; // 用户昵称
  string avatar = 4; // 用户头像
  string friendPetCover = 5; // 宠物头像
  xian_cwsx_dan.DanInfo dan_info = 6; // 段位信息
}

message NoteRankReq {
  int32 passback = 1; // 首次不传, 服务器返回什么, 传什么
}

message NoteRankRsp {
  repeated NoteRankItem list = 1; // 排行榜列表
  bool has_next = 2; // 是否还有下一页
  int32 passback = 3; //  再次翻页的时候需要把这个东西传过来
  SelfRankItem self_rank = 4;
}

message NoteWeekRankReq {
  int32 passback = 1; // 首次不传, 服务器返回什么, 传什么
}

message NoteWeekRankRsp {
  repeated NoteRankItem list = 1; // 排行榜列表
  bool has_next = 2; // 是否还有下一页
  int32 passback = 3; //  再次翻页的时候需要把这个东西传过来
  SelfRankItem self_rank = 4;
}

message ShopRecordReq {}

message ShopBuy {
  uint32 shop_id = 1;
  uint32 buy_num = 2; // 今日已经购买的次数
}

message GiftRecordReq {}

message GiftRecord {
  uint32 gift_id = 1;
  uint32 buy_num = 2; // 已经购买的次数
}

message GiftRecordRsp {
  repeated GiftRecord records = 1; // 已经购买的次数
}

message ShopRecordRsp {
  repeated ShopBuy shops = 1; // 今日已经购买过的商品
}

message ExItemsReq {
  // 需要兑换的商品id
  uint32 shop_id = 1;
  string bill_id = 2; // 票据随机id
}

message ExItemsRsp {
  repeated TreasureCard cards = 1; // 如果发奖产生宝藏奖励这个字段不为空
  repeated UserAsset assets = 2; // 游戏相关资产最新列表
  Error err = 3; // 错误信息
}

message SendHeartReq {
  string friend_id = 1;
  string owner_id = 2; // 发送方的openId -- 只对中台调用游戏时用
  string access_token = 3; // 透传accessToken
}

message SendHeartRsp {
  uint32 hearts = 1; // 本次赠送的体力数量
}

message InlineBuyReq {
  uint32 shop_id = 1; // 道具id(购买需要传的是配置的shop_id)
  string bill_id = 2; // 使用唯一id
}

message InlineBuyRsp {
  repeated UserAsset assets = 1; // 游戏相关资产最新列表
}

// 道具使用返回后才可使用下一个道具
message InlineUseItemReq {
  uint32 item_id = 1; //发的是道具表id
  string bill_id = 2; // 使用唯一id
}

message InlineUseItemRsp {
  repeated UserAsset assets = 1; // 游戏相关资产最新列表
}

message InlineItemCheckReq {
  repeated ItemOrder pay_item = 1; // 付费道具使用情况
}

message InlineItemCheckRsp {
  repeated UserAsset assets = 1; // 游戏相关资产最新列表
}

message UseContinuousItemReq {
  repeated uint32 item_id = 1; //发的是道具表id
}

message UseContinuousItemRsp {}

message ReportFloorStatusReq {
  uint32 status = 1; // 0关卡外，1关卡内 0需要清除连胜
}

message ReportFloorStatusRsp {
  uint32 continuous = 1; // 连续闯关次数
}

message FavoriteReq {}

message FavoriteRsp {
  int32 status = 1; // 今日是否已领奖， 0-未领奖; 1-已领奖
  repeated Reward rs = 2; // 收藏可得奖励列表
  int32 floor_max = 3; // 入口展示关卡上限
}

message FavoriteRewardReq {
  int32 entry_type = 1; // 0-正常, 1-WX收藏夹
}

message FavoriteRewardRsp {}

message DrawHeartReq {
  string friend_id = 1; //  好友openid
}

message DrawHeartRsp {
  uint32 hearts = 1; // 本次领取的爱心数量
}

message FriendsReq {
  string open_id = 1; // openId -- 只对中台调用游戏时用
  int32 passback = 2; // 首次不传, 服务器返回什么, 传什么
  string access_token = 3; // 透传accessToken
}

message Friend {
  string open_id = 1; // openid
  string name = 2; // 昵称
  string avatar = 3; // 头像
  int32 level = 4; // 等级
  int64 update_time = 5; // 最后更新时间
  int32 vit_status = 6; // 体力赠送收取状态: // 体力赠送收取状态:0-无事发生，xxxx1-已赠送好友体力，xxx1x-已收到好友送的体力，xx11x-已收到好友赠送并收取，x1xxx-已向好友请求送体力，1xxxx-已收到好友的求体力请求
  string jump_uid = 7; // 跳转uin --为了兼容Q音，这里统一转成string,k歌为raw_uid,Q音为encrypt_uid
  uint32 vip = 8; // 0 非vip,1 vip 其他:后续扩展
  bytes profile = 9; // 玩家游戏内部装饰
  xian_cwsx_dan.DanInfo dan_info = 10; // 玩家段位赛信息
}

message FriendsRsp {
  repeated Friend friends = 1;
  bool has_next = 2; // 是否还有下一页
  int32 passback = 3; //  再次翻页的时候需要把这个东西传过来
  int32 page_limit = 4; // 每页最大人数
}

message GetCanReciveListReq {}

message FriendBase {
  string uid = 1;
  string name = 2; // 昵称
  string avatar = 3; // 头像
  int32 vit_status = 4; // 体力赠送收取状态:2进制,0-无事发生，xxxx1-已赠送好友体力，xxx1x-已收到好友送的体力，xx11x-已收到好友赠送并收取，x1xxx-已向好友请求送体力，1xxxx-已收到好友的求体力请求
  bytes profile = 5; // 玩家游戏内部装饰
  xian_cwsx_dan.DanInfo dan_info = 6; // 玩家段位信息
}

message GetCanReciveListRsp {
  repeated FriendBase friends = 1;
}

message GetCanReciveNumReq {}

message GetCanReciveNumRsp {
  uint32 num = 1;
}

message GetAskListReq {
  uint32 passback = 1;
}

message GetAskListRsp {
  repeated FriendBase friends = 1;
  uint32 passback = 2;
  bool has_next = 3;
}

message GetAskNumReq {}

message GetAskNumRsp {
  uint32 num = 1;
}

message GetAskInfoReq {
  string friend_id = 1; // 查谁
}

message GetAskInfoRsp {
  string name = 1; // 昵称
  string avatar = 2; // 头像
  int32 vit_status = 3; // 体力赠送收取状态:2进制,0-无事发生，xxxx1-已赠送好友体力，xxx1x-已收到好友送的体力，xx11x-已收到好友赠送并收取，x1xxx-已向好友请求送体力，1xxxx-已收到好友的求体力请求
}

// 好友关注相关
enum FollowOptType {
  FOLLOW_TYPE_NONE = 0;
  FOLLOW_TYPE_ADD = 1;
  FOLLOW_TYPE_SUB = 2;
}

enum FollowResult {
  FOLLOW_TYPE_SUCC = 0;
  FOLLOW_TYPE_FAIL = 1;
}

// 关注/取关用户 req
message FollowUserReq {
  // optType
  FollowOptType type = 1;
  // open id 列表
  repeated string open_id_list = 2;
}

// 关注/取关用户 rsp
message FollowUserRsp {
  //key:openid
  map<string, FollowInfo> results = 1;
}

message FollowInfo {
  FollowResult result = 1;
  string open_id = 2;
}

// 关注/取关状态 req
message FollowStateReq {
  repeated string vecOpenIds = 1;
}

// 关注/取关状态 rsp
message FollowStateRsp {
  map<string, bool> ret = 1;
}

message FriendIdList {
  repeated string friend_ids = 1;
}

message ChapterUnlockReq {
  uint32 chapter_id = 1;
}

message ChapterUnlockRsp {}

message ChapterExploreReq {
  uint32 chapter_id = 1;
  uint32 component_id = 2; // 场景id
}

message ChapterExploreRsp {
  repeated TreasureCard cards = 1; // 如果发奖产生宝藏奖励这个字段不为空
  bool is_unlock = 2; // 下一章是否自动解锁，存在已经是最后一章的问题
  bool is_finish = 3; // 本章是否完成领奖
}

message ChapterExploreOneKeyReq {
  uint32 chapter_id = 1;
  repeated uint32 component_ids = 2; // 探险的场景id列表
}

message ChapterExploreOneKeyRsp {
  repeated TreasureCard cards = 1; // 如果发奖产生宝藏奖励这个字段不为空
  bool is_unlock = 2; // 下一章是否自动解锁，存在已经是最后一章的问题
  bool is_finish = 3; // 本章是否完成领奖
}

message ChapterListReq {}

message ChapterInfo {
  uint32 chapter_id = 1;
  repeated uint32 component_ids = 2; // 已解锁建筑id
  bool reward_state = 3; // 领奖状态 0表示未完成,1表示已发奖
  bool post_state = 4; // 明信片领奖状态 0表示未完成,1表示已发奖 -- K歌无视此字段
}

message ChapterListRsp {
  repeated ChapterInfo list = 1; // 已解锁列表
  int64 chapter_num = 2; // 配置的章节数量
}

message GetTaskListReq {}

message GetTaskListRsp {
  int64 connect_task_begin = 1; // 收集任务开始时间 -- 0表示无活动
  int64 connect_task_end = 2; // 收集任务结束时间 -- 0表示无活动
  uint32 connect_task_type = 3; // 当前生效的任务类型
  repeated CollectTask task_list = 4;
}

//message TaskRewardReq {
//  uint32 task_id = 1;
//}

//message TaskRewardRsp {
//  repeated TreasureCard cards = 1; // 如果发奖产生宝藏奖励这个字段不为空
//}

message ExchangeTreasureReq {
  uint32 treasure_card_id = 1; // 准备兑换的宝藏卡id -- 已经获得的卡禁止再次兑换
}

message ExchangeTreasureRsp {}

message TreasureListReq {}

message Treasure {
  uint32 treasure_id = 1; // 宝藏id
  repeated Information informations = 2; // 宝藏已经激活的信息id
  bool reward_state = 3; // 是否领奖
}

// 宝藏信息
message Information {
  uint32 information_id = 1; // 宝藏已经激活的信息id
  bool is_new = 2; // 是否新获得
}

message TreasureListRsp {
  uint32 flag_num = 1; // 当前碎片数量
  uint32 universal_card = 2; // 客户端未播放的万能卡数量 -- 需要客户端请求消除标志
  repeated Treasure treasure_list = 3; // 所有已经有激活信息的宝藏
  int64 treasure_num = 4; // 配置的宝藏数量
  int64 treasure_card_num = 5; // 配置的宝藏卡数量
}

message TreasureRewardReq {
  uint32 treasure_id = 1; // 宝藏id
}

message TreasureRewardRsp {}

message TreasureCardReward {
  uint32 frag_num = 1; // 最终的碎片数量
  uint32 universal_card = 2; // 本次自动兑换的万能卡
  repeated TreasureCard cards = 3; // 本次发奖产生宝藏奖励
}

message ClearTreasureLogo {
  uint32 treasure_id = 1; // 宝藏id
  repeated uint32 information_id = 2; // 需要消除的信息id
}

// 清除卡牌new标志，或者表现新获得卡(万能卡)
message TreasureClearMarkReq {
  repeated ClearTreasureLogo treasure_list = 1; // 待消除的new标志
  bool clear_universal_card = 2; // 是否清除万能卡表现
}

message TreasureClearMarkRsp {}

message TreasureUnlockReq {}

message TreasureUnlockRsp {
  repeated TreasureCard cards = 1; //宝藏卡奖励
}

message AuthorizedReq {
  string access_token = 1; // 透传accessToken
}

message AuthorizedRsp {
  repeated TreasureCard cards = 1; // 如果发奖产生宝藏奖励这个字段不为空
  int32 friend_api_auth = 2; // 好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆
  int32 bing_status = 3; // 绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定
}

message BindingReq {
  string access_token = 1; // 透传accessToken
}

message BindingRsp {
  repeated TreasureCard cards = 1; // 如果发奖产生宝藏奖励这个字段不为空
  int32 friend_api_auth = 2; // 好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆
  int32 bing_status = 3; // 绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定
}

message PetInfoReq {
  string friend_open_id = 1; // 传空查自己
}

message PetInfoRsp {
  int32 pet_id = 1; // 宠物id
  string pet_avatar = 2; // 宠物头像
  PetStatus petStatus = 3; // 宠物状态
  PetLiveStatus petLiveStatus = 4; // 宠物饥饿状态
  PetInteractiveStatus petInteractiveStatus = 5; // 宠物互动操作状态
}

message BillInfoReq {
  uint32 shop_id = 1; // 不发或者发0表示拉的进关种子
}

message BillInfoRsp {
  string bill_id = 1; // 票据随机id
}

message CheckinInfoReq {}

message CheckinReward {
  int32 days = 1; // 第几天, 1、2、3、4、5、6、7
  int32 ad_id = 2; // 对应的广告id
  repeated Reward rewards = 3; // 奖励列表
  repeated Reward extra_rewards = 4; // 额外奖励列表 -- 有这个字段表示有额外奖励
  bool get = 5; // 是否已经领取了, 为ture就表示已经领取了, 没有这个字段就是还没领取
}

message CheckinInfoRsp {
  repeated CheckinReward normal_rewards = 1; // 普通奖励
  repeated CheckinReward progress_rewards = 2; // 进度奖励
  int32 progress = 3; // 当前进度, 本次大循环内已经签到了多少天了
  bool today = 4; // 今天是否签到了, true就是签到了, 没有这个字段就是没签到
  uint32 st = 5; // 当前服务器10位时间戳
  uint32 today_end_st = 6; // 今天结束时间戳, 既可以当本次签到剩余时间(还没签到), 又可以当下次签到剩余时间(已经签到)
  uint32 end_ts = 7; // 本轮签到结束的10位时间戳
  string title = 8; // 签到文案
  bool today_double = 9; // 今天是否广告双倍签到了, true就是签到了, 没有这个字段就是没签到
  bool is_bundle = 10; // 是否礼包发奖
}

message CheckinReq {}

message CheckinRsp {
  repeated Reward rewards = 1; // 签到奖励
}

message CheckinProgressReq {}

message CheckinProgressRsp {
  repeated Reward rewards = 1; // 签到进度奖励
}

// 差异化
message DiffItemOrder {
  uint32 item_id = 1;
  int64 num = 2;
}

message MsgChat {
  int64 ct = 1; // 创建时间
  uint32 mt = 2; // 私信类型：msg_type
  string from_uid = 3; // 来源UID
  string to_uid = 4; // 接收UID
  string arg = 5; // 参数
}

// HeartbeatReq 心跳参数req
message HeartbeatReq {
  map<string, string> report_data = 1; // 直接透传落库
  string platform = 2; // 平台字段，kg、qqmusic，给游戏做差异化逻辑用的
}

// HeartbeatReq 心跳参数rsp
message HeartbeatRsp {}

// ----巅峰赛相关---
// 巅峰赛打关
message EntryPeakFloorReq {
  uint32 season_id = 1; // 玩的哪个赛季 -- 因为赛季是自动切换的，这里需要知道客户端认为的
  uint32 round = 2; // 玩的哪一轮次 -- 因为赛季是自动切换的，这里需要知道客户端认为的
  uint32 peak_floor_id = 3; // 玩的巅峰赛哪一关
  repeated int64 item_ids = 4; // 战前道具(使用配置id),进入游戏服务器只判断是否足够,客户端调用使用后扣除
  string game_seed = 5; // 新进关必须发新种子
  string schema_fp = 6; // 打开来源,从哪里打开的游戏
  int64 ct = 7; // 客户端请求时间
}

message EntryPeakFloorRsp {
  string game_seed = 1; // 种子
  bool cost_heart = 2; // 是否扣了普通体力
  int64 fail_count = 3; // 连败次数
  int64 fail_step = 4; // 连败次数对应额外步数
}

message SubmitPeakResultReq {
  string game_seed = 1; // 游戏种子
  repeated ItemOrder order_list = 2; // 客户端只需要传真实操作掉落的数量，最终结果服务器计算
  bool is_quit = 3; // 是否直接退出 -- 此值为true不再判断参数2,失败挑战也请直接发true
  uint32 used_step = 4; // 用了多少步
  repeated ItemOrder exchange_order_list = 5; // 剩余步数转换的道具
  repeated ItemOrder pay_item = 6; // 付费道具使用情况
  uint64 lighting_rush_score = 7; // lighting_rush挑战分数
  uint64 star_score = 8; // 明星分数
  bytes star_arg = 9; // 明星字节
  int32 super_ball = 10; //玩家触发超级彩球效果次数
  int32 item2_cnt = 11; //组合消除的次数
  bool is_interrupt = 12; // 是否中途有中断/关卡还原进入
  uint32 max_clear_score = 13; // 最大连消分数-客户端透传
  int32 dan_score = 14; // 段位赛积分
}

message SubmitPeakResultRsp {
  bool is_double = 1; // 是否使用了双倍buff
  repeated CollectTaskChange change = 2; // 收集任务变化
  repeated TreasureCard cards = 3; // 如果发奖产生宝藏奖励这个字段不为空
  repeated FloorReward floor_rewards = 4; // 关卡发的奖 -- 包含局内获得的金币
  repeated FloorReward task_rewards = 5; // 收集任务发的奖
  uint32 continuous = 6; // 连续闯关次数
  bool need_return_heart = 7; // 是否需要返还进入的体力
  string floor_config = 8; //关卡信息 -- 只会发准备挑战的关卡,如果stage_id=0,则表示已经没有可以玩的关
  uint32 version = 9; // 配置版本号
  map<int64, int64> version_map = 10; // 配置版本号集合
  int32 got_cups = 11; // 本关获得的奖杯数
  int32 total_cups = 12; // 本赛季总奖杯数(加上本关获得)
  repeated Reward pt_rewards = 13; // 平台特有奖励包
  repeated ItemOrder plant_item = 14; // 获得植物
}

message GetPeakInfoReq {}

message GetPeakInfoRsp {
  int64 peak_begin = 1; // 本赛季开始时间 -- 0表示未开启
  int64 peak_end = 2; //  本赛季结束时间 -- 0表示未开启
  uint32 peak_season = 4; // 当前赛季
  uint32 round = 5; // 玩家的当前轮次
  uint32 score = 6; // 玩家的当前赛季总分数
}

message OldUserReturnReq {}

message OldUserReturnRsp {
  repeated Reward rewards = 1; // 领奖成功,此列表表示奖励列表,领奖失败会直接报错
  repeated TreasureCard cards = 2; // 如果发奖产生宝藏奖励这个字段不为空
}

message OldUserReturnCheckReq {}

message OldUserReturnCheckRsp {
  bool has_rewards = 1; // 是否有可领取的奖励
  repeated Reward rewards = 2; // 上值为true,此列表表示奖励列表
}

enum GameActivity {
  Pinata = 0; // 小马活动
  KingsCup = 1; // 排行榜活动
  LavaVolvano = 2; // 熔岩火山活动
  WarOrder = 100; // 战队
  SuperBoll = 101; // 超级彩球
  GameActivity_Fishing = 201; // 小猫钓鱼
}

enum GameActivityType {
  Unknow = 0; //  未知
  Schedule = 1; // 进度
  Collect = 2; // 收集
}

enum GameActivityStatus {
  Default = 0; // 默认 未参加
  Ongoing = 1; // 参加进行中
  UnClaimed = 2; // 待领取
}

enum GameRewardItemType {
  GameReward_From_GameNormal = 0; // 游戏物品-默认
  GameReward_From_Platform = 1; // 平台物品
  GameReward_From_GameLimitedTime = 2; // 游戏物品-限时
}

// 游戏自己调用
message ActivityRetentionPopupReq {}

message RetentionPopup {
  GameActivity activityId = 1; // GameActivity 活动id
  GameActivityType activityType = 2; // GameActivityType 活动类型
  GameActivityStatus activityStatus = 3; // GameActivityStatus 活动状态
  uint32 currentSchedule = 4; // 当前进度值
  uint32 nextSchedule = 5; // 下一阶段值
  uint32 lostGoodNums = 6; // 损失物品数量
  repeated PopRewardItem NextStageRewards = 7; // 可获得奖励
  string activityName = 8; // 活动名
  map<string, string> mapExt = 15;
}

message RetentionPopupList {
  repeated RetentionPopup list = 1;
}

message ActivityRetentionPopupRsp {
  map<uint32, RetentionPopupList> pop_up_list = 1;
}

// 中台提供 --批量查
message RetentionPopupReq {
  string appId = 1;
  string openId = 2;
  uint32 stageType = 3; // 关卡类型
  bool doubleBuff = 4; // 双倍buff
}

message PopRewardItem {
  uint32 id = 1; // 奖励id
  uint32 num = 2; // 奖励数量 限时道具表示分钟数
  string name = 3; // 奖励名称
  GameRewardItemType type = 4; // 资产类型
}

message RetentionPopupRsp {
  GameActivityType activityType = 1; // GameActivityType 活动类型
  GameActivityStatus activityStatus = 2; // GameActivityStatus 活动状态
  uint32 currentSchedule = 3; // 当前进度值
  uint32 nextSchedule = 4; // 下一阶段值
  uint32 lostGoodNums = 5; // 损失物品数量
  repeated PopRewardItem NextStageRewards = 6; // 下阶段可获得奖励
  map<string, string> mapExt = 15;
}

// FloorReportReq 关卡静态数据上报req
//message FloorReportReq {
//  uint32 floor_id = 1; // 关卡顺序id
//  string cos_url = 2; // 操作数据记录
//}
//
// FloorReportRsp 关卡静态数据上报rsp
//message FloorReportRsp {}
//
// FloorListReq 关卡列表req
//message FloorListReq {
//  uint64 page = 1; // 页码
//  string uid = 2; // 用户ID
//  int64 pass_begin = 3; // 闯关耗时开始区间
//  int64 pass_end = 4; // 闯关耗时结束区间
//  int64 floor_begin = 5; // 关卡开始区间
//  int64 floor_end = 6; // 关卡结束区间
//  int64 submit_begin = 7; // 通关开始区间
//  int64 submit_end = 8; // 通关结束区间
//}
//
// FloorReport 关卡数据对象
//message FloorReport {
//  string uid = 1; // 用户ID
//  uint32 floor_id = 2; // 关卡顺序id
//  int64 submit_time = 3; // 结算时间
//  int64 entry_time = 4; // 关卡进入时间
//  int64 play_time = 5; // 闯关消耗时间
//  int64 fail_cnt = 6; // 失败次数
//  int64 no_report = 7; // 未收到上报次数
//  string cos_url = 8; // 操作数据记录
//}
//
// FloorListRsp 关卡列表rsp
//message FloorListRsp {
//  repeated FloorReport report_list = 1; // 关卡上报数据列表
//}

enum OperatingSystem {
  OperatingSystemUnknown = 0;
  OperatingSystemAndroid = 1;
  OperatingSystemIOS = 2;
}

message WarOrderInfoReq {
  OperatingSystem os = 1;
}

//message WarOrderReward {
//  repeated Reward rewards = 1; // 普通奖励列表
//  repeated Reward box_rewards = 2; // 宝箱奖励列表
//}

message WarOrderLoop {
  uint32 progress = 1; // 当前进度 --如果是满的，积累一个奖励，进度变成0
  uint32 progress_conf = 2; // 配置的进度
  uint32 num = 3; // 积攒的未领取数量
  repeated Reward rewards = 4; // 奖励列表
  uint32 total_num = 5; // 已领取次数
}

message WarOrderInfo {
  uint32 progress_id = 1; // 通行证进度id
  uint32 progress = 2; // 进度
  uint32 progress_conf = 3; // 配置的进度
  bool reward_state = 4; // 普通奖励领奖状态
  bool pay_reward_state = 5; // 付费奖励领奖状态
  repeated Reward rewards = 6; // 普通奖励列表
  repeated Reward pay_rewards = 7; // 付费奖励列表
}

message WarOrderInfoRsp {
  repeated WarOrderInfo order_list = 1; // 通行证信息
  string order_conf = 2; // 配置数据
  bool pay_state = 3; // 充值状态
  uint32 coin_progress = 4; // 金币箱子累计
  uint32 key_base = 5; // 钥匙增长基础值
  string title = 6; // 主题
  bool need_reward = 7; // 上期有可发放奖励 --客户端主动调用发放,不为false再请求发奖
  int64 st = 8; // 当前服务器10位时间戳
  int64 end_ts = 9; // 本轮战令通行证结束的10位时间戳
  string pay_id = 10; // 充值id --纯激活
  string product_id = 11;
  int64 price = 12; // 价格
  string available_time = 13; // 获得时间 --只对绿钻有用
  string sku_id = 14;
  string activity_id = 15;
  string periods = 16; // 期数
  int64 frame_id = 17; // 可获得的头像框
  int64 title_id = 18; // 可获得的称号
  string title_name = 19;
  string extra = 20; // 额外信息 -- 充值相关
  string last_title_name = 21; // 上期title,参加了才会返回值
  string last_title = 22; // 上期title,参加了才会返回值
  WarOrderPayInfo pay_info2 = 23; // 充值2 --激活 + 战令升级+N
  WarOrderPayInfo pay_info3 = 24; // 充值3 --激活 + 完成战令所有挡位
  WarOrderPayInfo pay_info4 = 25; // 充值4 --完成战令升级+N 激活后可买
  repeated WarOrderLoop list = 26; // 循环奖励列表,激活了循环列表这个才会返回
}

// 只对充值2，4 充值1用老字段，兼容老版本
message WarOrderPayInfo {
  string pay_id = 1; // 充值id --纯激活
  string product_id = 2;
  int64 price = 3; // 价格
  string available_time = 4; // 获得时间 --只对绿钻有用
  string sku_id = 5;
  string activity_id = 6;
  int64 activity_num = 7; // 升级+ N
  string extra = 8; // 酷狗异化字段
}

message WarOrderRewardReq {
  uint32 progress_id = 1; // 通行证进度id
  device.Device device = 2; // 设备信息
}

message WarOrderRewardRsp {
  repeated TreasureCard cards = 3; // 如果发奖产生宝藏奖励这个字段不为空
}

message WarOrderPayRewardReq {
  uint32 progress_id = 1; // 通行证进度id
  device.Device device = 2; // 设备信息
}

message WarOrderPayRewardRsp {
  repeated TreasureCard cards = 1; // 如果发奖产生宝藏奖励这个字段不为空
}

message WarOrderPreRewardReq {
  device.Device device = 1; // 设备信息
}

message WarOrderPreRewardRsp {
  repeated Reward rewards = 1; // 除了宝藏和宝盒金币的奖励
  repeated TreasureCard cards = 2; // 如果发奖产生宝藏奖励这个字段不为空
  uint32 box_coin = 3; // 宝盒里面包含的金币
  bool reward_finish = 4; // 是否完全发完奖
}

message WarOrderLoopRewardReq {
  device.Device device = 1; // 设备信息
}

message WarOrderLoopRewardRsp {
  uint32 num = 1; // 领取的礼包数量
  repeated TreasureCard cards = 2; // 如果发奖产生宝藏奖励这个字段不为空
}

message WarOrderCoinBoxReq {
  device.Device device = 1; // 设备信息
}

message WarOrderCoinBoxRsp {
  uint32 coin_progress = 1; // 金币箱子累计
}

enum ConfigOpTypeK {
  ConfigOpTypeKNone = 0; // 未知
  ConfigOpTypeKBoard = 1; // 杂项
  ConfigOpTypeKShop = 2; // 商店--买道具
  ConfigOpTypeKCollectActivity = 3; // 收集活动基础
  ConfigOpTypeKCollectTask = 4; // 收集活动任务
  ConfigOpTypeKItems = 5; // 道具表 --红石原来的
  ConfigOpTypeKAds = 6; // 广告
  ConfigOpTypeKTreasures = 7; // 宝藏
  ConfigOpTypeKTreasureCards = 8; // 宝藏卡
  ConfigOpTypeKBoxes = 9; // 宝箱
  ConfigOpTypeKGifts = 10; // 礼包
  ConfigOpTypeKWarOrder = 11; // 战令基础配置
  ConfigOpTypeKTaskRewardList = 12; // 任务奖励
  ConfigOpTypeKChapters = 13; // 章节
  ConfigOpTypeKComponents = 14; // 章节元素
  ConfigOpTypeKChapterGift = 15; // 章节奖励包含的中台配置  中台礼包配置--只包含章节的
  ConfigOpTypeKTeamWeek = 16; // 战队周榜奖励配置相关
  ConfigOpTypeKGetVersion = 17; // GetItem的内容版本号 --不支持configOp调用
  ConfigOpTypeKAvatarFrame = 18; // 头像框
  ConfigOpTypeKTitle = 19; // 称号
  ConfigOpTypeKFloorSwitch = 20; // 前100关配置
  ConfigOpTypeKPeakSeasonReward = 21; // 巅峰赛赛季排行奖励
  ConfigOpTypeKMax = 30; //版本最大值，更新需要改
}

message DataRange {
  int64 range_begin = 1; // 区间开始
  int64 range_end = 2; // 区间结束
  int64 version = 3; // 版本号
}

message ConfigOpReq {
  map<int64, int64> version = 1; // ConfigPlusTypeK的值,version  通过类型，版本号获取配置
  map<int64, DataRange> data_range = 2; // ConfigPlusTypeK的值,区间 通过类型，区间获取配置
}

message ConfigBase {
  int64 version = 1;
  string data = 2; // 只有配置信息
}

message ConfigOpRsp {
  map<int64, ConfigBase> kv = 1; // 返回的请求参数=>配置信息 通过类型，版本号获取的配置
  map<int64, ConfigBase> range_kv = 2; // 返回的请求参数=>配置信息 通过类型，区间获取的配置
}

message ConfigVersionReq {}

message ConfigVersionRsp {
  map<int64, int64> version = 1; // ConfigPlusTypeK的值,version
}

message ConfigOpOptimizeReq {
  ConfigOpTypeK k = 1; // 配置类型 目前只支持7.8.13.14
  int64 version = 2; // 客户端缓存的版本记录
  repeated uint32 id_list = 3; // 客户端当前的id缓存
}

message ConfigOpOptimizeRsp {
  ConfigOpTypeK k = 1; // 配置类型 目前只支持7.8.13.14  发啥返啥，客户端只更新对应k的
  int64 version = 2; // 服务器最新版本
  repeated uint32 id_list = 3; // 服务器认为客户端没有的id
  map<uint32, string> items = 4; // 返回最多20个差异配置，未返回的客户端重新拉取
}

message ConfigOpOptimizeByIdReq {
  ConfigOpTypeK k = 1; // 配置类型 目前只支持7.8.13.14
  repeated uint32 id_list = 2; // 客户端需要的id
}

message ConfigOpOptimizeByIdRsp {
  ConfigOpTypeK k = 1; // 配置类型 目前只支持7.8.13.14  发啥返啥，客户端只更新对应k的
  map<uint32, string> items = 2; // 返回最多20个
}

message SuperBollCheckReq {}

message SuperBollCheckRsp {
  uint32 win_count = 1; // 胜利次数，获得彩球输一把清0
  bool super_boll = 2; // 彩球活动可见true，不可见false
}

// 防止后续增加新的可过期配置预留
enum ConfigType {
  TYPE_NONE = 0;
  TYPE_AD = 1; // 目前只有一个广告配置
}

message ExpireConfigReq {
  repeated ConfigType conf_types = 1; // 需要拉取的配置
}

message ExpireConfigRsp {
  message ValidFloorType {
    repeated int64 list = 1;
  }
  string data = 1;
  map<int64, int64> ad_count = 2;
  uint32 ad_total = 3; // 定向1总数
  repeated uint32 target_ads = 4; // 定向1的id集合
  map<int64, ValidFloorType> valid_fts = 5; // 广告坑位id对应的有效关卡类型
  int32 ad_step = 6; // 加步数广告加的步数
}

message PetNewBieCheckReq {}

message PetNewBieCheckRsp {
  map<string, string> mapExt = 1; // 宠物所有新手引导进度
}

message PetNewBieUpdateReq {
  map<string, string> mapExt = 1; // 客户端传需要更新的引导进度
}

message PetNewBieUpdateRsp {}

message AntiCheckReq {}

enum AuthStatus {
  AuthStatus0k = 0; // 不截
  AuthStatusToCert = 1; // 拦截，提示authMsg，要先实名，走认证链接(跳转authURL)
  AuthStatusLimited = 2; // 拦截，提示authMsg，如未成年人可玩时段拦截场景
}

message AntiCheckRsp {
  int64 isAdult = 1; // 是否成年
  int64 isRealName = 2; // 是否实名
  int64 isOpenTime = 3; // 是否开放时间
  AuthStatus authStatus = 4; // 检测状态
  string authMsg = 5; // 提示文案
  string authURL = 6; // 认证链接
}

message ShareCallbackReq {}

message ShareCallbackRsp {
  int64 count = 1; // 今日已分享次数
}

message ShareTodayInfoReq {}

message ShareProgress {
  uint32 progress_id = 1; // 进度id
  uint32 progress_conf = 2; // 配置的进度  --改了配置实时生效
  bool is_reward = 3; // 此进度是否领奖
  repeated Reward rewards = 4; // 此进度配置的奖励
}

message ShareTodayInfoRsp {
  int64 probability = 1; // 概率
  int64 count = 2; // 今日已分享次数
  int64 day_share = 3; // 每日分享x次领奖
  bool today = 4; // 今日分享是否领奖
  repeated Reward rewards = 5; // 每日分享奖励配置
  int64 total_count = 6; // 总分享次数
  repeated ShareProgress progress_list = 7; // 分享进度信息
}

message ShareTodayRewardReq {}

message ShareTodayRewardRsp {
  repeated Reward rewards = 1; // 领奖成功,此列表表示奖励列表,领奖失败会直接报错
  repeated TreasureCard cards = 2; // 如果发奖产生宝藏奖励这个字段不为空
}

message ShareProgressRewardReq {
  uint32 progress_id = 1; // 进度id
}

message ShareProgressRewardRsp {
  repeated Reward rewards = 1; // 领奖成功,此列表表示奖励列表,领奖失败会直接报错
  repeated TreasureCard cards = 2; // 如果发奖产生宝藏奖励这个字段不为空
}

message AreaRankReq {
  Region city = 1; // 地区码为"",发全服
  int64 passback = 2;
  uint32 season = 3; // 拉赛季，0表示当前赛季
}

message Region {
  string name = 1; // 地区名称
  string code = 2; // 地区代码
}

message AreaRankRsp {
  repeated RankItem list = 1; // 排行榜列表
  bool has_next = 2; // 是否还有下一页
  int32 passback = 3; //  再次翻页的时候需要把这个东西传过来
  int32 rank = 4; // 自己的排名
  Region city = 5; //正在查询的
  Region self_city = 6; //自己的
}

message QueryConfigReq {
  string cacheVer = 1; // 前端回传本地缓存的版本号
}

message GeoCity {
  string name = 1; // 地区名称
  string code = 2; // 地区代码
  string pyPrefix = 3; // 拼音首字母
}

message GeoRegion {
  string name = 1;
  string code = 2;
  repeated GeoCity cities = 3;
}

// 前端本地缓存回包中的地区列表regions以及regionVersion
// 当地区映射未发生变化时,回包不会返回regions数据
message QueryConfigRsp {
  string regionVer = 1;
  repeated GeoRegion regions = 2;
  Region city = 3; //用户当前
}

message SuggestCheckReq {
  OperatingSystem os = 1; // 操作系统
  map<string, string> maxExt = 2; // 扩展字段
}

enum SuggestAbt {
  SuggestAbtDefault = 0; // 不干预
  SuggestAbtIntervene = 1; // 一直干预
  SuggestAbtNoInterveneAgain = 2; // 这次不干预下次继续问
  SuggestAbtInterveneAgain = 3; // 这次干预下次继续问
}

message SuggestCheckRsp {
  SuggestAbt suggest = 1; // 推荐类型
  string algorithmInfo = 2; // 上报用, 给到前端, 前端上报到 algorithm_id 字段
  string traceId = 3; // 上报用, 给到前端, 前端上报到trace_id字段
}

message DecorateInfoReq {}

message AvatarFrame {
  uint32 id = 1; // 头像框id
  int64 end_ts = 2; // 到期时间
  bool is_new = 3; // 是否新获得
  int64 update_at = 4; // 获得时间
}

message Title {
  uint32 id = 1; // 称号id
  int64 end_ts = 2; // 到期时间
  bool is_new = 3; // 是否新获得
  int64 update_at = 4; // 获得时间
}

message DecorateInfoRsp {
  bytes profile = 1; // 玩家游戏内部装饰 -- 正在用的
  repeated AvatarFrame frame_list = 2; // 已获得的头像框列表
  repeated Title title_list = 3; // 已获得的称号列表
}

message SetDecorateReq {
  string frame_id = 1; //头像框id --由于后期默认头像框是0，这里不能传uint32
  string title_id = 2; // 称号id
}

message SetDecorateRsp {
  bytes user_profile = 1; // 玩家游戏内部装饰
}

message AutomaticGiftReq {
  string open_id = 1;
  uint32 gift_id_begin = 2; // 发放开始id
  uint32 gift_id_end = 3; // 发放结束id
}

message AutomaticGiftRsp {}

message CheckPeakReq {
  string open_id = 1;
}

message CheckPeakRsp {
  bool Activity = 1;
  repeated int64 Vals = 2;
}

message ADPass {
  int64 ad_id = 1;
  string token = 2;
  int64 star_round = 3;
  string open_id = 4;
}

message ADRewardReq {
  string trace_id = 1;
  string uin = 2;
  string app_pass_data = 3;
  string ipv4 = 4;
}

message ADRewardRsp {
  int32 ret = 1;
  string msg = 2;
}

message AckGameGiftsReq {}

message AckGameGiftsRsp {
  repeated Item game_rewards = 1; // 游戏圈礼包内容领奖成功,此列表表示奖励列表
  repeated TreasureCard cards = 2; // 下发道具的宝藏奖励
}

message CdkGameGiftsReq {
  string cdk = 1; //用户填写的cdk
}

message CdkGameGiftsRsp {
  repeated Item game_rewards = 1; // 游戏圈礼包内容领奖成功,此列表表示奖励列表
  repeated TreasureCard cards = 2; // 下发道具的宝藏奖励
}

message GetSelfRankReq {}

message GetSelfRankRsp {
  uint32 rank = 1;
}

message GetDragonIndemnityReq {}

message GetDragonIndemnityRsp {
  int32 dragon_reward_state = 1; // -1 无奖励 0 有奖未领 1 有奖已领
}

message GetPeakRaceRewardReq {}

message GetPeakRaceRewardRsp {
  uint32 rank = 1; // 玩家上赛季最终排名
  uint32 season = 2; // 赛季
  uint32 score = 3; // 玩家上赛季最终赛季分
  uint32 floor = 4; // 玩家上赛季最终赛季关卡
  int64 end_time = 5; // 上赛季结束时间
  string data = 6; // 上赛季奖励配置
}

message GameQuanTaskReq {
  string iv = 1; //解密参数
  string encry_data = 2; //加密数据
  string signature = 3; //签名字符串后端校验数据真伪
}

message GameQuanTaskRsp {
  repeated Task list = 1;
}

message Task {
  int64 Id = 1; //任务id
  string taskName = 2;
  int32 isAck = 3; //是否领取过
  int32 taskType = 4; //任务类型 0永久 1每日
  repeated Item rewards = 5; // 奖励列表
  int32 cur_num = 6; //当前数量
  int32 target_num = 7; //目标数量
}

message AckGameQuanTaskReq {
  int64 Id = 1; //任务id
  string iv = 2; //解密参数
  string encry_data = 3; //加密数据
}

message AckGameQuanTaskRsp {
  repeated Item rewards = 5; // 奖励列表
  repeated TreasureCard cards = 2; // 下发道具的宝藏奖励
}

message AckTetrisGameReq {
  int64 num = 1;
}

message AckTetrisGameRsp {}

message UserSVipInfoReq {
  int64 num = 1;
}

message UserSVipInfoRsp {
  int64 svip_end_time = 1; // 超级会员过期时间
}

message CheckStepBuyReq {
  string bill_id = 1; // 票据随机id
}

message CheckStepBuyRsp {
  uint32 shop_id = 1; // 不在关卡这个发0
  string bill_id = 2; // 没用过返回""
  repeated UserAsset assets = 3; // 游戏相关资产
  repeated InfiniteItem infinite_items = 4; // 无限战前道具及过期时间
}
