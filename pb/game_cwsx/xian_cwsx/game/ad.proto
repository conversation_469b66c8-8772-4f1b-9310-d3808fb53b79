syntax = "proto3";

package xian_cwsx;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_cwsx/xian_cwsx/pkg/gen/pb/game_cwsx/xian_cwsx/game";
import "pb/game_cwsx/xian_cwsx/game/common.proto";

message AdInfo {
  uint32 ad_id = 1; // 坑位id
  uint32 view_time = 2; // 已观看次数
}

message ADBeforeReq {
  uint32 ad_id = 1; // 坑位id
  int64 floor = 2; // 21号坑位必填, 要查询关卡
  int64 stype = 3; // 21号坑位 关卡类型, <=100关的金币或鲜花关必填
}

message ADBeforeRsp {
  string token = 1;
  int32 ad_left_rewards = 2; //广告分享剩余激励次数
}

message ADReq {
  uint32 ad_id = 1; // 坑位id
  string token = 2;
  int64 star_round = 3; // 明星周期
  int64 stype = 4; // 关卡类型, <=100关的金币或鲜花关必填
  AdExtInfo ad_ext_info = 5; // 广告扩展信息
}
message AdExtInfo {
  string ad_token = 1; // 广告token
  string ad_pos_id = 2; // 广告位id
  DeviceInfo device = 3; // 设备信息
}


message ADRsp {
  uint32 ad_id = 1; // 坑位id
  uint32 view_time = 2; // 已观看次数
  int32 ad_left_rewards = 3; //广告分享剩余激励次数
}

message ADProgressReq {}

message ADProgressRsp {
  repeated AdInfo ad_list = 1;
  int32 ad_left_rewards = 2; //广告分享剩余激励次数
}

//message ADViewRewardReq {
//  uint32 ad_id = 1; // 坑位id
//}
//
//message ADViewRewardRsp {}

message SettleADReq {
  int64 floor = 1; // 要查询关卡
  int64 stype = 2; // 关卡类型, <=100关的金币或鲜花关必填
}

message SettleADRsp {
  int64 floor = 1; // 关卡
  int64 used = 2; // 今日已使用次数
  int64 max = 3; // 今日最大可用次数
  int64 group = 4; // 归属AB组
  bool match = 5; // 本关结算是否可开启广告
}

// 结算额外广告检查
message SettleExtraADReq {
  int64 floor = 1; // 要查询关卡
  bool is_task_center = 2; // 是否从任务中心进入
}

message SettleExtraADRsp {
  int64 floor = 1; // 关卡
  int64 used = 2; // 今日已使用次数
  int64 max = 3; // 今日最大可用次数
  repeated Reward rewards = 4; // 奖励列表 --有次数看完广告发这个
}

