[app]
env = "prod"
appName = "kugou_adapter_service"
httpAddr = "0.0.0.0"
httpPort = 8080
httpMode = "release"
enableTrace = true
enablePprof = true
endpoint = "***********"
token = "ofKaMEJoeAaSNvXnGKRF"

[logger]
level = "warn"
encoding = "json"
encodeCaller = "full"
console = false
filename = "work/log/app.log"
maxSize = 100
maxAge = 30
maxBackups = 30
compress = true

[database.default]
addr = "************:3306"
database = "platform_adapt_order"
user = "kugou_auth"
password = "gkmpszAGH3*-<P"
params = "charset=utf8mb4&parseTime=true&loc=Local"
enableTrace = true
MaxIdleConns = 100
MaxOpenConns = 100
maxLifeTime = "300s"
maxIdleTime = "180s"
skipDefaultTransaction = true
prepareStmt = true
translateError = true

[redis.default]
kind = "cluster"
addrs = ["************:6379"]
password = "Afsj23JB"
dialTimeout = "500ms"
readTimeout = "300ms"
writeTimeout = "300ms"

[apollo.default]
appid = "kugou_adapter_service"
cluster = "default"
namespaceName = "application"
ip = "http://fxbj.kgidc.cn/apollo/meta"
isBackupConfig = true
BackupConfigPath = "data/apollo"
syncServerTimeout = 3
mustStart = true

[thrift]
proxyEnabled = false
proxyHost = "fxbj.kgidc.cn/gwphp/"
timeout = "1000ms"
maxIdleConnsPerHost = 100
maxConnsPerHost = 200
maxIdleConns = 200
idleConnTimeout = "90s"
keepAlive = "15s"
connectionTimeout = "1s"

[thriftclient.fxsoa_soa_user]
name = "fxsoa_soa_user"
namespace = "fx-user-center"
port = 16056
uri = "/plat/user/thrift/userplatbizservice"

[thriftclient.fxsoa_checktoken]
name = "fxsoa_checktoken"
namespace = "fx-user-center"
port = 16034
uri = "/soa/token/thrift/tokenservice"

[thriftclient.platform_globalid_v2]
name = "platform_globalid_v2"
namespace = "basic-service"
port = 16164
uri = "/service"

[thriftclient.platform_certification_service]
name = "platform_certification_service"
namespace = "common-business"
port = 19029
uri = "/platform/thrift/certificationService"

[thriftclient.platform_prize_order]
name = "platform_prize_order"
namespace = "fx-middle-ground"
port = 17700
uri = "/platform/prize/order/sync/"

[thriftclient.platform_consume_service]
name = "platform_consume_service"
namespace = "consume-service"
port = 17007
uri = "/platform_consume_read_service/platform_consume_service"

[thriftclient.sing_consume_service]
name = "sing_consume_service"
namespace = "fx-middle-ground"
port = 17007
uri = "/platform_consume_read_service/platform_consume_service"

[thriftclient.platform_strategy_service]
name = "platform_strategy_service"
namespace = "strategy-service"
port = 16006
uri = "/platform/strategy"

[thriftclient.platform_prize_order_read]
name = "platform_prize_order"
namespace = "fx-middle-ground"
port = 17700
uri = "/platform/prize/order/ReadService"

[thriftclient.kugou_game_fisher_qm]
name = "kugou_game_fisher_qm"
namespace = "game-biz"
port = 16311
uri = "/kugou_adapter/sendSingleRewardCallback"

[thriftclient.sync_order_service]
name = "platform_prize_order"
namespace = "fx-middle-ground"
port = 17700
uri = "/platform/prize/order/sync/"

[thriftclient.commonaward]
name = "platform_award_service"
namespace = "fx-middle-ground"
port = 17099
uri = "/platform_award_service/award"

[thriftclient.goodsgiftservice]
name = "fanxing_finance_goods"
namespace = "send-gift"
port = 17888
uri = "/fanxing_finance_goods/gift_service"

[thriftclient.platform_operation_template]
name = "platform_operation_template"
namespace = "operate-center"
port = 19038
uri = "/platform/platform_operation_template/headlineThriftService"

[thriftclient.platform_socket_message]
name = "platform_socket_message"
namespace = "fx-socket"
port = 18086
uri = "/socket/messagev2.thrift"

[thriftclient.platform_starvip_service]
name = "platform_starvip_service"
namespace = "operate-center"
port = 19033
uri = "/platform/platform-starvip-service/userStarVipService"

[thriftclient.fxsoa_user_baseinfo]
name = "fxsoa_user_baseinfo"
namespace = "fx-user-center"
port = 16163
uri = "/soa/usermodule/userv2service.thrift"

[thriftclient.ys_common_msg_service]
name = "fxgroup_activity_service"
namespace = "yusheng"
port = 18320
uri = "/fxgroup_activity_service/commonMsgService"

[thriftclient.sing-kroom]
name = "sing-kroom"
host = "sing-kroom.kgidc.cn"
uri = "/sing7/kroom/push_socket_msg"