syntax = "proto3";

package game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_segment_api";

import "pb/game_segment/game_segment_comm.proto";

service SeasonSegmentAPI {
  // 查询段位奖励
  rpc QuerySegmentAward(QuerySegmentAwardReq) returns (QuerySegmentAwardRsp);
  // 领取段位奖励
  rpc ReceiveAward(ReceiveAwardReq) returns (ReceiveAwardRsp);
  // 查询赛季榜单-巅峰榜
  rpc QuerySeasonRank(QuerySeasonRankReq) returns (QuerySeasonRankRsp);
  // 查询段位榜单-分组榜单
  rpc QuerySegmentRank(QuerySegmentRankReq) returns (QuerySegmentRankRsp);
  // 查询赛季和段位信息
  rpc QuerySeasonSegment(QuerySeasonSegmentReq) returns (QuerySeasonSegmentRsp);
  // 查询赛季回顾纪录
  rpc QuerySeasonRecord(QuerySeasonRecordReq) returns (QuerySeasonRecordRsp);
  // 拉取赛季事件列表
  rpc GetSeasonEvent(GetSeasonEventReq) returns (GetSeasonEventRsp);
  // Ack赛季事件
  rpc AckSeasonEvent(AckSeasonEventReq) returns (AckSeasonEventRsp);
}

message QuerySegmentAwardReq {
  string bizAppID = 1; //bizAppID=>gameAppID
  int32 seasonID = 2; //赛季ID
}

message QuerySegmentAwardRsp {
  message SubSegAwardItem {
    int32 segmentID = 1; //段位ID
    string subName = 2; //子段位名称，比如：III
    string subIcon = 3; //子段位Icon
    AwardStatus status = 4; //奖励状态，参见：game_segment_comm.AwardStatus
    repeated AwardItem awardList = 5; //奖励列表
  }
  message SegmentAwardItem {
    string mainName = 2; //主段位名称，比如：黄金I
    string mainIcon = 3; //主段位Icon
    repeated SubSegAwardItem subSegmentList = 4; //子段位列表
    bool hasAward = 5; //段位奖励小红点
  }
  repeated SegmentAwardItem awardList = 1;
  int32 awardNum = 2; //待领取奖励数量，0-表示没有
  string segmentName = 3; //当前段位名称
}

message ReceiveAwardReq {
  string bizAppID = 1; //bizAppID=>gameAppID
  int32 seasonID = 2; //赛季ID
}

message ReceiveAwardRsp {
  int32 ret = 1; //0-成功，非0-失败
  string msg = 2; //错误提示信息
  repeated AwardItem awardList = 3; //奖励列表
}

message RankItem {
  int32 rank = 1; //排名(从1开始，-1表示没有进排名)
  string nick = 2; //昵称
  string avatar = 3; //头像url
  int64 score = 4; //积分
  string openID = 5; //用户openid
  int64 uid = 6; //用户uid
  bool isFollow = 7; //是否已关注
  repeated AwardItem awardList = 8; //奖励列表
}

message QuerySeasonRankReq {
  string bizAppID = 1; //bizAppID=>gameAppID
  string passback = 2; //分页参数
}

message QuerySeasonRankRsp {
  string passback = 1; //分页参数
  bool hasMore = 2; //是否还有数据
  RankItem selfRank = 3; //自己排名信息(只有首页才会返回)
  string bgImage = 4; //赛季背景图
  repeated RankItem rankList = 5; //排名列表
  string seasonName = 6; //赛季名称
  string segmentName = 7; //当前段位名称
}

message QuerySegmentRankReq {
  string bizAppID = 1; //bizAppID=>gameAppID
  RankType rankType = 2; //本期/上一期，参见：game_segment_comm.RankType
  string passback = 3; //分页参数
}

message SeasonSegmentInfo {
  message PeriodInfo {
    int64 startTime = 1; //周期开始时间
    int64 endTime = 2; //周期结束时间
  }
  int32 seasonID = 1; //赛季ID（-1表示无赛季信息）
  string seasonName = 2; //赛季名称
  string segmentName = 3; //段位名称，比如：黄金III
  string segmentIcon = 4; //段位Icon
  int32 rank = 5; //排名(从1开始, -1:表示暂无排名)
  int64 serverTs = 6; //服务器时间
  int64 settlementTs = 7 [deprecated = true]; //结算时间（废弃）
  GroupType groupType = 8; //分组类型，参见：game_segment_comm.GroupType
  int64 seasonStartTime = 9; //赛季开始时间
  int64 seasonEndTime = 10; //赛季结束时间
  string seasonRecordBackground = 11; //赛季回顾背景图
  string desc = 12; //周期结果描述
  PeriodInfo currPeriod = 13; //当前周期（为空表示不在任何周期内）
  PeriodInfo nextPeriod = 14; //下一个周期（为空表示没有下一个周期了）
  int64 score = 15; //周期积分
}

message QuerySegmentRankRsp {
  message SectionInfo {
    string sectionName = 1; //分段名称, 例如: 升段区, 保段区 or 降段区
    string sectionDesc = 2; //分段描述, 例如: 排名前20名可升至白银I
    GroupType groupType = 3; //分组类型，参见：game_segment_comm.GroupType
    int32 rankLine = 4; //进入该分组的排名下限(userRank <= rankLine)
  }
  string passback = 1; //分页参数
  bool hasMore = 2; //是否还有数据
  RankStatus status = 3; //排名开放状态，参见：game_segment_comm.RankStatus
  RankItem selfRank = 4; //自己排名信息(只有首页才会返回)
  SeasonSegmentInfo seasonSegmentInfo = 5; //赛季段位信息
  bool hasAward = 6; //段位奖励小红点(只有首页才会返回)
  string bgImage = 7; //段位背景图
  repeated RankItem rankList = 8; //排名列表
  repeated SectionInfo sectionInfo = 9; //分段文案信息(只有首页才会返回)
}

message QuerySeasonSegmentReq {
  string bizAppID = 1; //bizAppID=>gameAppID
  int32 seasonID = 2; //小于等于零，查当前最新赛季
}

message QuerySeasonSegmentRsp {
  SeasonSegmentInfo seasonSegmentInfo = 1; //赛季段位信息
  string selfAvatar = 2; //本人头像
  repeated string topRankAvatar = 3; //巅峰榜top3头像
  string avatarFrame = 4; //本人头像框
  bool hasEvent = 5; //是否有事件
  RankStatus status = 6; //排名开放状态，参见：game_segment_comm.RankStatus
}

message QuerySeasonRecordReq {
  string bizAppID = 1; //bizAppID=>gameAppID
  string passback = 2; //分页参数
}

message QuerySeasonRecordRsp {
  message SeasonHistory {
    string seasonName = 1; //赛季名称
    int64 startTime = 2; //赛季开始时间
    int64 endTime = 3; //赛季结束时间
    string segmentName = 4; //最高段位名称，比如：黄金III
    string segmentIcon = 5; //最高段位Icon
  }
  string passback = 1; //分页参数
  bool hasMore = 2; //是否还有数据
  repeated SeasonHistory seasonList = 3; //历史赛季列表
  string bgImage = 4; //赛季背景图
  string segmentName = 5; //当前段位名称
}

message GetSeasonEventReq {
  string bizAppID = 1; //bizAppID=>gameAppID
}

message GetSeasonEventRsp {
  message Event {
    int32 eventID = 1; //事件ID
    int32 cmd = 2; //主事件
    int32 subCmd = 3; //子事件
    string data = 4; //事件数据-json类型，根据cmd-subcmd找到对应结构解析
  }
  repeated Event eventList = 1; //事件列表
}

message AckSeasonEventReq {
  string bizAppID = 1; //bizAppID=>gameAppID
  repeated int32 eventIDList = 2; //事件ID列表
}

message AckSeasonEventRsp {}
