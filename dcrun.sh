#!/bin/bash
PROJECT="kugou_adapter_service"
DIR=/data1/platform/${PROJECT}/${PROJECT}
CONF_DIR=${DIR}/conf

function k8s_cluster_dev() {
  echo "kubernetes集群-开发环境."
  clear config_dev
}

function k8s_cluster_test() {
  echo "kubernetes集群-测试环境"
  clear config_test
}

function k8s_cluster_pro() {
  echo "kubernetes集群-生产环境"
  clear config_prod
}

function clear() {
    find "${CONF_DIR}" -mindepth 1 -maxdepth 1 ! -name "${1}.toml" | xargs -I {} rm {}
    mv ${CONF_DIR}/config_*.toml ${CONF_DIR}/config.toml
    ls -alF ${CONF_DIR}
}

function conf_rename() {
  # shellcheck disable=SC2154
  echo "[$(date)] 当前集群名字为$POD_CLUSTER_NAME"
  if [[ $POD_CLUSTER_NAME == "k8s_cluster_fxyc_test" ]] || [[ $POD_CLUSTER_NAME == "k8s_cluster_gz_tke_6_test" ]]; then
    k8s_cluster_test
  elif [[ $POD_CLUSTER_NAME == "k8s_cluster_fxyc_dev" ]]; then
    k8s_cluster_dev
  elif [[ $POD_CLUSTER_NAME == "k8s_cluster_bj_tke_6" ]] || [[ $POD_CLUSTER_NAME == "k8s_cluster_bj_tke_7" ]] || [[ $POD_CLUSTER_NAME == "k8s_cluster_bj_tke2_6" ]] || [[ $POD_CLUSTER_NAME == "k8s_cluster_bj_tke2_7" ]]; then
    echo "生产环境"
    k8s_cluster_pro
  else
    echo "集群名未能识别，中断并退出."
    exit 1
  fi
}

#run
function run() {
  ls -alF ${DIR}
  chmod +x ${DIR}/bin/${PROJECT}
  cd ${DIR}
  mkdir -p ${DIR}/work/log
  ls -alF ${DIR}
  ${DIR}/bin/${PROJECT}
}

function generate_log() {
   mkdir -p /data1/logs
}

#main func
function main() {
  conf_rename
  generate_log
  run
}

main