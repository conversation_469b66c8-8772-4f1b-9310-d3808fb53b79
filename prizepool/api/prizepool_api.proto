syntax = "proto3";

package game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/prizepool/api";

import "google/api/annotations.proto";

service PrizePoolApi {
  // 通知PK摇现金活动
  rpc NotifyPKCash(NotifyPKCashReq) returns (NotifyPKCashRsp) {
    option (google.api.http) = {
      post: "/miniprogram/prizepool_notify_pk_cash"
      body: "*"
    };
  }
}

message userContribute
{
    uint64 uid = 1;
    uint32 rank = 2; // 排名 从 1 开始
    int64 score = 3; // 分数
}

message AnchorRank
{
    uint64 anchorID = 1;
    uint32 rank = 2; // 排名 从 1 开始
    int64 score = 3; // 分数
    repeated userContribute contributes = 4; //用户贡献
}

message NotifyPKCashReq{
    string appId = 1;                   //游戏APPID
    string roundID = 2;                 //场次ID
    repeated AnchorRank ranks = 3;      //排行
    uint64 gameStartTs = 4;             //游戏开始时间
}

message NotifyPKCashRsp{
    int32 error_code = 1 [json_name = "error_code"];
    string error_msg = 2 [json_name = "error_msg"];
}   

