syntax = "proto3";

package game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/prizepool";
import "pb/interactive_game_pk/common/common.proto";

enum PrizePoolStatus {
    PRIZE_POOL_STATUS_NONE = 0;
    PRIZE_POOL_STATUS_ACCUM = 1;     //累积中
    PRIZE_POOL_STATUS_SETTLE = 2;    //结算中
    PRIZE_POOL_STATUS_FINISH = 3;    //结束
}

//meta数据
message poolMeta {
    uint32 partition = 1; //分区数
    PrizePoolStatus status = 2; //状态
    //todo 房间
}

//奖池item
message PoolItem {
    string openId = 1; 
    string roomID = 2;
    uint64 amount = 3;
    string anchorOpenId = 4;
}

//分key奖池
message PartitionPool {
    repeated PoolItem pools = 1;
}


//用户数值
message UserData {
    string openId = 1; //用户openid
    uint64 amount = 2; //金额
    uint32 rank = 3; //排行
}

//总奖池数据
message PoolData {
    uint64 totalAmount = 1;   //总金额
    repeated RoomData roomRank = 2;    //直播间数据
}

//房间数值
message RoomData {
    string anchorOpenId = 1; //主播openid
    uint64 totalAmount = 2; //总金额
    repeated UserData userRank = 3; //用户排行
    uint32 rank = 4;
}

//奖池快照
message PoolSnapshot {
    uint64 totalAmount = 1;   //总金额
    repeated RoomData roomRank = 2;    //直播间排行
}

//游戏信息
message PoolGameData {
    uint64 gameBeginTs = 1;    //游戏开始时间
    uint64 gameEndTs = 2;      //游戏结束时间
    interactive_game_pk.common.RoomEventGameOver gameOverInfo = 3;  //游戏结算信息
}

message PrizePoolConfigItem {
    string appId = 1;
    string serviceName = 2;
    uint32 timeout = 3;
    uint32 pKShareCash = 4; //是否走PK摇现金
}

message PrizePoolConfig {
    repeated PrizePoolConfigItem items = 1;
    uint64 uUpdateTs = 2;
}


