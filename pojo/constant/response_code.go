package constant

const (
	STATUS_SUCCESS int32 = 0
	STATUS_FAIL    int32 = 1

	PARAM_ERR int32 = 100001
)

type ErrorCode struct {
	Code  int
	Error string
}

var (
	//房间相关错误码(20000 ~ 20099)
	RiskContent                = ErrorCode{4001, "存在敏感信息"}
	MessageRiskContent         = ErrorCode{4002, "涉及违规词汇或行为，发送消息失败"}
	CreatedRoomtranFailed      = ErrorCode{20001, "房间创建失败"}
	CreatedRoomidFailed        = ErrorCode{20002, "房间创建失败"}
	CreatedRoompropFailed      = ErrorCode{20003, "初始化房间失败"}
	RoomNotFound               = ErrorCode{20004, "跟听已失效，可以邀请其他好友跟你听歌"}
	SyncinfoNotFound           = ErrorCode{20005, "房间不存在或者已经解散"}
	RoomIsFull                 = ErrorCode{20006, "跟听人数已满，可以邀请其他好友跟你听歌"}
	UserInRoom                 = ErrorCode{20007, "受邀人已在房"}
	NocanplySong               = ErrorCode{20008, "当前队列歌曲暂不支持跟听，换批歌曲试试吧！"}
	RandomRoomNotFound         = ErrorCode{20009, "匹配失败，请稍后重试"}
	NobodyToAccept             = ErrorCode{20010, "无人接收转移"}
	RoomIsFullInvite           = ErrorCode{20011, "跟听队列已满员"}
	OnlineUserRoomLimit        = ErrorCode{20012, "当前账号已在一起听中，请确认是否还在其他端加入了一起听"}
	OnlineUserRoomLimitDelErr  = ErrorCode{20013, "用户退出失败，请稍后重试"}
	UserNotInRoom              = ErrorCode{20014, "用户不在房，请稍后重试"}
	MicNeedLiveModeDoubleError = ErrorCode{20015, "连麦功能仅支持好友模式"}
	MicNeedDoubleUserError     = ErrorCode{20016, "等好友来了再用吧"}
	MicVersionError            = ErrorCode{20017, "对方版本暂不支持语音对讲"}
	LocationReportError        = ErrorCode{20018, "地理位置上报失败，请稍后重试"}

	//歌单相关错误码(20100 ~ 20149)
	DecodeRadioFailed   = ErrorCode{20102, "获取歌单失败"}
	MissedRadioSyncinfo = ErrorCode{20103, "歌单异常"}
	MissedRadioCursong  = ErrorCode{20104, "歌单异常"}
	EmptyRadioList      = ErrorCode{20105, "歌曲无效，换批歌试试吧"}
	ExceedMaximunSongs  = ErrorCode{20106, "播放队列太长啦，先听这些吧"}
	UpdateRadioSyncinfo = ErrorCode{20113, "更新歌单异常"}
	UpdateRadioInfott   = ErrorCode{20114, "更新歌单异常"}

	//播放器操作相关错误码(20150 ~ 20179)
	PlayerOperProfailed = ErrorCode{20150, "操作失败"}
	MissedPlaymode      = ErrorCode{20151, "请求无效"}
	PlayModeSame        = ErrorCode{20152, "模式一样，无需切换"}

	//跟听操作相关错误码(20180 ~ 20209)
	RadioInitPreparing     = ErrorCode{20197, "歌单准备中"}
	RadioInitFailed        = ErrorCode{20198, "歌单异常"}
	RadioLinkBroke         = ErrorCode{20199, "当前没有可一起听歌曲"}
	ConfiguratioNotChanged = ErrorCode{20180, "配置未发生变化"}
	DonNotPublic           = ErrorCode{20181, "Ta刚下线了，换个人听吧"}
	RepeatedApplication    = ErrorCode{20182, "已经申请过了，请稍后"}
	RestrictedApplication  = ErrorCode{20183, "申请跟Ta听次数达到上限了，请稍后再试"}
	UserListeningOthers    = ErrorCode{20184, "Ta在跟听中了，换个人听吧"}
	ListeningRoomFull      = ErrorCode{20185, "跟Ta听的人数达到上限了，换个人听吧"}
	ListeningMyself        = ErrorCode{20186, "Ta在跟听中了，换个人听吧"}
	EnterRoomIsFull        = ErrorCode{20187, "来晚一步，已经满员了哦"} //进房的时候满员的提示文案
	RoomSendMessageMute    = ErrorCode{20188, "你已被管理员禁言，请稍后重试"}
	UserInfoError          = ErrorCode{20189, "用户信息查询失败"}
	RoomSendMessageTooLong = ErrorCode{20190, "输入字数超过限制"}

	//请求参数相关错误码(30000 ~ 39999)
	InvalidRequest      = ErrorCode{30002, "非法请求"}
	TokenExpired        = ErrorCode{30003, "登录已过期"}
	MissedEventID       = ErrorCode{30004, "请求无效"}
	MissedComingFrom    = ErrorCode{30005, "请求无效"}
	MissedInviteID      = ErrorCode{30006, "请求无效"}
	MissedInviteCode    = ErrorCode{30007, "请求无效"}
	InviteCodeExpired   = ErrorCode{30008, "链接已过期"}
	InvalidQuerySong    = ErrorCode{30009, "请求无效"}
	MissedSongs         = ErrorCode{30010, "获取歌单信息失败"}
	MissedProgress      = ErrorCode{30011, "获取播放进度信息失败"}
	InvalidRadioActions = ErrorCode{30013, "请求无效"}
	PagesizeTooLarge    = ErrorCode{30014, "请求无效"}
	UserInviteSelf      = ErrorCode{30016, "请求无效"}
	UserFakeQuit        = ErrorCode{30017, "请求无效"}
	MissedAudioArgs     = ErrorCode{30018, "请求无效"}
	TooManyInviteIDs    = ErrorCode{30019, "邀请人数过多"}
	LiveModeRevert      = ErrorCode{30022, "请求无效"}
	AuthRoomFailed      = ErrorCode{39000, "权限不足"}
	AuthEventIDFailed   = ErrorCode{39001, "权限不足"}
	AuthCancelInvite    = ErrorCode{39002, "请求无效"}
	AuthRoomEnter       = ErrorCode{39003, "一起听邀请已失效"}
	RadioListExpired    = ErrorCode{39004, "请求无效，本地歌单列表已过期"}
	RadioEditNotAllow   = ErrorCode{39005, "权限不足"}
	HeartbeatIgnore     = ErrorCode{39007, "请求无效"}
	RadioPlayNotAllow   = ErrorCode{39008, "该歌曲暂不支持跟听"}
	AuthChatFailed      = ErrorCode{39010, "您已退出一起听，请重新加入"}
	PrivateRoomFailed   = ErrorCode{39034, "当前一起听为邀请制，暂不可跟听哦"}
	UpgradeVersion      = ErrorCode{39035, "加入失败，请升级酷狗最新版"}
	OtherUpgradeVersion = ErrorCode{39036, "对方酷狗版本太低，无法加入"}
	GroupChatSwitchOff  = ErrorCode{39037, "由于系统维护，今日暂不可发言，请于明日再试"}

	//权限相关错误码(39xxx)
	MysqlConnectErr      = ErrorCode{90010, "服务器繁忙"}
	MysqlQueryErr        = ErrorCode{90011, "服务器繁忙"}
	RedisConnectErr      = ErrorCode{90020, "服务器繁忙"}
	RedisQueryErr        = ErrorCode{90021, "服务器繁忙"}
	GetLockFailed        = ErrorCode{90022, "系统繁忙, 请稍后重试"}
	SystemError          = ErrorCode{90023, "系统繁忙, 请稍后重试"}
	UpstreamFriendsErr   = ErrorCode{90100, "获取互关好友失败"}
	UpstreamVipinfoErr   = ErrorCode{90101, "获取vip信息失败"}
	UpstreamChkFollowErr = ErrorCode{90102, "获取关注信息失败"}
	UpstreamUserinfoErr  = ErrorCode{90103, "获取用户信息失败"}
	//UpstreamMsgonlineErr = ErrorCode{90110, "网络错误"}
	ParamErr = ErrorCode{90111, "参数错误"}
	//JsonDecodeErr        = ErrorCode{90112, "系统繁忙, 请稍后重试"}
	FavSongErr           = ErrorCode{90113, "获取我喜欢歌曲列表失败"}
	RankAudioErr         = ErrorCode{90114, "获取排行榜歌曲列表接口失败"}
	AssetTypeErr         = ErrorCode{90115, "获取用户资产接口失败"}
	SignErr              = ErrorCode{90116, "鉴权失败"}
	GetUserFollowListErr = ErrorCode{90117, "获取互关好友失败"}
)
