package constant

import (
	"fmt"
	"time"
)

const (
	PREFIX         string = "GENTING:"
	SPLIT          string = ":"
	Call_BACK_TIME        = 5 * time.Second
)

type RedisKey struct {
	Key string
	Ttl time.Duration
}

var (
	// 全局
	OnlineUser  = &RedisKey{Key: "ONLINE:USER"}                                    // 全局在线人数
	OnlinePUser = &RedisKey{Key: "ONLINE:PUBLIC:ROOM:USER"}                        // 全局公开房在线人数
	RoomId      = &RedisKey{Key: "ROOMID:CLOUD:GEN:%s", Ttl: 172800 * time.Second} // 生成房间ID
	// 用户相关
	UserInfo        = &RedisKey{Key: "USERINFO:NEW:", Ttl: 600 * time.Second}                    // 邀请进房私聊消息 频率限制
	UserFollowGuide = &RedisKey{Key: "USER:FOLLOW:GUIDE:%d:%d:%d", Ttl: 86400 * 2 * time.Second} // 关注引导

	// 歌单相关
	RadioList        = &RedisKey{Key: "ROOMID:RADIO:LIST:%d:{eid:%s}", Ttl: 12 * 3600 * time.Second} // 歌单
	RadioExtends     = &RedisKey{Key: "ROOMID:EXTENDS:%d:eid:%s", Ttl: 12 * 3600 * time.Second}      // 歌曲的拓展信息
	RandomList       = &RedisKey{Key: "ROOMID:RANDOM:%d:eid:%s:%d", Ttl: 12 * 3600 * time.Second}    // 随机歌单
	SongInfo         = &RedisKey{Key: "SONGINFO_V5:%s:%s", Ttl: 3600 * time.Second}                  // 歌曲信息
	AlbumSongInfo    = &RedisKey{Key: "AlbumSongInfo:", Ttl: 3600 * time.Second}                     // 歌曲信息
	AlbumAudioTypeId = &RedisKey{Key: "AlbumAudioTypeId:", Ttl: 3600 * time.Second}                  // 专辑音频类型（用于风控过滤）
	SongRpcAudioInfo = &RedisKey{Key: "SongRpcAudioInfoV2:", Ttl: 3600 * time.Second}                // 对外提供的歌曲信息
	SongPrivinfo     = &RedisKey{Key: "SONG:202402:PRIVILEGE_V3:%s:%s:%d", Ttl: 3600 * time.Second}  // 歌曲信息
	SyncPlayer       = &RedisKey{Key: "PLAYER:SYNC:%d:{eid:%s}", Ttl: 12 * 3600 * time.Second}       // 播放器同步进度信息

	// 礼物相关
	GiftInfo = &RedisKey{Key: "GiftInfo%d", Ttl: 5 * 86400 * time.Second}
)

func GetRedisKeyInfo(redisKey *RedisKey) (key string, ttl time.Duration) {
	return GetRedisKeyString(redisKey), redisKey.Ttl
}

func GetRedisKeyString(redisKey *RedisKey) (key string) {
	return fmt.Sprintf("%s%s", PREFIX, redisKey.Key)
}
