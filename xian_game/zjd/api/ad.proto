syntax = "proto3";

package game_zjd;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_game/zjd/pkg/gen/pb/m/zjd/api";

// 广告上报 Req
message AdCheckReq {
  uint32 view_cnt = 1; // 从0累计,第几次观看
  Ad ad = 2; // 广告信息
}

// 广告上报 Rsp
message AdCheckRsp {
  uint32 view_cnt = 1; // 服务端统计已观看的次数,包含本地
}

message Ad {
  string ad_token = 1; // 广告token
  string ad_pos_id = 2; // 广告位id
  DeviceInfo device = 3; // 设备信息
}

message DeviceInfo {
  string ip = 1;
  string mac = 2;
  string imei = 3;
  string idfa = 4;
  string idfv = 5;
  uint32 mobileFlag = 6; // 是否来自手机
  string mobleQUA = 7; // qua
  string uuid = 8;
  string udid = 9;
  string qimei36 = 10;
  string deviceInfo = 11;
}

// 看广告复活 Req
message AdRevivesReq {}

// 看广告复活 Rsp
message AdRevivesRsp {
  int32 status = 1; // 订单状态 =>comm.TradeStatusType
}
