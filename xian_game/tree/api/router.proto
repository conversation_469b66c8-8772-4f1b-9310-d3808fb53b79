syntax = "proto3";

package game_tree;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_game/tree/pkg/gen/pb/xian_game/tree/api";

import "pb/asset/asset.proto";
import "pb/xian_game/tree/api/ad.proto";
import "pb/xian_game/tree/api/config.proto";
import "pb/xian_game/tree/comm/comm.proto";

service Api {
  rpc Config(ConfigReq) returns (ConfigRsp); // 配置数据
  rpc UserInfo(UserInfoReq) returns (UserInfoRsp); // 加载用户信息
  rpc Ticket(TicketReq) returns (TicketRsp); // 统一票据接口
  rpc Assets(AssetsReq) returns (AssetsRsp); // 用户资产信息
  //  rpc NoviceGuide(NoviceGuideReq) returns (NoviceGuideRsp); // 新手引导
  // ----状态
  rpc State(StateReq) returns (StateRsp); // 游戏状态,个人主页和访问其他玩家均使用
  rpc StateDetail(StateDetailReq) returns (StateDetailRsp); // 游戏状态,个人主页和访问其他玩家均使用
  // ----签到
  rpc SignInState(SignInStateReq) returns (SignInStateRsp); // 签到-状态
  rpc SignIn(SignInReq) returns (SignInRsp); // 签到-普通、补签、加倍领奖
  // ----水滴相关 (蓄水池与摇晃次数：仅能自己给自己浇水产生)
  rpc WateringSelf(WateringSelfReq) returns (WateringSelfRsp); // 浇水+++升级
  rpc Shake(ShakeReq) returns (ShakeRsp); // 仅摇晃
  rpc ShakeAward(ShakeAwardReq) returns (ShakeAwardRsp); // 仅领取摇晃结果
  rpc WaterAd(WaterAdReq) returns (WaterAdRsp); // 广告领水滴  ---无需验证用户水滴资产
  rpc WaterPoolGet(WaterPoolGetReq) returns (WaterPoolGetRsp); // 蓄水池水滴领取
  rpc WaterSteal(WaterStealReq) returns (WaterStealRsp); // 偷水滴
  rpc WateringOther(WateringOtherReq) returns (WateringOtherRsp); // 帮别人浇水
  // ----好友相关
  rpc Friends(FriendsReq) returns (FriendsRsp); // 好友列表
  rpc Visitors(VisitorsReq) returns (VisitorsRsp); // 拜访者列表
  // ----记录相关
  rpc Records(RecordsReq) returns (RecordsRsp); // 成长明细
  // ----设置服务器时间
  rpc GmSet(GmSetReq) returns (GmSetRsp); // gm工具
  // ----消息开关等设置 第一版本前端使用日历
  //  rpc SwitchSet(SwitchSetReq) returns (SwitchSetRsp); // 消息开关等设置
  // ----助力 第一版本暂时暂停
  //  rpc ShareVisit(ShareVisitReq) returns (ShareVisitRsp); // 分享好友助力状态
  //  rpc ShareReceive(ShareReceiveReq) returns (ShareReceiveRsp); // 单个助力奖励领取|全部助力奖励领取
  // ----任务中心 第一版本暂时暂停
  //  rpc TaskState(TaskStateReq) returns (TaskStateRsp); // 任务中心-任务状态
  //  rpc TaskAward(TaskAwardReq) returns (TaskAwardRsp); // 任务中心-任务领奖
  //  rpc TaskDo(TaskDoReq) returns (TaskDoRsp); // 任务中心-做任务 （定点领水滴、观看广告）

}

message UserInfoReq {
  LogicVersion logic_version = 10; // 客户端逻辑版本号 LogicVersion
}

message UserInfoRsp {
  repeated component.game.UserAsset assets = 1; // 资产余额
  uint32 first_login = 10; // 是否是第一次登录（注册） CommBool
  uint32 novice_guide = 11; // 是否完成新手引导 CommBool
  uint32 logic_version = 12; // 客户端逻辑版本号 LogicVersion
}

// 客户端逻辑版本号
enum LogicVersion {
  LogicVersion_Default = 0; // 默认
  LogicVersion_FreeWaterAndNewLevel = 1; // 等级(累计值超限+新等级模式)
}

// 票据 req
message TicketReq {}

// 票据 rsp
message TicketRsp {
  string tid = 1; // 票据ID
}

// 完成新手新导 req
message NoviceGuideReq {}

// 完成新手新导 rsp
message NoviceGuideRsp {}

// 状态 req
message StateReq {
  VisitType st = 1; // 打开场景
  string other_open_id = 2; // 其他用户openid
}

// VisitType 用户当前的游戏状态
enum VisitType {
  VisitType_None = 0; // 默认请求
  VisitType_Other = 1; // 访问其他用户
  VisitType_VisitByShare = 2; // 特殊访问：其他用户通过分享来源
}

// 状态 rsp
message StateRsp {
  int64 t = 1; // 当前服务器时间
  TreeLevel tree = 2; // 树相关信息
  int64 asset_water = 3; // 剩余水滴(自己的)
}

// 状态详情 Req
message StateDetailReq {
  VisitType st = 1; // 打开场景
  string other_open_id = 2; // 其他用户openid
}

// 状态详情 Rsp
message StateDetailRsp {
  int64 t = 1; // 当前服务器时间
  UseState use_state = 2; // 各种状态
  UserMiniInfo u_info = 3; // 当前主页用户信息/自己主页该内容不返回
  TreeLevel tree = 4; // 树相关信息
  PlayDay day = 5; // [按天刷新] 数据
  int64 asset_water = 6; // 剩余水滴(自己的)
  uint32 u_aw_whether = 7; // 被拜访者是否拥有水滴 CommBool
}

// PlayDay 按天刷新数据结构
message PlayDay {
  ShakeInfo shake = 1; // [按天刷新]摇晃信息
  WaterPool water_pool = 2; // [按天刷新]蓄水池
  Steal steal = 3; // [按天刷新]偷水数据
  WateringHelp water_help = 4; // [按天刷新]帮助他人浇水
  uint32 surplus_ad = 5; // [按天刷新]剩余广告次数
}

message UseState {
  uint32 cur_day_state = 1; // 签到当日的状态
  uint32 logic_version = 2; // 客户端逻辑版本号 LogicVersion
}

// UserMiniInfo 用户信息
message UserMiniInfo {
  string open_id = 1; // 用户openid
  string name = 2; // 用户昵称
  string avatar = 3; // 用户头像
}

// 签到状态 req
message SignInStateReq {}

// 签到状态 rsp
message SignInStateRsp {
  repeated SignInInfo sign_in_s = 1; // 签到的数据列表
}

// 签到 req
message SignInReq {
  SignInType type = 1; // 签到方式
  WeekDay week_day = 2; // 周-~周日
  int64 reward_id = 3; // 礼包ID
  AdInfo ad = 4; // 广告基础数据
}

// 签到 rsp
message SignInRsp {
  repeated RewardPool pool_list = 1; // 获取的奖励
}

// 浇水+升级 req
message WateringSelfReq {
  string tid = 1; // 票据ID
  uint32 water_every = 2; // 单次的水滴数量
  uint32 water_times = 3; // 浇N次[版本号大于0以后弃用]
  CommBool novice_guide = 4; // 新手引导 CommBool
  LogicVersion logic_version = 5; // 客户端逻辑 LogicVersion
}

// 浇水+升级 给自己浇水的结果 rsp
message WateringSelfRsp {
  TreeLevel tree_level = 1; // 当前树的最新状态
  ShakeInfo shake = 2; // 摇晃的数据
  RewardPool upgrade_pool = 3; // 升级产生的奖池 [当该值为空，则代表没有升级]
  RewardPool max_pool = 4; // 最高等级 [当该值为空，则代表没有升级]
  uint32 water_use_cnt = 5; // 自己总共使用了多少水滴
}

// 摇晃 req
message ShakeReq {
  string tid = 1; // 票据ID
}

// 摇晃 rsp
message ShakeRsp {
  RewardPool pool = 1; // 奖池信息
  ShakeInfo shake = 2; // 摇晃信息
}

// 摇晃第二次广告领奖 req
message ShakeAwardReq {
  AdInfo ad = 1; // 广告验证内容
  uint32 pool_item_id = 2; // 奖池子类ID
  string last_tid = 3; // 上一次票据ID
}

// 摇晃第二次广告领奖 rsp
message ShakeAwardRsp {
  RewardPool pool = 1; // 奖池信息
}

// 蓄水池领奖 req
message WaterPoolGetReq {}

// 蓄水池领奖 rsp
message WaterPoolGetRsp {}

// 广告领奖 req
message WaterAdReq {
  AdInfo ad = 1; // 广告验证内容
}

// 广告领奖 rsp
message WaterAdRsp {
  RewardPool pool = 1; // 奖池信息
}

// 偷水滴 req
message WaterStealReq {
  string tid = 1; // 票据ID
  string steal_open_id = 2; // 被偷水的用户ID
}

// 偷水滴 rsp
message WaterStealRsp {
  uint32 water_change_cnt = 1; // 丢失的水滴数量
}

// 帮他人浇水 req
message WateringOtherReq {
  string tid = 1; // 票据ID
  string help_open_id = 2; // 被助力的用户ID
}

// 帮他人浇水 rsp
message WateringOtherRsp {
  uint32 incr_self = 1; // 帮助浇水的用户增加5g水滴
  uint32 incr_other = 2; // 被浇水的好友摇钱树增加5g水滴的进度 【当该值为0,则代表被浇水的树当前树差一滴水满级、并且树没有被浇水】
}

// 好友列表 req
message FriendsReq {
  uint32 page = 1; // 页码 从1开始
}

// 好友列表 rsp
message FriendsRsp {
  repeated Friend friends = 1;
  bool is_last = 2; // 是否还有下一页,
}

// Friend好友的信息
message Friend {
  string open_id = 1; // 用户openid
  string name = 2; // 用户昵称
  string avatar = 3; // 用户头像
  uint32 level = 4; // 树等级
  uint32 steal_surplus_cnt = 5; // 剩余偷水次数
}

// 访客 req
message VisitorsReq {
  int64 last_flag = 1; // 访问的标识,默认为空时，默认返回最新的10条记录
}

// Visitor 好友的信息
message Visitor {
  UserMiniInfo user = 1; // 用户基础信息
  uint32 visitor_type = 2; // VisitorType
  uint32 water_cnt = 3; // 水滴数
  int64 millisecond = 4; // 时间戳（毫秒）
}

// VisitorType 访问类型
enum VisitorType {
  VisitorType_None = 0; // 无
  VisitorType_Steal = 1; // 好友偷水
  VisitorType_WateringHelp = 2; // 好友浇水
}

// 访客 rsp
message VisitorsRsp {
  repeated Visitor visitors = 1;
  int64 last_flag = 2; // 请求下一页访问的标识,大于0时，可以继续请求下一页
}

// 成长明细
message RecordsReq {
  uint32 page = 1; // 页码 (从1开始,服务器默认返回第一页)
}

// 成长明细
message RecordsRsp {
  repeated Record ll = 1; // 记录列表
  bool is_last = 2; // 是否最后一页
}

// 单个记录
message Record {
  uint32 t = 1; // RecordType
  string tid = 2; // 操作票据
  int64 time = 3; // 操作时间
  repeated RewardInfo rl = 4; // 礼包列表
}

enum RecordType {
  RecordType_None = 0; // 无
  RecordType_TreeUpgrade = 1; // 1.“摇钱树升级”
  RecordType_SignIn = 2; // 2.“签到”
  RecordType_MaxLevel = 3; // 3.“摇钱树满级”
  RecordType_Shake = 4; // 4.“摇晃掉落”
  RecordType_WaterPool = 5; // 5.“蓄水池奖励”
  RecordType_AD = 6; // 6.“水滴广告”
  RecordType_Steal = 7; // 7.“好友偷水”
  RecordType_WaterHelpSelf = 8; // 8.“好友浇水” 自己浇别人
}

// Gm工具 req
message GmSetReq {
  GmSetType type = 1; // 设置类型
  int64 gm_time = 100; // 游戏服务器时间 仅测试服可以使用
  int64 water = 101; // 水滴数量
  uint32 level = 102; // 等级
}

enum GmSetType {
  GmSetType_None = 0; // 无
  GmSetType_AddWater = 1; // 增加水滴
  GmSetType_SubWater = 2; // 减少水滴
  GmSetType_Level = 3; // 等级修改
  GmSetType_LevelAndWater = 4; // 等级+升级所需水滴修改
  GmSetType_ExtraReset = 5; // 扩展字段重置
}

// Gm工具 req
message GmSetRsp {}

message AssetsReq {}

message AssetsRsp {
  repeated component.game.UserAsset assets = 1; // 资产余额
}

// 开关设置 req
//message SwitchSetReq {
//  SwitchSetType type = 1; // 开关类型
//  CommBool state = 2; // 0:关闭,1:开启
//}
//
//enum SwitchSetType {
//  SwitchSetType_None = 0; // 无
//  SwitchSetType_SendMsg = 1; // 私信开关
//}
//
// 开关设置 rsp
//message SwitchSetRsp {}

//message ShareVisitReq {}
//
//message ShareVisitRsp {
//  repeated UserMiniInfo users = 1;
//  map<string, int64> receive_map = 2; // 领取奖励map
//}

//message ShareReceiveReq {
//  string tid = 1; // 票据ID
//}
//
//message ShareReceiveRsp {}
