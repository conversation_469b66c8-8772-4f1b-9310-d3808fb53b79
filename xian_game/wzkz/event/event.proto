syntax = "proto3";

package xian_wzkz;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_game/wzkz/pkg/gen/pb/xian_game/wzkz/event";

// 游戏内部事件类型
enum Event {
  EVENT_INVALID = 0; // 空事件
  EVENT_OPT_CHANGE = 1; // 配置变更
}

message GameEvent {
  uint32 version = 1; // 版本定义, 目前不判断，迭代后从2开始加
  Event evt = 2; // 事件类型
  bytes data = 3; // 事件数据
}

// 配置更新数据
message OptionED {
  string typ = 1; // 配置标识
}
