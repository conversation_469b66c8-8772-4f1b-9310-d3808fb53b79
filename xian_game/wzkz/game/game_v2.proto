syntax = "proto3";

package xian_wzkz;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_game/wzkz/pkg/gen/pb/xian_game/wzkz/game";

import "pb/adapter_unified_assets/adapter_unified_assets/common.proto";

message Error {
  uint32 code = 1; // 执行结果code码
  string msg = 2; // 错误消息
}

// SyncOptReq 同步配置Req
message SyncOptReq {
  string name = 1; // key名
  string data = 2; // 数据
}

// SyncOptReq 同步配置Rsp
message SyncOptRsp {
  string type = 1; // 类型 'success' | 'warning' | 'error';
  string msg = 2; // string;
}

message ExFTGReq {
  string tid = 1; // 实物ID
  uint32 exchange_id = 2; // 兑换ID
  uint32 gift_id = 3; // 类型为FireToGift=>置换的礼包ID
}

message ExFTGRsp {
  Error error = 100;
}

//message ExCTFReq {
//  enum ExCTFType {
//    ExCTFType_None = 0; // 默认
//    ExCTFType_All = 1; // 全部兑换
//  }
//  ExCTFType t = 1; // 兑换类型
//  string tid = 2; // 实物ID
//  uint32 exchange_id = 3; // 兑换ID
//  int64 coin_cnt = 4; // 金币数量
//  int32 scene = 5; // 场景透传上报使用
//}
//
//message ExCTFRsp {
//  Error error = 100;
//}

message ExCTFRecordReq {
  int32 page = 1; // 页码
}

message ExCTFRecordRsp {
  repeated ExCTFRecord ll = 1; // 记录列表
  Error error = 100;
}

message ExCTFRecord {
  int64 tt = 1; // 操作时间
  int64 coin_cnt = 2; // 使用的网赚金币
  int64 fire_cnt = 3; // 获得的火力数量
}

message ExCntReq {}

message ExCntRsp {
  ExCnt ex_info = 1; // 记录列表
  Error error = 100;
}

message ExCnt {
  uint64 rd_all_cnt = 1; // 剩余兑换数量全局每日
  uint64 rd_person_cnt = 2; // 剩余兑换数量个人每日
}

message ExFTGRecordReq {
  int32 page = 1; // 页码
}

message ExFTGRecordRsp {
  repeated ExFTGRecord ll = 1; // 记录列表
  Error error = 100;
}

message ExFTGRecord {
  int64 tt = 1; // 操作时间
  uint32 fire_cnt = 2; // 使用的火力
  GiftInfo gift_info = 3; // 礼物信息
}

message GiftInfo {
  string GIftId = 1; // 礼物编码
  uint32 type = 2; // 类型
  string name = 3; // 名称
  string logo = 4; // logo
  uint32 num = 5; // 数量
  int64 price = 6; // 礼物价值，配置接口有用
  string gift_plat_type = 7; // 透传透传宿主平台子奖品类型
}

message ExFTGListReq {}

message ExFTGListRsp {
  map<int32, ExFTG> mm = 1; // 兑换 兑换ID:兑换详情
  uint64 fire_cnt = 2; // 个人每日消耗的火力
  Error error = 100;
}

message ExFTG {
  uint64 au_cnt = 1; // 使用数量:全局每日 all_used_cnt
  uint64 su_cnt = 2; // 使用数量:个人每日 self_used_cnt
}

message ExFTGInfo {
  uint64 au_cnt = 1; // 使用数量:全局每日 all_used_cnt
  uint64 su_cnt = 2; // 使用数量:个人每日 self_used_cnt
  uint64 fire_cnt = 3; // 个人每日消耗的火力
}

// 票据 req
message TicketReq {}

// 票据 rsp
message TicketRsp {
  string tid = 1; // 事务ID
  Error error = 100;
}

// CoinSubPayReq 扣费金币
message CoinSubPayReq {
  enum ExCTFType {
    ExCTFType_None = 0; // 默认
    ExCTFType_All = 1; // 全部兑换
  }
  ExCTFType t = 1; // 兑换类型
  uint32 exchange_id = 2; // 兑换ID
  int64 coin_cnt = 3; // 金币数量
  string room_id = 4; // 房间号
  string order_id = 101; // 订单id，幂等使用
  int64 pay_amount = 102; // 总价
  adapter_unified_assets.PaySceneInfo pay_scene_info = 103; // 扣费场景信息
}

// 扣费金币
message CoinSubPayRsp {
  int32 status = 1; // 订单状态 =>TradeStatusType
  Error error = 100;
}

// CoinSubResReq 扣费金币
message CoinSubResReq {
  string order_id = 6; // 订单id，幂等使用
}

// 扣费金币
message CoinSubResRsp {
  int32 status = 1; // 订单状态 =>TradeStatusType
  Error error = 100;
}

enum TradeStatusType {
  TS_SubInventory = 0; // 默认状态(初次保存) 清理库存等内部操作
  TS_Pay = 1; // 开启支付
  TS_PayPending = 2; // 支付进行中(Pay接口未明确返回错误)
  TS_PaySuccess = 3; // 支付成功(Pay接口明确返回OK) 开始真正扣除库存
  TS_PayError = 4; // 支付失败(Pay接口明确返回失败)
  TS_PayUnkown = 5; // 支付网络失败
  TS_SendGoodsSuccess = 6; // 火力发放
  TS_End = 20; // 所有流程执行完成
}

message FirstFireReq {
  string tid = 1; // 实物ID
  int32 round_type = 3; // kb场 或者 鲜花场
}

message FirstFireRsp {
  Error error = 100;
}

message AdCheckReq {
  Ad ad = 1; // 广告信息
}

message Ad {
  string ad_token = 1; // 广告token
  string ad_pos_id = 2; // 广告位id
  DeviceInfo device = 3; // 设备信息
}

message DeviceInfo {
  string ip = 1;
  string mac = 2;
  string imei = 3;
  string idfa = 4;
  string idfv = 5;
  uint32 mobileFlag = 6; // 是否来自手机
  string mobleQUA = 7; // qua
  string uuid = 8;
  string udid = 9;
  string qimei36 = 10;
  string deviceInfo = 11;
}

message AdCheckRsp {
  Error error = 100;
}

// 免费火力领取请求
message FreeFireReq {
  string tid = 1; // 实物ID
}

// 免费火力领取响应
message FreeFireRsp {
  uint32 free_fire = 1; // 获得的火力
  Error error = 100;
}
