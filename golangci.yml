# 更多参数，清查询
# https://github.com/golangci/golangci-lint/blob/master/.golangci.example.yml
# 配置使用：https://github.com/uber-go/guide/blob/master/.golangci.yml
# revive推荐配置：https://github.com/mgechev/revive#recommended-configuration

run:
  timeout: 5m
  modules-download-mode: readonly

linters-settings:
  revive:
    ignore-generated-header: false
    severity: warning
    confidence: 0.8
    errorCode: 0
    warningCode: 0
    rules:
      - name: blank-imports
      - name: context-as-argument
      - name: context-keys-type
      - name: dot-imports
      - name: error-return
      - name: error-strings
      - name: error-naming
      - name: exported
      - name: if-return
      - name: increment-decrement
      - name: var-naming
      - name: var-declaration
      - name: package-comments
      - name: range
      - name: receiver-naming
      - name: time-naming
      - name: unexported-return
      - name: indent-error-flow
      - name: errorf
      - name: empty-block
      - name: superfluous-else
      - name: unused-parameter
      - name: unreachable-code
      - name: redefines-builtin-id

linters:
  enable:
    - errcheck
    - goimports
    - revive # 代替golint
    - govet
    - staticcheck
    - gofmt

issues:
  exclude-use-default: false
  max-issues-per-linter: 0
  max-same-issues: 0