syntax = "proto3";

package game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_family";

import "pb/adapter_user/adapter_user.proto";
import "pb/event/platform_event/platform_event.proto";

service FamilyServer {
  rpc BatchGetByFid(BatchGetByFidReq) returns (BatchGetByFidRsp); // 根据家族Id查询指定家族信息
  rpc BatchGetByOpenId(BatchGetByOpenIdReq) returns (BatchGetByOpenIdRsp); // 查询用户所在的家族信息
  rpc Convene(ConveneReq) returns (ConveneRsp); // 私信召集
  rpc RecommandList(RecommandListReq) returns (RecommandListRsp); // 查询推荐家族列表
  rpc Join(JoinReq) returns (JoinRsp); // 加入家族
  rpc ConsumeNotify(ConsumeNotifyReq) returns (ConsumeNotifyRsp); // 接收事件通知, 回调给游戏实现的接口(NotifyEventReq,NotifyEventRsp)
  rpc ReportScore(ReportScoreEventReq) returns (ReportScoreEventRsp); // 给家族加积分
  rpc GroupGetMemberList(GroupGetMemberListReq) returns (GroupGetMemberListRsp); // 分页获取家族成员列表
  rpc FamilyOperate(event.TmePlatformEventReq) returns (event.TmePlatformEventRsp); // 接受云下家族相关操作kafka消息
}

message BatchGetByFidReq {
  string appId = 1;
  string openId = 2;
  repeated uint64 groupIds = 3; // 家族id列表
}

message BatchGetByFidRsp {
  map<uint64, Group> mapInfo = 1;
}

message BatchGetByOpenIdReq {
  string appId = 1;
  repeated string openIds = 2;
  adapter_user.UserIDType idType = 3; //id类型
}

message BatchGetByOpenIdRsp {
  map<string, Group> mapInfo = 1;
}

message ConveneReq {
  string appId = 1; // appId
  string openId = 2; // openId
  uint64 groupId = 3; // 家族id
  string content = 4; // 召集文案
  string jumpUrl = 5; // 跳转链接
  string icon = 6; // 展示图片
  string title = 7; // 标题
  adapter_user.UserIDType idType = 8; //id类型
}

message ConveneRsp {}

message RecommandListReq {
  string appId = 1; // appId
  string openId = 2; // openId
  uint64 num = 3; // 拉取的数量
}

message RecommandListRsp {
  repeated Group groups = 1; // 推荐列表
}

message JoinReq {
  string appId = 1; // appId
  string openId = 2; // openId
  uint64 groupId = 3; // 家族id
  string reason = 4; // 加入理由
}

// 根据grpc code = 0 判断是否申请成功
message JoinRsp {
  uint32 freeEnter = 1; // 如果grpc code == 0且freeEnter == 1, 表示自动加入成功, 否则等审核, 审核通过后通过JoinCallbackReq回调业务
}

message Group {
  GroupBaseInfo base = 1; // 基础信息
}

message GroupBaseInfo {
  uint64 id = 1; // 家族id
  string name = 2; // 家族名称
  string avatar = 3; // 家族头像
  string adminOpenId = 4; // 族长openId
  GroupStatus status = 5; // 家族状态
  uint64 createTime = 6; // 创建时间
  uint32 freeEnter = 7; // 是否是免审加入家族，0不是免审家族，1是免审家族or自动通过
  uint64 memberCount = 8; // 当前家族人数
  uint64 level = 9; // 家族等级
  uint32 userLevelLimit = 10; // 加入用户等级限制
  uint32 userJoinWealthLevelLimit = 11; // 用户加入财富等级限制
}

enum GroupStatus {
  GROUP_STATUS_NORMAL = 0; //默认正常状态
  GROUP_STATUS_DESTORY = 1; //解散状态
  GROUP_STATUS_NO_EXIST = 2; //请求的家族id不存在
  GROUP_STATUS_FROZEN = 3; //因请求家族id的族长家族vip会员过期，导致该家族处于被冻结状态
  GROUP_STATUS_NEED_MODIFY = 4; //家族信息需要修改，导致家族处于冻结状态
  GROUP_STATUS_NO_ACTIVE = 5; //家族不活跃冻结，家族惩罚工具
}

enum GroupNotifyEventType {
  GROUP_NOTIFY_EVENT_TYPE_NONE = 0;
  GROUP_NOTIFY_EVENT_TYPE_JOIN = 7; // 加入
  GROUP_NOTIFY_EVENT_TYPE_LEAVE = 8; // 离开
  GROUP_NOTIFY_EVENT_TYPE_DELETE = 9; // 被踢
}

enum GroupScoreEventType {
  GROUP_EVENT_SCORE_TYPE_TOTAL = 0; // 所有
  GROUP_SCORE_EVENT_TYPE_TASK_COMPLETE = 1; // 任务完成
  GROUP_SCORE_EVENT_TYPE_CONSUME_KB = 2; // 送礼
  GROUP_SCORE_EVENT_TYPE_GAME_COMPLETE = 3; // 游戏获取
}

// 回调通知接口, 全民旅行后台实现接口(JoinEventNotifyReq,JoinEventNotifyRsp)
message NotifyEventReq {
  string appId = 1; // appId
  repeated string openIds = 2; // openIds
  uint64 groupId = 3; // 家族id
  uint32 eventType = 4; // GroupNotifyEventType
}

message NotifyEventRsp {}

message ConsumeNotifyReq {
  string appId = 1; // appId
  repeated uint64 uids = 2; // uids
  uint64 groupId = 3; // 家族id
  uint32 eventType = 4; // GroupNotifyEventType
}

message ConsumeNotifyRsp {}

message ReportScoreEventReq {
  string appId = 1; // appId
  string openId = 2; // openId
  uint64 groupId = 3; // 家族id
  uint64 score = 4; // 积分
  GroupScoreEventType scoreType = 5; // 积分类型
  string consumeId = 6; // 幂等id
  map<string, string> mapExt = 7;
}

// 通过grpc code判断
message ReportScoreEventRsp {}


enum emMemberListType {
  MEMBER_LIST_INVALID = 0;    //无效值
  MEMBER_LIST_APPLY = 1;      //正在申请的用户
  MEMBER_LIST_TOP = 2;        //TOP成员
  MEMBER_LIST_ALL = 3;        //所有成员
  MEMBER_LIST_ADMIN = 4;      //管理员
  MEMBER_LIST_RANDOM = 5;     //随机获取列表
  MEMBER_LIST_FORWARD = 6;    //正序, 时间戳由小到大
  MEMBER_LIST_BACKWARD = 7;   //逆序, 时间戳由大到小
};

message GroupGetMemberListReq {
  string appId = 1;
  emMemberListType listType = 2; //请求类型
  uint32 groupId = 3; // 家族ID
  uint64 limit = 4; // 每次返回数量
  string passback = 5; // 回传参数
  string source = 6; // 访问来源
  adapter_user.UserIDType idType = 7; // 返回的id类型
  bool onlyPlayUsers = 8; // 是否过滤玩过游戏，仅限全部成员使用
}

message GroupMemberInfo {
  string userId = 1;
  string nickName = 2; // 昵称
}

message GroupGetMemberListRsp {
  repeated GroupMemberInfo vecList = 1; //返回结果
  uint32 total = 2;  // 总数量
  uint32 hasMore = 3; // 是否还有更多
  string passback = 4;
}
