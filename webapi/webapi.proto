syntax = "proto3";

package webapi;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/webapi";

service WebApi {
  // 用户实名信息
  rpc UserAuthentication(UserAuthenticationReq) returns (UserAuthenticationRsp);
  // 充值
  rpc Recharge(RechargeReq) returns (RechargeRsp);
  // 防沉迷状态
  rpc Health(HealthReq) returns (HealthRsp);
}

message UserAuthenticationReq {
}

message UserAuthenticationRsp {
  bool isAuth = 1; // 是否实名
  uint32 age = 2; // 年龄
  string authUrl = 3; // 认证连接
  bool isAdult = 4; // 是否成年
}

message RechargeReq {
  int64 num = 1; // 充值金额
}

message RechargeRsp {
}

message HealthReq {
}

message HealthRsp {
  bool isHealth = 1; // 是否可以玩游戏
}

message Account {
  int64 balance = 1; // 余额
  int64 monthValue = 2; // 月份
  int64 monthBalance = 3; // 月充值额度
}
