syntax = "proto3";

package game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/interactive_game_competition/competition_admin";

import "pb/interactive_game_competition/common/competition_config.proto";
import "pb/interactive_game_competition/common/competition_match.proto";

message ReportStageRankReq {
  string matchId = 1;
  int64 stageIndex = 2;
  string roomId = 3;
  repeated common.StageRank rank = 4;
}

message ReportStageRankRsp {
  string matchId = 1;
}

// 概念层级定义:
// Competition: 赛制相关定义
// Match: 轮次相关信息, 如19点场第一轮
// Stage: 回合相关信息, 如19点场第一轮第一局
service GameCompetitionAdmin {
  // (后台)
  rpc ReportStageRank(ReportStageRankReq) returns (ReportStageRankRsp);
}
