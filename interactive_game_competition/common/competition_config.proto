syntax = "proto3";

package game.common;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/interactive_game_competition/common";

// PayMode 付费模式
enum PayMode {
  PayFree = 0;
  PayRequired = 1; // 付费场
  PayFlower = 2; // 鲜花礼物道具场
}

// StageConfig 赛事阶段信息
message StageConfig {
  int32 index = 1; // 阶段序号
  int32 initPlayerNum = 2; // 初始人数
  int32 promotionNum = 3; // 晋级人数
}

// PayConfig 报名付费模式
message PayConfig {
  int32 assetId = 1; // 资产ID
  int32 assetNum = 2; // 报名费
  string modeId = 3; // 模式
  string modeName = 4; // 模式名称
  PayMode payMode = 5; // 付费模式
}

// RobotConfig 机器人配置
message RobotConfig {
  bool enable = 1; // 是否允许机器人加入
  string mode = 2; // 算法模式(default)
}

// OperateConfig 运营配置
message OperateConfig {
  int32 beginDateTime = 1; // 赛事开始时间
  int32 endDateTime = 2; // 赛事结束时间
  int32 beginDayMinute = 3; // 每日开始时间
  int32 endDayMinute = 4; // 每日结束时间
  int32 showAdvanceSeconds = 5; // 开始前x秒可参与报名
  map<string, string> extInfo = 6; // 其他运营配置
  // key枚举:
  // banner: 运营banner图
  // title: 标题
  // sub_title: 副标题

}

message RewardRankItem {
  int32 index = 1; // 名次
  int32 assetNum = 2; // 可发的资产数量
}

// RewardConfig 发奖配置
message RewardConfig {
  int64 assetId = 1; // 奖品资产ID
  repeated RewardRankItem rankRewards = 2; // 每个名次的奖品
  int64 total = 3; // 总奖池信息
}

// TransparentConfig 透传配置
message TransparentConfig {
  map<string, string> transConfig = 1; // 透传配置
  // key: modeId, value: 模式id
  // key: cRoundId, value: 大轮次id, 自动填充
  // key: userHoldTime, value: 10, 拆蛋鹅摸牌停留时长

}

// CompetitionConfig 赛制配置
message CompetitionConfig {
  int64 id = 1; // 配置ID
  string appId = 2; // 小游戏APPID
  int64 initParticipantsNum = 4; // 初始参赛人数
  int32 initSignupWaitSeconds = 5; // 成局等待时长
  int32 stageNum = 6; // 比赛局数
  repeated StageConfig stageInfo = 7; // 赛事阶段配置
  repeated PayConfig payInfo = 8; // 付费配置
  RobotConfig robotInfo = 9; // 机器人配置
  OperateConfig operateInfo = 10; // 运营配置
  TransparentConfig transInfo = 11; // 透传配置
  repeated RewardConfig rewardInfo = 12; // 发奖配置
  bool enable = 13; // 是否上架
  int32 roomSize = 14; // 单房间参赛人数
}
