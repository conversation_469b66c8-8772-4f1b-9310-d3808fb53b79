syntax = "proto3";

package game.common;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/interactive_game_competition/common";

import "pb/interactive_game_competition/common/competition_match.proto";

message SnapshotRunningMatchInfo {
  int64 configId = 1;
  string matchId = 2;
  int64 matchIndex = 3;
  MatchStatus matchStatus = 4;
  int64 minTs = 5;
  int64 maxTs = 6;
}

message SnapshotRunningMatchList {
  repeated SnapshotRunningMatchInfo list = 1;
  int64 ts = 2;
}
