syntax = "proto3";

package component.game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/exchange_admin";

import "google/api/annotations.proto";

service ExchangeAdmin {
  //同步单个活动
  rpc SyncActOne(SyncActOneReq) returns (SyncActOneRsp) {
    option (google.api.http) = {
      post: "/exchange_admin/syncActOne"
      body: "*"
    };
  }
  //同步单条记录
  rpc SyncOne(SyncOneReq) returns (SyncOneRsp) {
    option (google.api.http) = {
      post: "/exchange_admin/syncOne"
      body: "*"
    };
  }
}

message SyncActOneReq {
  //act_id 活动ID
  int64 act_id = 1;
  //平台id
  uint64 plat_id = 2;
}

message SyncActOneRsp {}

message SyncOneReq {
  //exchange_id 兑换ID
  int64 exchange_id = 1;
  //平台id
  uint64 plat_id = 2;
}

message SyncOneRsp {}
