syntax = "proto3";

package pagoda_rank;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/bmp/pagoda_rank";

import "pb/bmp/pagoda_rank/pagoda_rank_sync.proto";
import "pb/adapter_unified_assets/callback/callback.proto";


// 九层宝塔通层榜单api
service PagodaRankApiServer {
  // 配置同步接口
  rpc PagodaRankConfigSync(PagodaRankConfigSyncReq) returns (PagodaRankConfigSyncRsp);
  // 查询榜单接口
  rpc LevelRank(LevelRankReq) returns (LevelRankRsp);
  // 查询挂件接口
  rpc Pendant(PendantReq) returns (PendantRsp);
  // 设置匿名接口
  rpc SetAnonymous(SetAnonymousReq) returns (SetAnonymousRsp);
  // 获取基础配置信息接口
  rpc GetBasicConfigInfo(GetBasicConfigInfoReq) returns (GetBasicConfigInfoRsp);
  // 中台发福利礼包回调检查
  rpc CallbackCheckSend(callback.GiftPackageBusinessCheckSendReq) returns (callback.GiftPackageBusinessCheckSendRsp);
}

message GetBasicConfigInfoReq {
  int64 ActivityID = 1; //活动id
}

message GetBasicConfigInfoRsp {
  uint64 StartTime = 1; // 开始时间
  uint64 EndTime = 2; // 结束时间
  bool CurrentUserIsAnonymous = 3; // 当前登录用户是否匿名
  repeated RankType RankType = 4; // 当前配置排行榜类
}

message UserInfo {
  bool Anonymous = 1; //是否匿名
  string EncryptUin = 2; //uid，q音为加密uin
  string Name = 3; //名字
  string Logo = 4; //头像
  int64 Rank = 5; //排名
  int64 Score = 6; //榜单值（通关层数）
}

message LevelRankReq {
  int64 ActivityID = 1; //活动id
  RankType RankType = 2; //排行榜类型
  bool SecondaryRank = 3; // 是否是二级榜单
  int32 StartIndex = 4; // 首次不传（默认为0） 后续透传后台返回的
  uint64 EndTime = 5; // 时间范围结束
  string EncryptUin = 6; //uid，q音为加密uin，查询二级榜单时需要
}

message LevelRankRsp {
  bool HasMore = 1; //是否还有下一页
  int32 StartIndex = 2; // 后台透传
  UserInfo CurrentUserInfo = 3; // 当前主播信息
  UserInfo ChiefFan = 4; // 首席粉丝信息
  repeated UserInfo RankList = 5; // / 排行榜数据
  int32 ContributeScore = 6; // 助力分数
}

message PendantReq {
  int64 ActivityID = 1; //活动id
  string EncryptUin = 2; //uid，q音为加密uin
}

message PendantRsp {
  int32 Rank = 1; // 当前主播排名
  int64 Score = 2; //榜单值（通关层数）
  int64 DiffScore = 3; // 跟上一名的差值, 第一名则为超下一名
  uint64 StartTime = 4; // 开始时间
  uint64 EndTime = 5; // 结束时间
}

message SetAnonymousReq {
  int64 ActivityID = 1; //活动id
  bool Anonymous = 2; //是否匿名
}

message SetAnonymousRsp {
  int32 Ret = 1;
}
