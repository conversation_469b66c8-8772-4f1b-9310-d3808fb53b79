syntax = "proto3";

package game_packapi;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_packapi";

//打包协议
service Api {
  rpc GamePackapi(GamePackapiReq) returns (GamePackapiRsp);
  rpc GetOpenId(GetOpenIdReq) returns (GetOpenIdRsp);
}

message GamePackapiReq {
  string app_id = 1; // app_id
  string open_id = 2; // 操作 open_id
  string to_open_id = 3; // 动作朝向 open_id
  uint32 cmd_id = 4; // cmd类型 游戏自己定义
  string msg_id = 5; // 消息唯一ID
  uint32 ts = 6; // 时间时间戳
  string payload = 7; // app_id + cmd_id对接的结构体 json.Marshal 之后的结果
}


message GamePackapiRsp {
  string msg_rtn = 1; //回包
}

message GetOpenIdReq {
  string strAppID = 1; // app_id
  int64 lUid = 2; // 用户Id
  int64 lPlatID = 3; // 平台ID
}


message GetOpenIdRsp {
  string strOpenid = 1; //openid
}