syntax = "proto3";

package cwsx_game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/cwsx_game_lava_volvano_api";

enum ActivityStatus {
  ActivityStatusDefault = 0; // 默认 当前没有活动
  ActivityStatusOngoing = 1; // 未参加
  ActivityStatusParticipated = 2; // 参加进行中
  ActivityStatusFinished = 3; // 已完成 已领奖
  ActivityStatusFailed = 4; // 失败惩罚中
  ActivityStatusFinishNotView = 5; // 已完成 未领奖
  ActivityStatusFailEnd = 6; // 已失败 活动结束
}

// 闯关进度
message StageProgress {
  uint32 curStage = 1; // 当前关卡数
  int64 createTime = 2; // 闯关结算时间
}

// CKV+
message UserProgress {
  // user
  uint32 status = 1; // 活动状态
  uint32 startStage = 2; // 开始活动时的关卡数
  repeated StageProgress stageProgress = 3; // 闯关成功进度
  string roundId = 4; // 轮次Id {activityId}_{dailyStart}
  int64 startTime = 5; // 开始时间
  // config
  uint32 maxLevel = 6; // 关卡数
  uint32 maxPlayer = 7; // 最大参与人数
  int64 rewardId = 8; // 奖励礼包Id
  repeated int64 propsIds = 9; // 礼包奖励Id
  uint32 rewardSum = 10; // 奖励总数
  int64 failPunish = 11; // 失败惩罚时间
  int64 roundDuration = 12; // 参与后活动持续时间
  // players
  repeated string successPlayers = 13; // 成功玩家
  repeated string failPlayers = 14; // 失败玩家
  // result
  int64 settleTime = 15; // 结算时间
  uint32 rank = 16; // 结算排名
  uint32 rewardNum = 17; // 奖励数量
  uint32 noticeInterval = 18; // 提醒间隔
  // 主键信息
  map<string,int64> mPlayerIds = 19;
  int64 selfId = 20;
}

message CwsxGameLavaVolvanoConfig {
  int64 id = 1; // 活动id
  uint32 maxLevel = 2; // 最大关卡数
  uint32 maxPlayer = 3; // 最大参与人数
  int64 failPunish = 4; // 失败惩罚时间
  int64 roundDuration = 5; // 参与后活动持续时间
  int64 startTime = 6; // 开始时间
  int64 endTime = 7; // 结束时间
  string dailyStart = 8; // 每日开始时间
  string dailyEnd = 9; // 每日结束时间
  int64 rewardId = 10; // 奖励礼包Id
  uint32 rewardSum = 11; // 奖励总数
  uint32 historyJoinPlayer = 12; // 历史用户
  repeated string whiteOpenIds = 13; // 白名单
  repeated int64 propsIds = 14; // 礼包奖励Id
  uint32 allowStage = 15; // 允许显示关卡
  uint32 noticeInterval = 16; // 提醒间隔
}

message ClaimRecords {
  message Record {
    string avatar = 1;
    string nick = 2;
    string openId = 3;
    uint32 rewardNum = 4;
    uint32 uTs = 5;
  }
  repeated Record records = 1;
}