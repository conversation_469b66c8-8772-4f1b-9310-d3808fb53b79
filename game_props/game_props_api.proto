syntax = "proto3";

package game_props_backend;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_props";

import "pb/game_props/common.proto";

//游戏道具背包协议
service GamePropsApi {
  //获取背包道具列表
  rpc GetUserPropsAsset(GetUserPropsAssetReq) returns (GetUserPropsAssetRsp);
  //增加道具
  rpc AddUserPropsAsset(AddUserPropsAssetReq) returns (AddUserPropsAssetRsp);
  //扣减道具
  rpc SubUserPropsAsset(SubUserPropsAssetReq) returns (SubUserPropsAssetRsp);
  //查询道具信息
  rpc GetPropsInfo(GetPropsInfoReq) returns (GetPropsInfoRsp);
  //批量增加道具
  rpc BatchAddUserPropsAsset(BatchAddUserPropsAssetReq) returns (BatchAddUserPropsAssetRsp);
}

//用户id类型枚举
enum UserIDType {
  OpenIDType = 0;     //使用openID
  UinType = 1;        //使用端内登录态uin
  EncryptUinType =2;      //加密uin
}

message UserIDUnit{
  int32 idType = 1;         //UserIDType 用户id类型;0为openid;1为端内登录态uin
  string userID = 2;        //用户id(参考idType)
}

message GetUserPropsAssetReq {
  string appID = 1;         //游戏ID
  UserIDUnit userInfo =2;   //用户ID信息
  string filterTag =3;      //指定道具标记,空为查所有(可用于过滤周期性道具)
}

message PopUnit{
  int32 id =1;              //id
  string name = 2;          //名称
  int32 num = 3;            //数量
  string pic =4;            //图片  
  int32 lastExpireTs = 5;   //最近一批过期时间
  string filterTag = 6;     //指定标记
  map<string,string> ext = 7; //扩展信息
}

message GetUserPropsAssetRsp {
  repeated PopUnit infoList = 1;  //道具信息
  int32 sysTs =2;                 //系统时间
}

message AddUserPropsAssetReq{
  string appID = 1;         //游戏ID
  UserIDUnit userInfo =2;   //用户ID信息
  string filterTag =3;      //指定标记;可用于周期性道具
  string orderID = 4;       //订单id
  int32 propID = 5;         //道具ID
  int32 num = 6;            //数量
  int64 endTime = 7;        //过期时间
  map<string,string> ext = 8;           //ext扩展信息
}

message AddUserPropsAssetRsp{
  string msg = 1;           //回包信息,ok
  int32 actualAddNum = 2; //本次请求实际增加的数量
  int32 totalNum = 3;      //当前余额数量
}

message SubUserPropsAssetReq{
  string appID = 1;         //游戏ID
  UserIDUnit userInfo =2;   //用户ID信息
  string filterTag =3;      //指定标记;可用于周期性道具
  string orderID = 4;       //订单id
  int32 propID = 5;         //道具ID
  int32 num = 6;            //数量
}

message SubUserPropsAssetRsp{
  string msg = 1;           //回包信息,ok
  int32 totalNum = 2;      //当前余额数量
}

message GetPropsInfoReq{
  repeated int32 propIDList = 1;//道具ID
}

message GetPropsInfoRsp{
  map<int32,PropInfo> infoMap = 1;        //道具信息
}

message AddUnit{
  int32 propID = 1;                     //道具ID
  int32 num = 2;                        //数量
  int64 deadLine =3;                    //过期时间
  map<string,string> ext = 4;           //ext扩展信息
}

message BatchAddUserPropsAssetReq{
  string appID = 1;                     //游戏ID
  UserIDUnit userInfo =2;               //用户ID信息
  string filterTag =3;                  //指定标记;可用于周期性道具
  string orderID = 4;                   //订单id
  repeated AddUnit propList = 5;        //道具列表
}

message AddResult{
  int32 propID = 1;                     //道具ID
  int32 actualAddNum = 2;               //本次请求实际增加的数量
  int32 totalNum = 3;                   //当前余额数量
}

message BatchAddUserPropsAssetRsp{
  string msg = 1;                       //回包信息,ok
  map<int32,AddResult> resultMap = 2;   //propID->增加结果 
}