syntax = "proto3";

package game_props_backend;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_props";

message PropInfo {
  int32 id =1;              //id
  string name = 2;          //名称
  string pic =3;            //图片
  string desc = 4;          //描述
  int32 configID =5;        //配置ID 
}

//游戏背包道具定义(同一天过期的合并)
message PropUnit {
  int32 propID = 1;         //道具ID
  int32 num = 2;            //数量
  int64 deadLine =3;        //过期时间
}

//单个道具id的批次信息
message PropCollect{
  int32 totalNum = 1;             //总数量
  repeated PropUnit units = 2;    //道具批次
  string filterTag = 3;           //标记
  map<string,string> ext = 4;     //扩展信息
}

//单个道具id存的记录列表
message PropItem {
  int32 propID = 1;                  //道具ID
  map<string,PropCollect> coll =3;   //tag,按标记分类
}

//用户背包某个游戏的所有道具(游戏id纬度)
message UserPropsInfo {
  map<int32,PropItem> allProp = 1; //所有道具;id->批次
  map<string,int64> orderTs = 2;   //最近200条订单记录
}

