syntax = "proto3";

package uni_pay;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/uni_pay/common";

enum OperatingSystem {
  OperatingSystemUnknown = 0;
  OperatingSystemAndroid = 1;
  OperatingSystemIOS = 2;
}

enum StatusOfOrder {
  Unknown = 0;
  Placed = 1; // 已下单
  Completed = 2; // 已完成
  Ongoing = 3; // 进行中-check完成
}

// PayScene 货币类型
enum PayScene {
  AppPlatform = 0; // 平台支付:绿钻
  PayPlatform = 1; // 支付中台-人民币支付
  KugouVip = 2; // 酷狗vip
}

message PayFlow {
  string appId = 1;
  string openId = 2;
  int64 businessId = 3; // 业务
  string productId = 4; // 购买商品
  int64 amount = 5; // 价格 单位分
  string orderId = 6; // 游戏订单号
  string platformOrderId = 7; // 平台订单号
  int64 timestamp = 8; // 时间
  uint32 os = 9; // 系统
  uint32 status = 10; // 状态
  int64 currencyType = 11; // 货币类型
  int64 currencyId = 12; // 货币id

  map<string,string> mapExt = 20; // 扩展字段
}
