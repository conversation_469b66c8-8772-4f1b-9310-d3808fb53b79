syntax = "proto3";

package uni_pay;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/uni_pay/callback";

import "pb/uni_pay/common/common.proto";

message CheckOrderReq {
  string appId = 1;
  string openId = 2;
  string productId = 3; // 商品id
  int64 price = 4; // 价格
  int64 greenId = 5; // 货币id
  OperatingSystem os = 6; // 系统
  map<string, string> mapExt = 7;
}

message CheckOrderRsp {
  int64 price = 1; // 价格
  int64 greenId = 2; // 货币id
}

message DeliveryReq {
  string appId = 1;
  string openId = 2;
  string productId = 3; // 商品id
  string transactionId = 4; // 唯一订单id
  int64 timestamp = 5; // 发货时间戳
  OperatingSystem os = 6; // 系统
  map<string, string> mapExt = 7;
}

message DeliveryRsp {}
