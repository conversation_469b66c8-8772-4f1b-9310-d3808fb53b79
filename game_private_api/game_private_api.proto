syntax = "proto3";

package kg.game_private_api;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_private_api";

import "google/api/annotations.proto";

service GamePrivateApi {
  // 同步用户开关事件
  rpc NormalMsgUserSwitchEvent(NormalMsgUserSwitchEventReq) returns (NormalMsgUserSwitchEventRsp) {
    option (google.api.http) = {
      post: "/miniprogram/normal_msg/user_switch_event"
      body: "*"
    };
  }
  // ---- 幸运农场 Start -----------//
  //发送房间消息
  rpc NormalMsgLuckyFarmAnimalEvent(NormalMsgLuckyFarmAnimalEventReq) returns (NormalMsgLuckyFarmAnimalEventRsp) {
    option (google.api.http) = {
      post: "/miniprogram/normal_msg/lucky_farm/animal_event"
      body: "*"
    };
  }
  rpc NormalMsgLuckyFarmLuckyMoment(NormalMsgLuckyFarmLuckyMomentReq) returns (NormalMsgLuckyFarmLuckyMomentRsp) {
    option (google.api.http) = {
      post: "/miniprogram/normal_msg/lucky_farm/lucky_moment"
      body: "*"
    };
  }
  rpc NormalMsgLuckyFarmNoAnimalEvent(NormalMsgLuckyFarmNoAnimalEventReq) returns (NormalMsgLuckyFarmNoAnimalEventRsp) {
    option (google.api.http) = {
      post: "/miniprogram/normal_msg/lucky_farm/no_animal_event"
      body: "*"
    };
  }
  // ---- 幸运农场 End ----------//

  // ---- 惊喜舞会 Start -----------//
  rpc SurpriseDancingGetRoomData(SurpriseDancingGetRoomDataReq) returns (SurpriseDancingGetRoomDataRsp) {
    option (google.api.http) = {
      post: "/miniprogram/normal_msg/surprise_dancing/get_room_data"
      body: "*"
    };
  }
  rpc SurpriseDancingGetLiveStatus(SurpriseDancingGetLiveStatusReq) returns (SurpriseDancingGetLiveStatusRsp) {
    option (google.api.http) = {
      post: "/miniprogram/normal_msg/surprise_dancing/get_live_status"
      body: "*"
    };
  }
  // ---- 惊喜舞会 End ----------//

}

// 生成图片信息
message NormalMsgLuckyFarmAnimalEventReq {
  string app_id = 1;
  string seq_id = 2;
  repeated string animal_ids = 3;
}

message NormalMsgLuckyFarmAnimalEventRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message NormalMsgUserSwitchEventReq {
  string app_id = 1;
  uint64 uid = 2;
  bool is_open = 3;
  uint32 sw_type = 4; // 脚手架类型
}

message NormalMsgUserSwitchEventRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message NormalMsgLuckyFarmLuckyMomentReq {
  string app_id = 1;
  string seq_id = 2;
  string lucky_text = 3;
  // 新增狂欢时刻接口
  uint32 join_num = 4; // 参与人数
  uint32 flower_num = 5; // 鲜花数量
}

message NormalMsgLuckyFarmLuckyMomentRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message LuckyFarmNoAnimalCount {
  string animal_id = 1;
  uint32 animal_count = 2;
}

// 幸运牧场: 没有出现怪兽信息
message NormalMsgLuckyFarmNoAnimalEventReq {
  string app_id = 1;
  string seq_id = 2;
  repeated LuckyFarmNoAnimalCount animal_info = 3;
}

message NormalMsgLuckyFarmNoAnimalEventRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

// 惊喜舞会 Start

enum RoomSourceType {
  ROOM_SOURCE_TYPE_NONE = 0;
  ROOM_SOURCE_TYPE_LIVE = 1; //直播
  ROOM_SOURCE_TYPE_FRIEND_KTV = 2; //好友歌房
  ROOM_SOURCE_TYPE_KTV = 3; //单麦歌房
  ROOM_SOURCE_TYPE_MUTIL_KTV = 4; //多麦歌房
}

// 获取数据详细信息
message UserInfo {
  string nick = 1;
  string avatar = 2;
  uint64 uid = 3;
}

message RoomInfo {
  string room_id = 1;
  string room_name = 2;
  string face_url = 3;
  UserInfo anchor = 4;
  RoomSourceType source_type = 5;
}

message SurpriseDancingGetRoomDataReq {
  string app_id = 1;
  uint32 num = 2;
}

message SurpriseDancingGetRoomDataRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  repeated RoomInfo man_anchor = 3;
  repeated RoomInfo woman_anchor = 4;
  repeated RoomInfo ktv_room = 5;
}

message SurpriseDancingGetLiveStatusReq {
  string app_id = 1;
  repeated string room_id_list = 2;
}

message SurpriseDancingGetLiveStatusRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  map<string, bool> map_status = 3;
}

// 惊喜舞会 End
