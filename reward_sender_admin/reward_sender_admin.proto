syntax = "proto3";

package component.game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/reward_sender_admin";

import "google/api/annotations.proto";

service RewardSenderAdmin {
  //插入更新
  rpc Upsert(UpsertReq) returns (UpsertRsp) {
    option (google.api.http) = {
      post: "/reward/upsert"
      body: "*"
    };
  }
  //预同步
  rpc PreSync(PreSyncReq) returns (PreSyncRsp) {
    option (google.api.http) = {
      post: "/reward/preSync"
      body: "*"
    };
  }
  //体验环境同步单条记录
  rpc SyncOneExp(SyncOneExpReq) returns (SyncOneExpRsp) {
    option (google.api.http) = {
      post: "/reward/syncOneExp"
      body: "*"
    };
  }
  //外网环境同步单条记录
  rpc SyncOneProd(SyncOneProdReq) returns (SyncOneProdRsp) {
    option (google.api.http) = {
      post: "/reward/syncOneProd"
      body: "*"
    };
  }
}

message UpsertReq {
  //reward_id 奖励ID
  int64 reward_id = 1;
  //type type:1 插入 type:2 更新
  uint32 type = 2;
  //RewardConfigAdmin 配置config
  RewardConfigAdmin config = 3;
  //更新人
  string update_user = 4;
}

message UpsertRsp {
  //reward_id 奖励ID
  int64 reward_id = 1;
}

message PreSyncReq {
  //reward_id 奖励ID
  int64 reward_id = 1;
  //更新人
  string update_user = 2;
}

message PreSyncRsp {}

message SyncOneExpReq {
  //reward_id 奖励ID
  int64 reward_id = 1;
  //更新人
  string update_user = 2;
}

message SyncOneExpRsp {}

message SyncOneProdReq {
  //reward_id 奖励ID
  int64 reward_id = 1;
  //更新人
  string update_user = 2;
}

message SyncOneProdRsp {}

message SubRewardAdmin {
  // sub_gift_id 子奖品ID
  string sub_gift_id = 1;
  // sub_gift_type 子奖品Type
  string sub_gift_type = 2;
  // sub_gift_num 子奖品数量
  int64 sub_gift_num = 3;
  // sub_gift_name 子奖品名称
  string sub_gift_name = 4;
  // sub_gift_logo 子奖品logo
  string sub_gift_logo = 5;
  // sub_gift_modify_name 修改后子奖品名称
  string sub_gift_modify_name = 6;
  // sub_gift_modify_logo 修改后子奖品logo
  string sub_gift_modify_logo = 7;
  // sub_gift_unit_price 子奖品单价
  uint32 sub_gift_unit_price = 8;
  // sub_gift_type_name 子奖品类型名
  string sub_gift_type_name = 9;
  // expire_type 过期类型, 1相对过期, 2绝对过期, 0表示不过期
  uint32 sub_gift_expire_type = 10;
  // expire_sec 过期时间(s), 相对过期是x秒后过期, 绝对时间是过期时间戳
  uint32 sub_gift_expire_time = 11;
}

message RewardAdmin {
  // gift_id 奖品ID
  string gift_id = 1;
  // gift_type 奖品Type 参考GiftType
  uint32 gift_type = 2;
  // gift_num 发放数量
  int64 gift_num = 3;
  // gift_reason 发放理由
  string gift_reason = 4;
  // SubRewardAdmin 子奖品信息, 如无子奖品，则为主奖品
  repeated SubRewardAdmin sub_gift = 5;
  // goods_service 回调服务
  string goods_service = 6;
}

message RewardConfigAdmin {
  repeated RewardAdmin rewards = 1;
  string reward_name = 2;
  string game_appid = 3;
  uint64 plat_id = 4;
  string msg_content = 5;
  string msg_link_name = 6;
  string msg_link_url = 7;
}

message RewardListAdmin {
  map<int64, RewardConfigAdmin> rewardInfo = 1;
}
