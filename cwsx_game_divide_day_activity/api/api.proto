syntax = "proto3";

package cwsx_game_divide_day_activity;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/cwsx_game_divide_day_activity";

import "pb/cwsx_game_divide_day_activity/common/common.proto";
import "pb/device/device.proto";

service Api {
  // 查询活动页面
  rpc ActivityPage(ActivityPageReq) returns (ActivityPageRsp);
  // 领取奖励
  rpc ClaimReward(ClaimRewardReq) returns (ClaimRewardRsp);
}

message ActivityPageReq {}

message ActivityPageRsp {
  int32 version = 1; // 当前活动版本
  int32 status = 2; // 活动状态 参考 ActivityStatus
  int32 targetAmount = 3; // 目标金额(分)
  int32 rechargeAmount = 4; // 当前充值金额（分）
  string banner = 5; // banner图
  string description = 6; // 描述
  string rule = 7; // 规则
  repeated common.DayCard dayCard = 8; // 每日礼包
}

message ClaimRewardReq {
  int32 dayCardType = 1; // 日期类型
  device.Device device = 2;
}

message ClaimRewardRsp {
  int32 dayCardType = 1;
}
