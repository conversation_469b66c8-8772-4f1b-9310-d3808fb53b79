syntax = "proto3";

package game_im;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/im";

import "pb/im/im_write_wrap.proto";

service write {
  rpc WriteIm(SendMsgReq) returns (SendMsgRsp);
  rpc CommSendMsg(SendMsgWrapReq) returns (SendMsgWrapRsp);
}


// SendMsgReq 使用，业务侧消息不应该使用此消息类型
enum emImMsgType
{
    IM_MSG_TYPE_GROUP = 0; //群消息
    IM_MSG_TYPE_C2C = 1; //C2C消息
    IM_MSG_TYPE_GROBAL = 2; //全局消息，比如大喇叭
}


// SendMsgReq 使用，业务侧消息不应该使用此消息类型
enum emImMsgSubType
{
    //IM_MSG_TYPE_GROUP 对应的子类型
    IM_MSG_SUBTYPE_GROUP_NORMAL = 0; //普通消息
    IM_MSG_SUBTYPE_GROUP_GIFT = 1; //礼物消息
    IM_MSG_SUBTYPE_GROUP_CONTROL = 2; //信令消息

    //IM_MSG_TYPE_C2C 对应的子类型
    IM_MSG_SUBTYPE_C2C_DEFAULT = 3; //消息
    IM_MSG_SUBTYPE_C2C_FORCELOGOUT = 4; //底层强制退出消息

    //IM_MSG_TYPE_GROBAL 对应的子类型
    IM_MSG_SUBTYPE_GROBAL_DEFAULT = 5;//消息
}

message ImMsg {
  string msgId = 1;         //消息id，保持唯一
  int32 msgType = 2;        //消息主类型emImMsgType
  int32 msgSubType = 3;     //消息子类型emImMsgSubType
  bytes content = 4;        //消息体
  int32 canLost = 5;        //为0表示不允许丢失，比如礼物消息、信令消息等
  int64 targetUid = 6;      //目标uid，c2c消息需要填写
  string remark = 7;        //业务标识信息
  int64 microsecondTs = 8;  //消息发送的微秒时间戳
  string openId = 9;        //目标openid，c2c消息需要填写，仅targetUid=0时才会生效
}

message SendMsgReq {
  string groupId = 1;   //房间ID
  ImMsg msg = 2;        //消息
}

message SendMsgRsp {
  string groupId = 1;   //房间ID
}

message WebMessageItem
{
	string strMsgId = 1;  	        //消息唯一ID
	int64 iCreateTime = 2;  	        //消息创建时间
	int32 iMsgType = 3;                //消息类型，定义见room_im_comm_define.jce的emImMsgType
	int32 iMsgSubType = 4;             //消息子类型，定义见room_im_comm_define.jce的emImMsgSubType
	string content = 5;              //消息内容
}
message WebGetMessageReq
{
	string strKGroupId = 1;          //房间ID
	int64 uid = 2; 				    //用户id
	string passback = 3;             //首次不填，后面透传后台返回的passback字段
	int32 iRole = 4; 			        //用户角色, 定义见room_im_comm_define.jce的emImRoleMask
	int64 lClientSeq = 5; 			//客户端seq,快速地从房间1切到房间2再切到房间1,这样可能串消息
	int32 iIsInitReq = 6;          //第一次进房,只想获取passback不想挂起等待消息(passback为空 + iIsInitReq==1时生效)
	int32 iNeedHistory = 7;        //iNeedHistory=1后台下发进房前的聊天消息
	string strOpenId = 8;            //用户openid，仅uid为0时起效
}

message WebGetMessageRsp
{
	string strKGroupId = 1;              //房间ID
	repeated WebMessageItem vecMsg = 2;  	//消息数组
	int32 interval_ms = 3; 		        //客户端从收到消息到下一次拉取消息的间隔，单位ms。如果这个值是0，那就是长轮询，后台一回包，客户端就重新拉取。如果这个值非0，那就是定时轮询。
	int32 timeout_ms = 4; 		    //非wifi,客户端下一次拉取消息的超时时间，单位ms。由后台来灵活控制超时时间，避免客户端因为超时太长而导致出现卡住现象。
	string passback = 5;     	        //首次不填，后面透传后台返回的passback字段
	int64 lClientSeq = 6; 			    //客户端seq,快速地从房间1切到房间2再切到房间1,这样可能串消息
	int32 timeout_wifi_ms = 7; 		//wifi,客户端下一次拉取消息的超时时间，单位ms。由后台来灵活控制超时时间，避免客户端因为超时太长而导致出现卡住现象。
}