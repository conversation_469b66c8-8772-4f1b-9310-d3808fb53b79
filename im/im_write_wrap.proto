syntax = "proto3";

package game_im;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/im";


enum emBusinessMsgType
{
    BUSINESS_MSG_TYPE_DEFAULT = 0; //默认消息
    BUSINESS_MSG_TYPE_SQUADRON = 1; //群消息
}



// BUSINESS_MSG_TYPE_SQUADRON
enum emSquadronMsgSubType
{
    IM_MSG_SQUADRON_SUBTYPE_NORMAL = 0; //普通消息
}

message User {
    string openId = 1;
}

message RoomMsg {
  string roomId = 1;        //房间id
  string msgId = 2;         //消息id，保持唯一(业务侧可写可不写，不写的话im侧自行生成)
  int32 msgType = 3;        //消息主类型emBusinessMsgType
  int32 msgSubType = 4;     //消息子类型
  string content = 5;       //消息内容
  User sender = 6;          //发送人
  User receiver = 7;        //接受人
  int64 microsecond = 8;  //消息发送的微秒时间戳
  int64 seq = 9;          //消息序列号,roomid纬度递增,业务侧不写
}


message SendMsgWrapReq {
    RoomMsg msg = 1;
    int32 sendType = 2;           //发送类型，0默认房间组播，1单播c2c
}

message SendMsgWrapRsp {
    string roomId = 1;
}


message GetCommMsgReq {
    string roomId = 1;
    string openId = 2;            //用户openid
    string passback = 3;             //首次不填，后面透传后台返回的passback字段
}

message GetCommMsgRsp {
    repeated RoomMsg vecMsg = 1;  	//消息数组
    int32 interval_ms = 2; 		        //客户端从收到消息到下一次拉取消息的间隔，单位ms。如果这个值是0，那就是长轮询，后台一回包，客户端就重新拉取。如果这个值非0，那就是定时轮询。
    int32 timeout_ms = 3; 		       //客户端下一次拉取消息的超时时间，单位ms。由后台来灵活控制超时时间，避免客户端因为超时太长而导致出现卡住现象。
	string passback = 4;     	        //首次不填，后面透传后台返回的passback字段
}



message GetHistoryMsgReq {
    string roomId = 1;
    string openId = 2;            //用户openid
    string passback = 3;          //首次不填，后面透传后台返回的passback字段
    string msgid = 4;             //如果是拉老消息，为本地最老的一条消息msgid/如果拉新消息，为本地最新一条消息的msgid,如果本地没消息，穿空
    int32 oldMsg = 5;             //0拉新消息，1拉老消息
    int32 count = 6;              //拉取消息条数   
}

message GetHistoryMsgRsp {
    repeated RoomMsg vecMsg = 1;  	//消息数组
	string passback = 2;     	    
}