syntax = "proto3";

package rte.common.coin_dozer;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/coin_dozer_common";

// 物品信息
message GoodsInfo {
  //id
  uint32 id = 1;
  //数量
  uint32 num = 2;
}

message MessageInfo {
  // 推币机ID
  string machine_id = 1;
  // 协议ID
  string cmd = 2;
  // 数据
  string data = 3;
}

message CoordinateInfo {
  // 屏幕宽度
  uint32 width = 1;
  // x坐标
  uint32 position_x = 2;
}
