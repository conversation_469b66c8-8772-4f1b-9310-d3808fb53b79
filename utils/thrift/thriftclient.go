package thrift

import (
	"context"
	"errors"
	"fmt"
	"git.kugou.net/fxgo/core/config"
	"git.kugou.net/fxgo/core/logger"
	"net/http"

	"github.com/apache/thrift/lib/go/thrift"
)

var client *http.Client

func catClientMiddleware(name string) thrift.ClientMiddleware {
	return func(next thrift.TClient) thrift.TClient {
		return thrift.WrappedTClient{
			Wrapped: func(ctx context.Context, method string, args, result thrift.TStruct) (thrift.ResponseMeta, error) {

				logger.Infof("call name:%s, args:%s, result:%s", name, args, result)
				call, err := next.Call(ctx, method, args, result)
				if err != nil {
					logger.Errorf("trift call TException name:%s, args:%s, result:%s, error: %v", name, args, result, err)
				}
				return call, err
			},
		}
	}
}

func getStandardClient(addr string, headers map[string]string) (*thrift.TStandardClient, error) {

	var transport thrift.TTransport
	var err error

	transport, err = thrift.NewTHttpClientWithOptions(addr, thrift.THttpClientOptions{
		Client: client,
	})

	if err != nil {
		return nil, fmt.Errorf("error new http mycontext : %w", err)
	}

	if len(headers) > 0 {
		for k, v := range headers {
			transport.(*thrift.THttpClient).SetHeader(k, v)
		}
	}
	//protocol
	protocolFactory := thrift.NewTBinaryProtocolFactoryConf(&thrift.TConfiguration{
		TBinaryStrictRead:  thrift.BoolPtr(true),
		TBinaryStrictWrite: thrift.BoolPtr(true),
	})
	//protocolFactory := thrift.NewTBinaryProtocolFactory(true, true)

	//no buffered
	transportFactory := thrift.NewTTransportFactory()

	transport, err = transportFactory.GetTransport(transport)
	if err != nil {
		return nil, fmt.Errorf("error get transport : %w", err)
	}

	if err := transport.Open(); err != nil {
		return nil, fmt.Errorf("error open transport : %w", err)
	}

	i := protocolFactory.GetProtocol(transport)
	o := protocolFactory.GetProtocol(transport)
	return thrift.NewTStandardClient(i, o), nil
}

func GetTClientKgProxy[T any](newClient func(thrift.TClient) T, serviceName string) (client T, err error) {

	var callUrl string
	serviceCfg, ok := config.Get().ThriftClient[serviceName]

	if !ok {
		err = errors.New("ThriftClient配置不存在")
		return
	}

	headers := make(map[string]string, 2)
	headers["R_servername"] = serviceCfg.Name
	headers["KgrpcHost"] = serviceCfg.Host

	callUrl = "http://" + config.Get().Thrift.ProxyHost + "/kgrpc_proxy" + serviceCfg.Uri

	logger.Info(callUrl)
	standardClient, err := getStandardClient(callUrl, headers)
	if err != nil {
		return
	}

	client = newClient(thrift.WrapClient(standardClient, catClientMiddleware(serviceName+":"+serviceName+":"+serviceCfg.Uri)))
	return
}
