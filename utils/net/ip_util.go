// Description: IP地址工具函数
package net

import (
	"fmt"
	"net"
)

// IsValidIP 检查一个字符串是否代表一个有效的IP地址（IPv4或IPv6）
func IsValidIP(ipStr string) bool {
	return net.ParseIP(ipStr) != nil
}

// IPToString 将net.IP类型的IP地址转换为字符串表示
func IPToString(ip net.IP) string {
	if ip == nil {
		return ""
	}
	return ip.String()
}

// StringToIP 将字符串表示的IP地址转换为net.IP类型
func StringToIP(ipStr string) (net.IP, error) {
	return net.ParseIP(ipStr), nil
}

// IPToUint32 将IPv4地址转换为32位无符号整数（仅适用于IPv4）
func IPToUint32(ip net.IP) (uint32, error) {
	if ip == nil || ip.To4() == nil {
		return 0, ErrInvalidIPType
	}
	return ipv4ToInt(ip.To4())
}

// Uint32ToIP 将32位无符号整数转换回IPv4地址（仅适用于IPv4）
func Uint32ToIP(ipUint32 uint32) net.IP {
	return intToIPv4(ipUint32)
}

// ErrInvalidIPType 是在尝试对IPv6地址执行IPv4特定操作时返回的错误
var ErrInvalidIPType = fmt.Errorf("invalid IP type: expected IPv4")

// ipv4ToInt 将IPv4地址转换为32位无符号整数
func ipv4ToInt(ip net.IP) (uint32, error) {
	if ip4 := ip.To4(); ip4 == nil {
		return 0, ErrInvalidIPType
	}
	return binaryIPv4ToInt(ip)
}

// intToIPv4 将32位无符号整数转换回IPv4地址
func intToIPv4(ipUint32 uint32) net.IP {
	ipBytes := make(net.IP, net.IPv4len)
	binaryIntToIPv4(ipUint32, ipBytes)
	return ipBytes
}

// binaryIPv4ToInt 将IPv4地址的字节表示转换为32位无符号整数
func binaryIPv4ToInt(ip net.IP) (uint32, error) {
	if len(ip) != net.IPv4len {
		return 0, ErrInvalidIPType
	}
	return uint32(ip[0])<<24 + uint32(ip[1])<<16 + uint32(ip[2])<<8 + uint32(ip[3]), nil
}

// binaryIntToIPv4 将32位无符号整数转换为IPv4地址的字节表示
func binaryIntToIPv4(ipUint32 uint32, ip net.IP) {
	ip[0] = byte(ipUint32 >> 24)
	ip[1] = byte(ipUint32 >> 16)
	ip[2] = byte(ipUint32 >> 8)
	ip[3] = byte(ipUint32)
}

// IpExampleUsage 展示了如何使用iputil包
func IpExampleUsage() {
	// 示例：检查IP地址的有效性
	ipStr := "***********"
	if IsValidIP(ipStr) {
		fmt.Printf("IP地址 '%s' 是有效的。\n", ipStr)
	} else {
		fmt.Printf("IP地址 '%s' 是无效的。\n", ipStr)
	}

	// 示例：将IP地址转换为字符串
	ip := net.ParseIP("2001:db8::68")
	if ip != nil {
		fmt.Printf("IP地址的字符串表示：%s\n", IPToString(ip))
	}

	// 示例：将字符串转换为IP地址
	parsedIP, err := StringToIP("*******")
	if err == nil {
		fmt.Printf("解析后的IP地址：%s\n", parsedIP)
	}
}
