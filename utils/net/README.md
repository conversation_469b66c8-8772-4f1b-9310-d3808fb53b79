# 网络工具包

本包提供了一系列网络相关的工具方法，主要用于获取客户端真实IP地址。

## 功能特性

- 🌐 支持多种代理和负载均衡器场景
- 🔍 智能检测多个HTTP头字段
- ✅ IP地址格式验证
- 🚀 高性能实现
- 🛡️ 安全过滤（过滤本地和无效IP）

## 主要方法

### GetClientIP(r *http.Request) string

获取客户端真实IP地址的完整版本，支持以下HTTP头：

- `X-Forwarded-For` (最高优先级)
- `X-Real-IP`
- `X-Client-IP`
- `X-Forwarded`
- `X-Cluster-Client-IP`
- `Forwarded-For`
- `Forwarded`

**使用示例：**
```go
func handler(w http.ResponseWriter, r *http.Request) {
    clientIP := net.GetClientIP(r)
    fmt.Printf("客户端IP: %s\n", clientIP)
}
```

### GetClientIPSimple(r *http.Request) string

简化版本，只检查最常用的HTTP头：
- `X-Forwarded-For`
- `X-Real-IP`

**使用示例：**
```go
func handler(w http.ResponseWriter, r *http.Request) {
    clientIP := net.GetClientIPSimple(r)
    fmt.Printf("客户端IP: %s\n", clientIP)
}
```

### GetClientIPWithDefault(r *http.Request, defaultIP string) string

带默认值的版本，当无法获取有效IP时返回指定的默认值。

**使用示例：**
```go
func handler(w http.ResponseWriter, r *http.Request) {
    clientIP := net.GetClientIPWithDefault(r, "0.0.0.0")
    fmt.Printf("客户端IP: %s\n", clientIP)
}
```

## 使用场景

### 1. 日志记录

```go
func LoggingMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        clientIP := net.GetClientIP(r)
        log.Printf("[%s] %s - IP: %s", r.Method, r.URL.Path, clientIP)
        next.ServeHTTP(w, r)
    })
}
```

### 2. 访问控制

```go
func SecurityMiddleware(allowedIPs []string) func(http.Handler) http.Handler {
    allowedIPMap := make(map[string]bool)
    for _, ip := range allowedIPs {
        allowedIPMap[ip] = true
    }
    
    return func(next http.Handler) http.Handler {
        return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
            clientIP := net.GetClientIP(r)
            if !allowedIPMap[clientIP] {
                http.Error(w, "Access denied", http.StatusForbidden)
                return
            }
            next.ServeHTTP(w, r)
        })
    }
}
```

### 3. 限流控制

```go
type IPRateLimiter struct {
    requests map[string]int
    limit    int
}

func (rl *IPRateLimiter) Middleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        clientIP := net.GetClientIP(r)
        rl.requests[clientIP]++
        if rl.requests[clientIP] > rl.limit {
            http.Error(w, "Rate limit exceeded", http.StatusTooManyRequests)
            return
        }
        next.ServeHTTP(w, r)
    })
}
```

## 代理和负载均衡器支持

本工具包支持以下常见的代理和负载均衡器：

- **Nginx**: 通过 `X-Real-IP` 和 `X-Forwarded-For`
- **Apache**: 通过 `X-Forwarded-For`
- **Cloudflare**: 通过 `CF-Connecting-IP` (可扩展)
- **AWS ALB/ELB**: 通过 `X-Forwarded-For`
- **Google Cloud Load Balancer**: 通过 `X-Forwarded-For`

## 安全考虑

1. **IP验证**: 自动过滤无效和本地IP地址
2. **头部优先级**: 按照安全性排序检查HTTP头
3. **格式验证**: 使用 `net.ParseIP` 验证IP格式
4. **防伪造**: 优先使用可信的HTTP头

## 性能

- 使用高效的字符串操作
- 避免不必要的内存分配
- 支持基准测试验证性能

运行性能测试：
```bash
go test -bench=. -benchmem
```

## 注意事项

1. **代理配置**: 确保代理服务器正确设置了相关HTTP头
2. **信任边界**: 只信任来自可信代理的HTTP头
3. **IPv6支持**: 完全支持IPv6地址格式
4. **私有IP**: 根据业务需求决定是否接受私有IP地址

## 测试

运行单元测试：
```bash
go test -v
```

运行覆盖率测试：
```bash
go test -cover
```
