package net

import (
	"fmt"
	"net/http"
)

// ExampleHandler 示例处理器，展示如何在HTTP处理器中使用IP获取工具
func ExampleHandler(w http.ResponseWriter, r *http.Request) {
	// 方式1: 使用完整版本的GetClientIP（推荐）
	clientIP := GetClientIP(r)
	fmt.Printf("客户端IP (完整版): %s\n", clientIP)
	
	// 方式2: 使用简化版本的GetClientIPSimple
	clientIPSimple := GetClientIPSimple(r)
	fmt.Printf("客户端IP (简化版): %s\n", clientIPSimple)
	
	// 方式3: 使用带默认值的版本
	clientIPWithDefault := GetClientIPWithDefault(r, "0.0.0.0")
	fmt.Printf("客户端IP (带默认值): %s\n", clientIPWithDefault)
	
	// 在响应中返回IP信息
	w.Header().Set("Content-Type", "application/json")
	response := fmt.Sprintf(`{
		"client_ip": "%s",
		"client_ip_simple": "%s",
		"client_ip_with_default": "%s",
		"headers": {
			"x_forwarded_for": "%s",
			"x_real_ip": "%s",
			"remote_addr": "%s"
		}
	}`, clientIP, clientIPSimple, clientIPWithDefault,
		r.Header.Get("X-Forwarded-For"),
		r.Header.Get("X-Real-IP"),
		r.RemoteAddr)
	
	w.Write([]byte(response))
}

// LoggingMiddleware 日志中间件示例，记录客户端IP
func LoggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		clientIP := GetClientIP(r)
		fmt.Printf("[%s] %s %s - Client IP: %s\n", 
			r.Method, r.URL.Path, r.Proto, clientIP)
		
		// 将IP添加到请求上下文中，供后续处理器使用
		// 这里可以使用context.WithValue来传递IP
		
		next.ServeHTTP(w, r)
	})
}

// SecurityMiddleware 安全中间件示例，基于IP进行访问控制
func SecurityMiddleware(allowedIPs []string) func(http.Handler) http.Handler {
	allowedIPMap := make(map[string]bool)
	for _, ip := range allowedIPs {
		allowedIPMap[ip] = true
	}
	
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			clientIP := GetClientIP(r)
			
			// 检查IP是否在允许列表中
			if !allowedIPMap[clientIP] {
				http.Error(w, "Access denied", http.StatusForbidden)
				return
			}
			
			next.ServeHTTP(w, r)
		})
	}
}

// RateLimitByIP IP限流示例（简化版）
type IPRateLimiter struct {
	requests map[string]int
	limit    int
}

func NewIPRateLimiter(limit int) *IPRateLimiter {
	return &IPRateLimiter{
		requests: make(map[string]int),
		limit:    limit,
	}
}

func (rl *IPRateLimiter) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		clientIP := GetClientIP(r)
		
		// 简化的限流逻辑（实际应用中需要考虑时间窗口）
		rl.requests[clientIP]++
		if rl.requests[clientIP] > rl.limit {
			http.Error(w, "Rate limit exceeded", http.StatusTooManyRequests)
			return
		}
		
		next.ServeHTTP(w, r)
	})
}

// GetClientLocation 根据IP获取客户端位置信息（示例接口）
func GetClientLocation(r *http.Request) map[string]string {
	clientIP := GetClientIP(r)
	
	// 这里可以集成第三方IP地理位置服务
	// 比如 MaxMind GeoIP, IP2Location 等
	location := map[string]string{
		"ip":      clientIP,
		"country": "Unknown",
		"city":    "Unknown",
		"region":  "Unknown",
	}
	
	// 示例：简单的IP段判断（实际应用中应使用专业的GeoIP数据库）
	if clientIP != "unknown" {
		// 这里可以添加实际的地理位置查询逻辑
		location["country"] = "China" // 示例
		location["city"] = "Beijing"  // 示例
	}
	
	return location
}

// ValidateClientIP 验证客户端IP是否合法
func ValidateClientIP(r *http.Request) bool {
	clientIP := GetClientIP(r)
	return clientIP != "unknown" && clientIP != ""
}

// GetClientIPInfo 获取客户端IP的详细信息
func GetClientIPInfo(r *http.Request) map[string]interface{} {
	return map[string]interface{}{
		"ip":                GetClientIP(r),
		"ip_simple":         GetClientIPSimple(r),
		"remote_addr":       r.RemoteAddr,
		"x_forwarded_for":   r.Header.Get("X-Forwarded-For"),
		"x_real_ip":         r.Header.Get("X-Real-IP"),
		"x_client_ip":       r.Header.Get("X-Client-IP"),
		"user_agent":        r.Header.Get("User-Agent"),
		"is_valid":          ValidateClientIP(r),
		"location":          GetClientLocation(r),
	}
}
