package net

import (
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestGetClientIP(t *testing.T) {
	tests := []struct {
		name     string
		headers  map[string]string
		expected string
	}{
		{
			name: "X-Forwarded-For with single IP",
			headers: map[string]string{
				"X-Forwarded-For": "*************",
			},
			expected: "*************",
		},
		{
			name: "X-Forwarded-For with multiple IPs",
			headers: map[string]string{
				"X-Forwarded-For": "***********, *************, ********",
			},
			expected: "***********",
		},
		{
			name: "X-Real-IP",
			headers: map[string]string{
				"X-Real-IP": "***********",
			},
			expected: "***********",
		},
		{
			name: "X-Client-IP",
			headers: map[string]string{
				"X-Client-IP": "***********",
			},
			expected: "***********",
		},
		{
			name: "Priority test - X-Forwarded-For should win",
			headers: map[string]string{
				"X-Forwarded-For": "***********",
				"X-Real-IP":       "***********",
				"X-Client-IP":     "***********",
			},
			expected: "***********",
		},
		{
			name: "Invalid IP should be skipped",
			headers: map[string]string{
				"X-Forwarded-For": "invalid-ip",
				"X-Real-IP":       "***********",
			},
			expected: "***********",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/", nil)
			
			// 设置测试头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}
			
			result := GetClientIP(req)
			if result != tt.expected {
				t.Errorf("GetClientIP() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestGetClientIPSimple(t *testing.T) {
	tests := []struct {
		name     string
		headers  map[string]string
		expected string
	}{
		{
			name: "X-Forwarded-For",
			headers: map[string]string{
				"X-Forwarded-For": "***********",
			},
			expected: "***********",
		},
		{
			name: "X-Real-IP",
			headers: map[string]string{
				"X-Real-IP": "***********",
			},
			expected: "***********",
		},
		{
			name: "Multiple headers - X-Forwarded-For priority",
			headers: map[string]string{
				"X-Forwarded-For": "***********",
				"X-Real-IP":       "***********",
			},
			expected: "***********",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/", nil)
			
			// 设置测试头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}
			
			result := GetClientIPSimple(req)
			if result != tt.expected {
				t.Errorf("GetClientIPSimple() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestGetClientIPWithDefault(t *testing.T) {
	req := httptest.NewRequest("GET", "/", nil)
	defaultIP := "***********"
	
	result := GetClientIPWithDefault(req, defaultIP)
	
	// 由于没有设置任何头，应该返回默认IP
	if result != defaultIP {
		t.Errorf("GetClientIPWithDefault() = %v, expected %v", result, defaultIP)
	}
}

func TestIsValidIP(t *testing.T) {
	tests := []struct {
		ip       string
		expected bool
	}{
		{"***********", true},
		{"2001:db8::1", true},
		{"***********", true},
		{"127.0.0.1", false},    // loopback
		{"::1", false},          // loopback
		{"localhost", false},    // hostname
		{"invalid-ip", false},   // invalid format
		{"", false},             // empty
		{"unknown", false},      // unknown
	}

	for _, tt := range tests {
		t.Run(tt.ip, func(t *testing.T) {
			result := isValidIP(tt.ip)
			if result != tt.expected {
				t.Errorf("isValidIP(%v) = %v, expected %v", tt.ip, result, tt.expected)
			}
		})
	}
}

// BenchmarkGetClientIP 性能测试
func BenchmarkGetClientIP(b *testing.B) {
	req := httptest.NewRequest("GET", "/", nil)
	req.Header.Set("X-Forwarded-For", "***********, *************")
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GetClientIP(req)
	}
}

func BenchmarkGetClientIPSimple(b *testing.B) {
	req := httptest.NewRequest("GET", "/", nil)
	req.Header.Set("X-Forwarded-For", "***********, *************")
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GetClientIPSimple(req)
	}
}
