package utils

import (
	"bufio"
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"math/rand"
	"net"
	"os"
	"reflect"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/spf13/cast"
)

const LocalIp string = "127.0.0.1"

const letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890"

func ProcessNo() string {
	if os.Getpid() > 0 {
		return strconv.Itoa(os.Getpid())
	}
	return ""
}

func HostName() string {
	if hs, err := os.Hostname(); err == nil {
		return hs
	}
	return "unknown"
}

func OSName() string {
	return runtime.GOOS
}

var allIpV4s []string

func AllIPV4() (ipv4s []string) {

	if len(allIpV4s) > 0 {
		return allIpV4s
	}

	adders, err := net.InterfaceAddrs()
	if err != nil {
		return
	}

	for _, addr := range adders {
		if ipNet, ok := addr.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				ipv4 := ipNet.IP.String()
				if ipv4 == LocalIp || ipv4 == "localhost" {
					continue
				}
				ipv4s = append(ipv4s, ipv4)
			}
		}
	}

	allIpV4s = ipv4s
	return
}

func IPV4() string {
	ipv4s := AllIPV4()
	if len(ipv4s) > 0 {
		return ipv4s[0]
	}
	return "no-hostname"
}

func FindIpV4(prefix string) string {
	adders, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}

	for _, addr := range adders {
		if ipNet, ok := addr.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				ipv4 := ipNet.IP.String()
				if ipv4 == LocalIp || ipv4 == "localhost" {
					continue
				}
				if strings.HasPrefix(ipv4, prefix) {
					return ipv4
				}
			}
		}
	}
	return ""
}

func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

func RemoveDuplicateElement(languages []string) []string {
	result := make([]string, 0, len(languages))
	temp := map[string]struct{}{}
	for _, item := range languages {
		if _, ok := temp[item]; !ok {
			temp[item] = struct{}{}
			result = append(result, item)
		}
	}
	return result
}

func ParseParameter(paramters string) map[string]string {
	mp := make(map[string]string)
	kvs := strings.Split(paramters, "|")
	for _, kv := range kvs {
		p := strings.Split(kv, ":")
		if len(p) == 2 {
			if len(p[1]) != 0 {
				mp[p[0]] = p[1]
			}
		}
	}
	return mp
}

// SIPInfo IP、地区配置
type SIPInfo struct {
	LocalIP string
	Area    string
}

const (
	ipInfoPath = "/usr/local/services/etc/ipinfo"
)

var (
	once   sync.Once
	ipinfo = SIPInfo{LocalIP: LocalIp, Area: "sz"}
)

// GetIPInfo 获取地区信息:sz/sh
func GetIPInfo(cfgPath ...string) (string, string) {
	once.Do(func() {
		p := ipInfoPath
		if len(cfgPath) > 0 && len(cfgPath[0]) > 0 {
			p = cfgPath[0]
		}

		f, err := os.Open(p)
		if err == nil {
			rd := bufio.NewReader(f)
			l, err := rd.ReadString('\n')
			for err == nil {
				if pos := strings.Index(l, "localip="); pos == 0 {
					ipinfo.LocalIP = strings.Trim(l[8:], "\n")
				} else if pos := strings.Index(l, "area="); pos == 0 {
					ipinfo.Area = strings.Trim(l[5:], "\n")
				}
				l, err = rd.ReadString('\n')
			}
		}
	})
	return ipinfo.LocalIP, ipinfo.Area
}

func Min(x, y int) int {
	if x < y {
		return x
	}
	return y
}

func Max(x, y int) int {
	if x > y {
		return x
	}
	return y
}

func IsZeroValue(ptr interface{}) bool {
	// 获取指针指向对象的类型
	val := reflect.ValueOf(ptr).Elem()
	// 获取该类型的零值
	zero := reflect.Zero(val.Type())
	// 比较指针指向的对象和零值是否相等
	return reflect.DeepEqual(val.Interface(), zero.Interface())
}

func If[T any](condition bool, a, b T) T {
	if condition {
		return a
	}
	return b
}

func IfT[T any](condition bool, a, b *T) T {
	if condition {
		return *a
	}
	return *b
}

//func MapWeakDecode(data map[string]string, output interface{}) error {
//	for key, value := range data {
//		CamelKey := strcase.ToCamel(key)
//		data[CamelKey] = value
//	}
//	return mapstructure.WeakDecode(data, output)
//}

func VoToMap(vo interface{}) map[string]interface{} {
	voValue := reflect.ValueOf(vo)
	// 如果是指针，获取指针指向的元素
	if voValue.Kind() == reflect.Ptr {
		voValue = voValue.Elem()
	}
	if voValue.Kind() != reflect.Struct {
		return nil
	}

	result := make(map[string]interface{})
	voType := voValue.Type()

	for i := 0; i < voValue.NumField(); i++ {
		field := voValue.Field(i)
		fieldType := voType.Field(i)
		tag := fieldType.Tag.Get("json")

		key := strings.Split(tag, ",")[0]
		value := field.Interface()

		if value != nil {
			result[key] = value
		}
	}

	return result
}

type StringToInt int

func (sti *StringToInt) UnmarshalJSON(data []byte) error {
	if len(data) == 0 {
		*sti = 0
		return nil
	}
	// 尝试将二进制数组解释为一个整数
	value, err := strconv.Atoi(string(data))
	if err == nil {
		*sti = StringToInt(value)
		return err
	}

	// 将 JSON 数据（字节切片）转换为字符串
	str := string(data)
	// 由于 JSON 值是用双引号包围的字符串，需要去除这些双引号
	str = str[1 : len(str)-1]

	// 尝试将字符串转换为整数
	value, err = strconv.Atoi(str)
	if err != nil {
		return err
	}

	// 设置转换后的整数值
	*sti = StringToInt(value)
	return nil
}

// SplitArrayIntoBatches 切片分批拆分
func SplitArrayIntoBatches[T any](data []T, batchSize int) [][]T {
	if batchSize <= 0 {
		return [][]T{data}
	}

	length := len(data)
	numBatches := (length + batchSize - 1) / batchSize

	batches := make([][]T, numBatches)
	for i := 0; i < numBatches; i++ {
		start := i * batchSize
		end := start + batchSize
		if end > length {
			end = length
		}
		batch := make([]T, end-start)
		for j := start; j < end; j++ {
			batch[j-start] = data[j]
		}
		batches[i] = batch
	}

	return batches
}

func GetIntListSameMember(list []int, list2 []int) []int {
	returnValue := make([]int, 0)
	if len(list) == 0 || len(list2) == 0 {
		return returnValue
	}
	for _, value := range list {
		for _, value2 := range list2 {
			if value == value2 {
				returnValue = append(returnValue, value)
				break
			}
		}
	}
	return returnValue
}

func ExtractRangeFromArray[T any](arr []T, start, end int) ([]T, error) {
	length := len(arr)

	if start < 0 || start >= length {
		return nil, fmt.Errorf("start index out of range")
	}

	if end < start || end >= length {
		return nil, fmt.Errorf("end index out of range")
	}

	return arr[start : end+1], nil
}

func RandomInt32(n int32) int32 {
	return rand.Int31n(n)
}

func RandomInt(n int) int {
	return rand.Intn(n)
}

func RandomStr(length int) string {
	b := make([]byte, length)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

//func GetDistance(lat1, lon1, lat2, lon2 float64) float64 {
//	point1 := geo.NewPoint(lat1, lon1)
//	point2 := geo.NewPoint(lat2, lon2)
//	distance := point1.GreatCircleDistance(point2)
//	return distance
//}

func GetMonthByRoomId(roomId int64) int {
	if roomId <= 0 {
		return 0
	}
	nowMonth := time.Now().Month()
	nowYear := time.Now().Year()
	roomMonth := 0
	roomIdString := cast.ToString(roomId)
	if len(roomIdString) == 9 {
		roomMonth = cast.ToInt(roomIdString[0:1])
	} else {
		roomMonth = cast.ToInt(roomIdString[0:2])
	}
	if roomMonth > int(nowMonth) {
		nowYear = nowYear - 1
	}
	if roomMonth >= 10 {
		return cast.ToInt(cast.ToString(nowYear) + cast.ToString(roomMonth))
	} else {
		return cast.ToInt(cast.ToString(nowYear) + "0" + cast.ToString(roomMonth))
	}
}

func SignWithComma(vo interface{}, alt string) string {
	v := reflect.ValueOf(vo)

	// 如果 v 是一个指针，则解引用它
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	t := v.Type()

	// 获取所有字段的名称
	fieldNames := make([]string, 0, v.NumField())
	for i := 0; i < v.NumField(); i++ {
		fieldNames = append(fieldNames, t.Field(i).Name)
	}

	// 按字段名称排序
	sort.Strings(fieldNames)

	var source strings.Builder

	for _, fieldName := range fieldNames {
		// sign字段不参与加密
		if fieldName == "Sign" {
			continue
		}

		fieldValue := v.FieldByName(fieldName)

		// 处理字段，不过滤零值
		if fieldValue.IsValid() && fieldValue.CanInterface() {
			obj := fieldValue.Interface()
			switch v := obj.(type) {
			case []interface{}:
				// List加密有空格,去除
				source.WriteString(strings.ReplaceAll(fmt.Sprint(v), " ", ""))
			default:
				source.WriteString(fmt.Sprint(v))
			}
			source.WriteString(",")
		}
	}

	// 加盐
	source.WriteString(alt)
	// 生成MD5哈希值
	hash := md5.Sum([]byte(source.String()))
	return hex.EncodeToString(hash[:])
}

// MakeSign 生成签名，cl 为目标结构体，alt 为加盐字符串，excludeFields 为要排除的字段集合
func MakeSign(cl interface{}, alt string, excludeFields ...string) string {
	// 将 excludeFields 转为 map，方便查找
	excludeMap := createExcludeMap(excludeFields)
	// 获取并排序字段名
	fieldNames := getSortedFieldNames(cl, excludeMap)
	// 构建签名字符串
	signSource := buildSignSource(cl, fieldNames)
	// 添加盐值
	signSource.WriteString(alt)
	// 计算 MD5 哈希值
	return computeMD5Hash(signSource.Bytes())
}

func getSortedFieldNames(cl interface{}, excludeMap map[string]struct{}) []string {
	var fieldNames []string
	val := reflect.ValueOf(cl)

	// 解引用指针，确保 cl 是结构体
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}
	typ := val.Type()

	for i := 0; i < typ.NumField(); i++ {
		fieldName := typ.Field(i).Name
		if _, excluded := excludeMap[fieldName]; !excluded && strings.ToUpper(fieldName) != "SIGN" {
			fieldNames = append(fieldNames, fieldName)
		}
	}
	sort.Strings(fieldNames)
	return fieldNames
}

func buildSignSource(cl interface{}, fieldNames []string) bytes.Buffer {
	var source bytes.Buffer
	val := reflect.ValueOf(cl)

	// 解引用指针，确保 cl 是结构体
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	for _, fieldName := range fieldNames {
		fieldValue := val.FieldByName(fieldName)
		if fieldValue.IsValid() {
			// 仅对支持 IsNil 方法的类型检查是否为 nil
			if fieldValue.Kind() == reflect.Ptr {
				if fieldValue.IsNil() {
					continue // 如果为 nil，跳过写入
				}
				// 解引用指针，获取实际的值
				fieldValue = fieldValue.Elem()
			}
			if fieldValue.Kind() == reflect.Interface {
				if fieldValue.IsNil() {
					continue // 如果为 nil，跳过写入
				}
			}
			source.WriteString(formatFieldValue(fieldValue) + ",")
		}
	}
	return source
}

// formatFieldValue 格式化字段值，去除空格等
func formatFieldValue(fieldValue reflect.Value) string {
	switch fieldValue.Kind() {
	case reflect.Slice, reflect.Array:
		// 将 Slice 或 Array 转换为字符串，假设它包含的是字节或字符类型
		if fieldValue.Type().Elem().Kind() == reflect.Uint8 { // 处理 byte slice 转字符串
			return strings.ReplaceAll(strings.TrimSpace(string(fieldValue.Bytes())), " ", "")
		}
		// 其他 Slice 或 Array 类型转换
		var elements []string
		for i := 0; i < fieldValue.Len(); i++ {
			elements = append(elements, formatFieldValue(fieldValue.Index(i)))
		}
		return strings.Join(elements, ",")
	default:
		// 处理基本类型 (如 int32, int, float64) 转字符串
		return fmt.Sprintf("%v", fieldValue.Interface())
	}
}

// computeMD5Hash 计算 MD5 哈希值
func computeMD5Hash(data []byte) string {
	hash := md5.Sum(data)
	return hex.EncodeToString(hash[:])
}

// createExcludeMap 将要排除的字段列表转换为 map
func createExcludeMap(fields []string) map[string]struct{} {
	excludeMap := make(map[string]struct{})
	for _, field := range fields {
		excludeMap[field] = struct{}{}
	}
	return excludeMap
}

func RandomOne[T any](items []T) (T, error) {

	if len(items) == 0 {
		var t T
		return t, errors.New("items empty")
	}

	return items[rand.Intn(len(items))], nil
}

//// CalPercentStr 计算百分比字符串  CalPercentStr(1, 2, 2) = "50"
//func CalPercentStr(num, den int64, precision int) string {
//	if den == 0 || num == 0 {
//		return "0"
//	}
//
//	ratioStr := new(big.Rat).SetFrac(
//		big.NewInt(100*num),
//		big.NewInt(den),
//	).FloatString(precision)
//
//	return RemoveTrailingZero(ratioStr)
//}

// SplitVerticalLine 用竖线分割，优先英文|， 也兼容拼写错误的中文竖线
func SplitVerticalLine(str string) []string {
	str = strings.TrimSpace(str)
	if strings.Contains(str, "|") {
		return strings.Split(str, "|")
	}
	return strings.Split(str, "丨")
}

func SplitAny(str string, delimiters ...string) []string {
	if len(delimiters) == 0 {
		return make([]string, 0)
	}

	str = strings.TrimSpace(str)
	for _, delimiter := range delimiters {
		if strings.Contains(str, delimiter) {
			return strings.Split(str, delimiter)
		}
	}
	return strings.Split(str, delimiters[0])
}
