package signutils

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"reflect"
	"sort"
	"strings"
)

func SignWithComma(vo interface{}, alt string) string {
	v := reflect.ValueOf(vo)

	// 如果 v 是一个指针，则解引用它
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	t := v.Type()

	// 获取所有字段的名称
	fieldNames := make([]string, 0, v.NumField())
	for i := 0; i < v.NumField(); i++ {
		fieldNames = append(fieldNames, t.Field(i).Name)
	}

	// 按字段名称排序
	sort.Strings(fieldNames)

	var source strings.Builder

	for _, fieldName := range fieldNames {
		// sign字段不参与加密
		if fieldName == "Sign" {
			continue
		}

		fieldValue := v.FieldByName(fieldName)

		// 处理字段，不过滤零值
		if fieldValue.IsValid() && fieldValue.CanInterface() {
			obj := fieldValue.Interface()
			switch v := obj.(type) {
			case []interface{}:
				// List加密有空格,去除
				source.WriteString(strings.ReplaceAll(fmt.Sprint(v), " ", ""))
			default:
				source.WriteString(fmt.Sprint(v))
			}
			source.WriteString(",")
		}
	}

	// 加盐
	source.WriteString(alt)

	// 生成MD5哈希值
	hash := md5.Sum([]byte(source.String()))
	return hex.EncodeToString(hash[:])
}

func Contains[T comparable](arr []T, target T) bool {
	for _, v := range arr {
		if v == target {
			return true
		}
	}
	return false
}
