syntax = "proto3";

package component.game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/order";

import "pb/order/order.proto";

// key: order:<appId>:<orderId>

message StorageOrderStatusLog {
  StatusOfOrder status = 1;
  int64 updateTime = 2;
}

message StorageOrderInfo {
  string openId = 1;
  bytes bizData = 2; // 业务数据
  int64 expireTime = 3;
  repeated StorageOrderStatusLog statusLog = 4;
}
