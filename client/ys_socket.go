package client

import (
	"context"
	"git.kugou.net/fxgo/core-rpc/v2/thrift"
	"git.kugou.net/fxgo/core/client/xthrift"
	"git.kugou.net/fxgo/core/logger"
	commonmsg "kugou_adapter_service/third-party/gen-go/common_msg"
)

type commonMsgThriftClient struct {
}

var CommonMsgThriftClient = &commonMsgThriftClient{}

const (
	CommonMsgTypeText       int32 = 2
	CommonMsgFunctionHalfH5 int32 = 4 //拉起半屏h5
)

// YsSendRoomCommonRoomMsg 鱼声发送房间公聊区消息
func (c *commonMsgThriftClient) YsSendRoomCommonRoomMsg(ctx context.Context, roomId int32, msg *commonmsg.CommonMsgDto) {
	commonMsgService, err := xthrift.GetTClient(commonmsg.NewCommonMsgServiceClient, "ys_common_msg_service")
	if err != nil {
		return
	}
	val, err := commonMsgService.SendRoomCommonRoomMsg(ctx, roomId, msg)
	if err != nil {
		logger.Errorf("YsSendRoomCommonRoomMsg failed. roomId: %v, roomId: %v, value:%v,err:%v", roomId, msg, val, err)
		return
	}
	if val.Code != 0 {
		logger.Errorf("YsSendRoomCommonRoomMsg biz error. roomId: %v, roomId: %v, value:%v,err:%v", roomId, msg, val, err)
		return
	}
}

// 发送所有房间公聊区消息
func (c *commonMsgThriftClient) YsSendAllCommonRoomMsg(ctx context.Context, msg *commonmsg.CommonMsgDto) {
	commonMsgService, err := thrift.GetService(commonmsg.NewCommonMsgServiceClient, "ys_common_msg_service")
	if err != nil {
		return
	}
	val, err := commonMsgService.SendAllCommonRoomMsg(ctx, msg)
	if err != nil {
		logger.Errorf("SendAllCommonRoomMsg failed. msg: %v, value:%v,err:%v", msg, val, err)
		return
	}
	if val.Code != 0 {
		logger.Errorf("SendAllCommonRoomMsg biz error.  msg: %v, value:%v,err:%v", msg, val, err)
		return
	}
}
