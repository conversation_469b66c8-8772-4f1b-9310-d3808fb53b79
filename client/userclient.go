package client

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.kugou.net/fxgo/core/client/xthrift"
	"git.kugou.net/fxgo/core/logger"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"kugou_adapter_service/helper"
	"kugou_adapter_service/kugourpc"
	"kugou_adapter_service/model"
	"kugou_adapter_service/third-party/gen-go/thrift/platform/user"
	"kugou_adapter_service/utils/net"
	"log"
	"net/url"
	"strconv"
	"strings"
	"time"
)

const (
	KUGOU_SEX_UNKNOWN = 2
	KUGOU_SEX_MALE    = 1
	KUGOU_SEX_FEMALE  = 0

	KSONG_SEX_UNKNOWN = 0
	KSONG_SEX_MALE    = 1
	KSONG_SEX_FEMALE  = 2
)

type KugouUserProfile struct {
	Birthday   string `json:"birthday"`
	City       string `json:"city"`
	Nickname   string `json:"nickname"`
	Pic        string `json:"pic"`
	Province   string `json:"province"`
	ServerTime string `json:"servertime"`
	Sex        int    `json:"sex"`
	UserID     int64  `json:"userid"`
	Username   string `json:"username"`
}

func GetPlatUserInfo(kugouId int64) (*model.PlatformUserInfo, error) {
	client, err := xthrift.GetTClient(user.NewUserPlatBizServiceClient, "fxsoa_soa_user")
	if err != nil {
		return nil, err
	}
	resMsg, err := client.GetUserInfoFromKugou(context.Background(), kugouId)
	if err == nil && strings.HasSuffix(resMsg.GetResponseCode(), "000") && strings.TrimSpace(resMsg.GetData()) != "" {
		var kugouUserProfile KugouUserProfile
		if err := json.Unmarshal([]byte(resMsg.GetData()), &kugouUserProfile); err == nil {
			return TransformUserInfo(kugouUserProfile), nil
		} else {
			log.Printf("[酷狗用户信息]-反序列化数据为NULL, 酷狗ID[%s], 响应码[%s], 响应信息[%s], 响应内容[%s]",
				kugouId, resMsg.GetResponseCode(), resMsg.GetResponseDesp(), resMsg.GetData())
		}
	}
	return nil, err
}

type KugouListUserProfile struct {
	ArttoyAvatar string `json:"arttoy_avatar"`
	AuthExplain  string `json:"auth_explain"`
	BcCode       string `json:"bc_code"`
	Birthday     string `json:"birthday"`
	Descri       string `json:"descri"`
	FxNickname   string `json:"fx_nickname"`
	FxPic        string `json:"fx_pic"`
	Gender       int    `json:"gender"`
	Iden         int    `json:"iden"`
	IsStar       int    `json:"is_star"`
	KNickname    string `json:"k_nickname"`
	KPic         string `json:"k_pic"`
	KStar        int    `json:"k_star"`
	LiveAddr     string `json:"live_addr"`
	LiveStatus   int    `json:"live_status"`
	TNickname    string `json:"t_nickname"`
	TPic         string `json:"t_pic"`
	Userid       int    `json:"userid"`
}

type UserInfoList struct {
	Data struct {
		TList []KugouListUserProfile `json:"t_list"`
	} `json:"data"`
	Status    int `json:"status"`
	ErrorCode int `json:"error_code"`
}

func BatchGetPlatUserInfo(kugouIds []int64) ([]model.PlatformUserInfo, error) {
	header := map[string]string{}
	uids := lo.FilterMap(kugouIds, func(item int64, index int) (string, bool) {
		value := cast.ToString(item)
		return value, item > 0
	})
	uidStr := strings.Join(uids, ",")
	body := map[string]string{
		"t_userid_list": uidStr,
		"clientip":      net.GetLocalIP(),
	}
	rpcHost := helper.GetApolloClient().GetStringValue("relation.rpcHost", "relation.user.kgidc.cn")
	serverId := helper.GetApolloClient().GetStringValue("serverId", "3450")
	serverKey := helper.GetApolloClient().GetStringValue("serverKey", "hpfg1Nczb23FtPm1leItNRxBWbEOjmdI")
	baseUri := "/v3/info_list"
	params := map[string]string{
		"serverid":   serverId,
		"servertime": strconv.FormatInt(time.Now().Unix(), 10),
		"appid":      "1010",
		"clientver":  "0",
		"mid":        "-",
		"uuid":       "-",
		"plat":       "1",
		"dfid":       "-",
	}
	signature := kugourpc.BuildSignature(serverKey, params, body)
	params["signature"] = signature
	values := url.Values{}
	for key, value := range params {
		values.Add(key, value)
	}
	fullUri := fmt.Sprintf("%s?%s", baseUri, values.Encode())
	var ret UserInfoList
	err := kugourpc.PostWithJson(rpcHost, fullUri, body, &ret, header)
	marshal, err := json.Marshal(ret)
	if err != nil {
		return nil, err
	}
	if ret.Status != 1 || ret.ErrorCode != 0 {
		return nil, errors.New(strconv.Itoa(ret.ErrorCode))
	}
	platformUserInfos := lo.FilterMap(ret.Data.TList, func(item KugouListUserProfile, index int) (model.PlatformUserInfo, bool) {
		return model.PlatformUserInfo{
			Partner:   model.KUGOU,
			OpenID:    strconv.Itoa(item.Userid),
			Nick:      item.TNickname,
			Avatar:    item.TPic,
			UID:       int64(item.Userid),
			Age:       getAge(item.Birthday),
			Gender:    kgGenderToQmGender(item.Gender),
			RichLevel: 0,
			VIPLevel:  0,
			City:      "",
		}, true
	})
	logger.Warnf("marshal: %v", string(marshal))
	return platformUserInfos, err
}

func TransformUserInfo(kugouUserProfile KugouUserProfile) *model.PlatformUserInfo {
	platformUserInfo := &model.PlatformUserInfo{
		Partner:   model.KUGOU,
		OpenID:    strconv.FormatInt(kugouUserProfile.UserID, 10),
		Nick:      kugouUserProfile.Nickname,
		Avatar:    kugouUserProfile.Pic,
		UID:       kugouUserProfile.UserID,
		City:      kugouUserProfile.City,
		Age:       getAge(kugouUserProfile.Birthday),
		Gender:    kgGenderToQmGender(kugouUserProfile.Sex),
		RichLevel: 0,
		VIPLevel:  0,
	}
	return platformUserInfo
}

func getAge(birthday string) int {
	if strings.TrimSpace(birthday) == "" {
		log.Println("[年龄计算]-生日数据格式为空,无法计算年龄")
		return 0
	}

	datePatterns := []string{"2006-01-02", "2006-01-02 15:04:05"}

	for _, pattern := range datePatterns {
		t, err := time.Parse(pattern, birthday)
		if err == nil {
			now := time.Now()
			age := now.Year() - t.Year()
			if now.YearDay() < t.YearDay() {
				age--
			}
			return age
		}
	}

	log.Printf("[年龄计算]-生日数据格式为[%s],无法计算年龄\n", birthday)
	return 0
}

func kgGenderToQmGender(sex int) int {
	switch sex {
	case KUGOU_SEX_FEMALE:
		return KSONG_SEX_FEMALE
	case KUGOU_SEX_MALE:
		return KSONG_SEX_MALE
	case KUGOU_SEX_UNKNOWN:
		return KSONG_SEX_UNKNOWN
	default:
		return KSONG_SEX_UNKNOWN
	}
}
