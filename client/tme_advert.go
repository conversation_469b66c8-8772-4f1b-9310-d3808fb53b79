package client

import (
	"context"
	"git.kugou.net/fxgo/core/logger"
	"google.golang.org/grpc/metadata"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_advert"
	"time"
)

func AdvertCheck(appName string, appId string, openId string, adPosId string, adToken string) (*adapter_advert.AdvertCheckRsp, error) {
	logger.Warnf("开始请求[GetProfile]，请求参数。appId: %v, openId: %v, appName: %v", appId, openId, appName)
	gc, _ := GetGrpcClient()
	ctx, cancel := context.WithTimeout(context.Background(), 3000*time.Millisecond)
	defer cancel()
	client := gc.GetAdapterAdvertClient()
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	req := &adapter_advert.AdvertCheckReq{
		AppId:       appId,
		OpenId:      openId,
		AdPosId:     adPosId,
		AdToken:     adToken,
		Qimei36:     "",
		SceneId:     "",
		AdCheckMask: adapter_advert.AdCheckMask_AdCheckMaskNone,
		MapExt:      map[string]string{},
	}
	if riskCtxStr, err := GetRiskCtx(appId); err == nil {
		req.MapExt["online_earning_risk_ctx"] = riskCtxStr
	}
	rsp, err := client.AdvertCheck(ctx, req)
	if err != nil {
		logger.Warnf("AdvertCheck failure, req:%+v rsp:%+v err:%+v", req, rsp, err)
		return nil, err
	}
	logger.Warnf("AdvertCheck success, req:%+v rsp:%+v err:%+v", req, rsp, err)
	return rsp, nil
}
