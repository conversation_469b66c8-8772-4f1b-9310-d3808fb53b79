package client

import (
	"context"
	"git.kugou.net/fxgo/core/logger"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/adapter_unified_assets"
	assets_callback "kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/adapter_unified_assets_callback"
	callback "kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/callback"
	"time"
)

func Pay(appName string, req *adapter_unified_assets.PayReq) (*adapter_unified_assets.PayRsp, error) {
	logger.Warnf("开始请求[pay]，请求参数。req: %v", req)
	conn, err := grpc.NewClient(GetTarget(), grpc.WithInsecure())
	if err != nil {
		return nil, err
	}
	defer func(conn *grpc.ClientConn) {
		_ = conn.Close()
	}(conn)
	ctx, cancel := context.WithTimeout(context.Background(), 5000*time.Millisecond)
	defer cancel()
	client := adapter_unified_assets.NewAdapterUnifiedAssetClient(conn)
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	rsp := &adapter_unified_assets.PayRsp{}
	rsp, err = client.Pay(ctx, req)
	if err != nil {
		logger.Warnf("Pay failure, req:%+v rsp:%+v err:%+v", req, rsp, err)
		return nil, err
	}
	logger.Warnf("Pay success, req:%+v rsp:%+v err:%+v", req, rsp, err)
	return rsp, nil
}

func PayAndGiveGifts(appName string, req *adapter_unified_assets.PayAndGiveGiftsReq) (*adapter_unified_assets.PayAndGiveGiftsRsp, error) {
	logger.Warnf("开始请求[PayAndGiveGifts]，请求参数。req: %v", req)
	conn, err := grpc.NewClient(GetTarget(), grpc.WithInsecure())
	if err != nil {
		return nil, err
	}
	defer func(conn *grpc.ClientConn) {
		_ = conn.Close()
	}(conn)
	ctx, cancel := context.WithTimeout(context.Background(), 5000*time.Millisecond)
	defer cancel()
	client := adapter_unified_assets.NewAdapterUnifiedAssetClient(conn)

	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	rsp := &adapter_unified_assets.PayAndGiveGiftsRsp{}
	rsp, err = client.PayAndGiveGifts(ctx, req)
	if err != nil {
		logger.Warnf("PayAndGiveGifts failure, req:%+v rsp:%+v err:%+v", req, rsp, err)
		return nil, err
	}
	logger.Warnf("PayAndGiveGifts success, req:%+v rsp:%+v err:%+v", req, rsp, err)
	return rsp, nil
}

func SendGiftPackage(appName string, req *adapter_unified_assets.SendGiftPackageReq) (*adapter_unified_assets.SendGiftPackageRsp, error) {
	logger.Warnf("开始请求[sendGiftPackage]，请求参数。req: %v", req)
	conn, err := grpc.NewClient(GetTarget(), grpc.WithInsecure())
	if err != nil {
		return nil, err
	}
	defer func(conn *grpc.ClientConn) {
		_ = conn.Close()
	}(conn)
	ctx, cancel := context.WithTimeout(context.Background(), 5000*time.Millisecond)
	defer cancel()
	client := adapter_unified_assets.NewAdapterUnifiedAssetClient(conn)
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	rsp := &adapter_unified_assets.SendGiftPackageRsp{}
	rsp, err = client.SendGiftPackage(ctx, req)
	if err != nil {
		logger.Warnf("sendGiftPackage failure, req:%+v rsp:%+v err:%+v", req, rsp, err)
		return nil, err
	}
	logger.Warnf("sendGiftPackage success, req:%+v rsp:%+v err:%+v", req, rsp, err)
	return rsp, nil
}

func GetOrderStatus(appName string, req *adapter_unified_assets.GetOrderStatusReq) (*adapter_unified_assets.GetOrderStatusRsp, error) {
	logger.Warnf("开始请求[GetOrderStatus]，请求参数。req: %v", req)
	conn, err := grpc.NewClient(GetTarget(), grpc.WithInsecure())
	if err != nil {
		return nil, err
	}
	defer func(conn *grpc.ClientConn) {
		_ = conn.Close()
	}(conn)
	ctx, cancel := context.WithTimeout(context.Background(), 3000*time.Millisecond)
	defer cancel()
	client := adapter_unified_assets.NewAdapterUnifiedAssetClient(conn)
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	rsp := &adapter_unified_assets.GetOrderStatusRsp{}
	rsp, err = client.GetOrderStatus(ctx, req)
	if err != nil {
		logger.Warnf("GetOrderStatus failure, req:%+v rsp:%+v err:%+v", req, rsp, err)
		return nil, err
	}
	logger.Warnf("GetOrderStatus success, req:%+v rsp:%+v err:%+v", req, rsp, err)
	return rsp, nil
}

func GiftPackageBusinessCheckSend(appName string, req *callback.GiftPackageBusinessCheckSendReq) (*assets_callback.GiftPackageBusinessCheckSendRsp, error) {
	logger.Warnf("开始请求[GiftPackageBusinessCheckSend]，请求参数。req: %v", req)
	conn, err := grpc.NewClient(GetTarget(), grpc.WithInsecure())
	if err != nil {
		return nil, err
	}
	defer func(conn *grpc.ClientConn) {
		_ = conn.Close()
	}(conn)
	ctx, cancel := context.WithTimeout(context.Background(), 3000*time.Millisecond)
	defer cancel()
	client := assets_callback.NewCallbackClient(conn)
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	rsp := &assets_callback.GiftPackageBusinessCheckSendRsp{}
	rsp, err = client.GiftPackageBusinessCheckSend(ctx, req)
	if err != nil {
		logger.Warnf("GetOrderStatus failure, req:%+v rsp:%+v err:%+v", req, rsp, err)
		return nil, err
	}
	logger.Warnf("GetOrderStatus success, req:%+v rsp:%+v err:%+v", req, rsp, err)
	return rsp, nil
}
