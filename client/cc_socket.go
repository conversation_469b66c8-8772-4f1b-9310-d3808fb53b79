package client

import (
	"context"
	"git.kugou.net/fxgo/core/client/xthrift"
	"git.kugou.net/fxgo/core/logger"
	"kugou_adapter_service/third-party/gen-go/kroompushmsgservice"
)

// SingMsgVo 唱唱房间消息
type SingMsgVo struct {
	MsgType int         `json:"msg_type"`
	MsgData SingMsgData `json:"msg_data"`
}

// SingMsgData 唱唱消息数据
type SingMsgData struct {
	Type      int                    `json:"type"`
	Text      string                 `json:"text"`
	Images    []string               `json:"images"`
	LeftImage string                 `json:"left_image"`
	Button    string                 `json:"button"`
	Function  int                    `json:"function"`
	BizId     int                    `json:"biz_id"`
	Config    map[string]interface{} `json:"config"`
}

// CcSendRoomCommonRoomMsg 唱唱发送房间公聊区消息
func (c *commonMsgThriftClient) CcSendRoomCommonRoomMsg(roomId int32, content string) {
	kroomPushMsgService, err := xthrift.GetTClient(kroompushmsgservice.NewKroomMsgThriftServiceClient, "sing-kroom")
	if err != nil {
		return
	}
	val, err := kroomPushMsgService.PushKroomSocketMsg(context.Background(), roomId, content)
	logger.Warnf("SendContentToSingRoom finish roomId:%v, content:%v, val:%v", roomId, content, val)
	if err != nil {
		logger.Errorf("SendContentToSingRoom error roomId:%v, content:%v, err:%v", roomId, content, err)
		return
	}
	if val.Ret != 0 {
		logger.Errorf("SendContentToSingRoom fail roomId:%v, content:%v, err:%v", roomId, content, err)
		return
	}
}
