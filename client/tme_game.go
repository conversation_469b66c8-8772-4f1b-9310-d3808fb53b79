package client

import (
	"context"
	"errors"
	"git.kugou.net/fxgo/core/logger"
	"github.com/spf13/cast"
	"google.golang.org/grpc/metadata"
	"kugou_adapter_service/constant"
	"kugou_adapter_service/helper"
	"kugou_adapter_service/model"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_revenue"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_user"
	"kugou_adapter_service/pkg/gen/proto/pb/gopen"
	"strconv"
	"time"
)

func GetTarget() string {
	return helper.GetApolloClient().GetStringValue("tmeGame.host", "common.cloud.ingress.test.dove:65501")
}

func Openid2PlatUid(appId string, openId string, appName string) (int64, error) {
	logger.Warnf("开始请求[Openid2PlatUid]，请求参数。appId: %v, openId: %v, appName: %v", appId, openId, appName)
	timeout := helper.GetApolloClient().GetIntValue("Openid2PlatUid.timeout.mills", 600)
	gc, _ := GetGrpcClient()
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Millisecond)
	defer cancel()
	client := gc.GetGOpenClient()
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	rsp, err := client.Openid2PlatUid(ctx, &gopen.Openid2PlatUidReq{
		StrAppID:  appId,
		StrOpenid: openId,
	})
	if err != nil {
		logger.Warnf("开始请求[Openid2PlatUid]，查询失败。appId: %v, openId: %v, appName: %v, err:%+v", appId, openId, appName, err)
		return 0, err
	}
	return int64(rsp.LUid), nil
}

func MOpenid2PlatUid(appName string, appId string, openIds []string) (*gopen.MOpenid2PlatUidRsp, error) {
	logger.Warnf("开始请求[MOpenid2PlatUid]，请求参数。appName: %v, appId: %v, openIds: %v", appName, appId, openIds)
	timeout := helper.GetApolloClient().GetIntValue("MOpenid2PlatUid.timeout.mills", 600)
	gc, _ := GetGrpcClient()
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Millisecond)
	defer cancel()
	client := gc.GetGOpenClient()
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	rsp, err := client.MOpenid2PlatUid(ctx, &gopen.MOpenid2PlatUidReq{
		StrAppID:   appId,
		VecOpenids: openIds,
	})
	if err != nil {
		logger.Warnf("开始请求[MOpenid2PlatUid]，查询失败。appName: %v, appId: %v, openIds: %v, err:%+v", appName, appId, openIds, err)
		return nil, err
	}
	return rsp, nil
}

func PlatUid2Openid(appId string, platUid int64, appName string) (string, error) {
	logger.Warnf("开始请求[PlatUid2Openid]，请求参数。appId: %v, platUid: %v, appName: %v", appId, platUid, appName)
	timeout := helper.GetApolloClient().GetIntValue("PlatUid2Openid.timeout.mills", 600)
	gc, _ := GetGrpcClient()
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Millisecond)
	defer cancel()
	client := gc.GetGOpenClient()
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	rsp, err := client.PlatUid2Openid(ctx, &gopen.PlatUid2OpenidReq{
		StrAppID: appId,
		LPlatID:  8,
		LUid:     uint64(platUid),
	})
	if err != nil {
		logger.Warnf("开始请求[PlatUid2Openid]，查询失败。appId: %v, platUid: %v, appName: %v, err:%+v", appId, platUid, appName, err)
		return "", err
	}
	return rsp.StrOpenid, nil
}

func ValidateSessionKey(appId string, openId string, sessionKey string, appName string) bool {
	logger.Warnf("开始请求[ValidateSessionKey]，请求参数。appId: %v, openId: %v, appName: %v", appId, openId, appName)
	timeout := helper.GetApolloClient().GetIntValue("ValidateSessionKey.timeout.mills", 600)
	gc, _ := GetGrpcClient()
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Millisecond)
	defer cancel()
	client := gc.GetGOpenClient()
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	anchor, _ := strconv.ParseInt("0", 10, 64)
	req := gopen.ValidateSessionKeyReq{
		StrAppID:      appId,
		StrOpenid:     openId,
		StrSessionKey: sessionKey,
		LAnchor:       anchor,
	}
	rsp, err := client.ValidateSessionKey(ctx, &req)
	if err != nil {
		logger.Warnf("validate Session key sign fail, req=%v, err=%+v", req, err)
		return false
	}
	logger.Warnf("validate Session key sign success, req=%v, rsp=%v", req, rsp)
	return true
}

func QueryAsset(appName string, gameAppId string, openId string, assetType adapter_revenue.AssetType, assetId int64) (*adapter_revenue.QueryAssetRsp, error) {
	logger.Warnf("调用游戏中台资产查询接口，请求参数。gameAppId: %v, openId: %v, assetType: %v", gameAppId, openId, assetType)
	timeout := helper.GetApolloClient().GetIntValue("QueryAsset.timeout.mills", 600)
	gc, _ := GetGrpcClient()
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Millisecond)
	defer cancel()
	client := gc.GetAdapterRevenueClient()
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	req := &adapter_revenue.QueryAssetReq{
		AppId:     gameAppId,
		OpenId:    openId,
		AssetType: assetType,
		AssetIds:  []int64{assetId},
		Source: &adapter_revenue.SourceChannel{
			SourceId: 1,
			Reason:   "",
		},
	}
	rsp, err := client.QueryAsset(ctx, req)
	if err != nil {
		logger.Warnf("调用游戏中台资产查询接口，查询失败。req:%+v rsp:%+v err:%+v", req, rsp, err)
		return nil, err
	}
	logger.Warnf("调用游戏中台资产查询接口，查询成功。req:%+v rsp:%+v err:%+v", req, rsp, err)
	return rsp, nil
}

func AddAsset(appName string, gameAppId string, openId string, orderNo string,
	assetType adapter_revenue.AssetType, assetId int64, assetNum int64, sourceId int32) (*adapter_revenue.AddAssetRsp, error) {
	timeout := helper.GetApolloClient().GetIntValue("AddAsset.timeout.mills", 600)
	gc, _ := GetGrpcClient()
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Millisecond)
	defer cancel()
	client := gc.GetAdapterRevenueClient()
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	req := &adapter_revenue.AddAssetReq{
		AppId:     gameAppId,
		OpenId:    openId,
		BillNo:    orderNo,
		Timestamp: time.Now().UnixMilli(),
		AssetType: assetType,
		Assets: []*adapter_revenue.AssetChange{{
			AssetId:  assetId,
			AssetNum: assetNum,
		}},
		Source: &adapter_revenue.SourceChannel{
			SourceId: uint32(sourceId),
			Reason:   "",
		},
		Scene:  0,
		MapExt: map[string]string{},
	}
	if riskCtxStr, err := GetRiskCtx(gameAppId); err == nil {
		req.MapExt["online_earning_risk_ctx"] = riskCtxStr
	}
	rsp, err := client.AddAsset(ctx, req)
	if err != nil {
		logger.Warnf("调用游戏中台资产发放接口，发放失败。req:%+v rsp:%+v err:%+v", req, rsp, err)
		return nil, err
	}
	logger.Warnf("调用游戏中台资产发放接口，发放成功。req:%+v rsp:%+v err:%+v", req, rsp, err)
	return rsp, nil
}

func SubAsset(gameAppId string, openId string, orderNo string, assetType adapter_revenue.AssetType,
	assetNum int64, subAssetExt model.SubAssetExt) (*adapter_revenue.SubAssetRsp, error) {
	logger.Warnf("调用游戏中台资产扣减接口，请求参数。gameAppId: %v, openId: %v, orderNo: %v, assetType: %v, assetNum: %v",
		gameAppId, openId, orderNo, assetType, assetNum)
	timeout := helper.GetApolloClient().GetIntValue("SubAsset.timeout.mills", 600)
	assetId := int64(0)
	switch assetType {
	case adapter_revenue.AssetType_AssetTypeKugouFxCoin:
		assetId = 2000000020
	case adapter_revenue.AssetType_AssetTypeCarrierDogecoin:
		assetId = 2000001025
	default:
		logger.Warnf("调用游戏中台资产扣减接口，assetType不支持。assetType: %v", assetType)
		return nil, errors.New("assetType is invalid")
	}
	gc, _ := GetGrpcClient()
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Millisecond)
	defer cancel()
	client := gc.GetAdapterRevenueClient()
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": constant.AppNameKugou}))
	req := &adapter_revenue.SubAssetReq{
		AppId:     gameAppId,
		OpenId:    openId,
		BillNo:    orderNo,
		Timestamp: time.Now().UnixMilli(),
		AssetType: assetType,
		Assets: []*adapter_revenue.AssetChange{{
			AssetId:  assetId,
			AssetNum: assetNum,
		}},
		Source: &adapter_revenue.SourceChannel{
			SourceId: 0,
			Reason:   "",
		},
		Scene: 0,
		MapExt: map[string]string{
			"skuId":     subAssetExt.SkuId,
			"quantity":  cast.ToString(subAssetExt.Quantity),
			"gameExtra": subAssetExt.GameExtra,
			"platExtra": subAssetExt.PlatExtra,
		},
	}
	rsp, err := client.SubAsset(ctx, req)
	if err != nil {
		logger.Warnf("SubAsset failure, req:%+v rsp:%+v err:%+v", req, rsp, err)
		return nil, err
	}
	logger.Warnf("SubAsset success, req:%+v rsp:%+v err:%+v", req, rsp, err)
	return rsp, nil
}

func GetProfile(appId string, openId string, appName string, AvatarLength int32, AvatarWidth int32, idType int32) (*adapter_user.Profile, error) {
	logger.Warnf("开始请求[GetProfile]，请求参数。appId: %v, openId: %v, appName: %v", appId, openId, appName)
	timeout := helper.GetApolloClient().GetIntValue("GetProfile.timeout.mills", 600)
	gc, _ := GetGrpcClient()
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Millisecond)
	defer cancel()
	client := gc.GetAdapterUserClient()
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	req := &adapter_user.GetProfileReq{
		AppId:        appId,
		OpenId:       openId,
		AvatarLength: AvatarLength,
		AvatarWidth:  AvatarWidth,
		Mask:         0,
		IdType:       idType,
	}
	rsp := &adapter_user.GetProfileRsp{}
	rsp, err := client.GetProfile(ctx, req)
	if err != nil {
		logger.Warnf("GetProfile failure, req:%+v rsp:%+v err:%+v", req, rsp, err)
		return nil, err
	}
	logger.Warnf("GetProfile success, req:%+v rsp:%+v err:%+v", req, rsp, err)
	return rsp.Profile, nil
}

func BatchGetProfile(appName string, appId string, openIdList []string, AvatarLength int32, AvatarWidth int32, uidList []string, encryptUidList []string) (*adapter_user.BatchGetProfileRsp, error) {
	logger.Warnf("开始请求[BatchGetProfile]，请求参数。appId: %v, openIdList: %v, appName: %v", appId, openIdList, appName)
	timeout := helper.GetApolloClient().GetIntValue("BatchGetProfile.timeout.mills", 600)
	gc, _ := GetGrpcClient()
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Millisecond)
	defer cancel()
	client := gc.GetAdapterUserClient()
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"app-name": appName}))
	req := &adapter_user.BatchGetProfileReq{
		AppId:        appId,
		AvatarLength: AvatarLength,
		AvatarWidth:  AvatarWidth,
		Mask:         0,
	}
	if openIdList != nil && len(openIdList) > 0 {
		req.OpenIdList = openIdList
	}
	if uidList != nil && len(uidList) > 0 {
		req.UidList = uidList
	}
	if encryptUidList != nil && len(encryptUidList) > 0 {
		req.EncryptUidList = encryptUidList
	}
	rsp := &adapter_user.BatchGetProfileRsp{}
	rsp, err := client.BatchGetProfile(ctx, req)
	if err != nil {
		logger.Warnf("BatchGetProfile failure, req:%+v rsp:%+v err:%+v", req, rsp, err)
		return nil, err
	}
	logger.Warnf("BatchGetProfile success, req:%+v rsp:%+v err:%+v", req, rsp, err)
	return rsp, nil
}
