package client

import (
	"context"
	"errors"
	"fmt"
	"git.kugou.net/fxgo/core/client/xthrift"
	"git.kugou.net/fxgo/core/logger"
	"kugou_adapter_service/third-party/gen-go/userstarvipservice"
)

type UserStarVipClient struct {
}

func GetUserStarVipClient() *UserStarVipClient {
	return &UserStarVipClient{}
}

type StarVipInfo struct {
	KugouId      int64  `json:"kugouId"`
	MysticStatus int32  `json:"mysticStatus"`
	StarvipType  int32  `json:"starvipType"`
	StarvipLevel int32  `json:"starvipLevel"`
	URL          string `json:"url"`
	MysticName   string `json:"mysticName"`
	KingName     string `json:"kingName"`
	BorthType    int32  `json:"borthType"`
}

func (c *UserStarVipClient) doGetUserStarVipMap(kgIds []int64) (map[int64]*StarVipInfo, error) {
	client, err := xthrift.GetTClient(userstarvipservice.NewStarvipServiceClient, "platform_starvip_service")
	if err != nil {
		logger.Errorf("GetUserStarVipMap error.创建client失败. error=%v", err)
		return nil, err
	}
	response, err := client.GetStarvipInfoBatch(context.Background(), kgIds)
	if err != nil {
		logger.Errorf("GetUserStarVipMap error.调用失败. request=%v, error=%v", kgIds, err)
		return nil, err
	}

	if response == nil || response.Data == nil || len(response.Data) == 0 {
		logger.Errorf("GetUserStarVipMap error.返回值错误. request=%v,  response=%v", kgIds, response)
		return nil, errors.New(fmt.Sprintf("返回值错误:%v", response))
	}
	logger.Warnf("GetUserStarVipMap success. request=%v, size=%v", kgIds, len(response.Data))
	//组装数据
	starVipMap := make(map[int64]*StarVipInfo)
	for _, starVip := range response.Data {
		starVipMap[starVip.KugouId] = &StarVipInfo{
			KugouId:      starVip.KugouId,
			MysticStatus: starVip.MysticStatus,
			StarvipType:  starVip.StarvipType,
			StarvipLevel: starVip.StarvipLevel,
			URL:          starVip.URL,
			MysticName:   *starVip.MysticName,
			KingName:     *starVip.KingName,
			BorthType:    *starVip.BorthType,
		}
	}
	return starVipMap, nil
}

func (c *UserStarVipClient) GetUserStarVipNickName(kugouId int64) string {
	starVipMap, _ := c.doGetUserStarVipMap([]int64{kugouId})
	if starVipMap == nil || len(starVipMap) == 0 {
		return ""
	}
	if starVipMap[kugouId] == nil || starVipMap[kugouId].MysticStatus != 1 {
		return ""
	}
	//返回神秘嘉宾信息
	return starVipMap[kugouId].MysticName
}
