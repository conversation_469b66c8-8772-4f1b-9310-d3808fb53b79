package client

import (
	"context"
	"encoding/json"
	"fmt"
	"git.kugou.net/fxgo/core/client/xthrift"
	"git.kugou.net/fxgo/core/logger"
	"github.com/shopspring/decimal"
	"kugou_adapter_service/helper"
	"kugou_adapter_service/kugourpc"
	"kugou_adapter_service/model"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_revenue"
	"kugou_adapter_service/third-party/gen-go/consumeservice"
	"kugou_adapter_service/third-party/gen-go/platform_consume_read_service"
	"net/url"
	"strconv"
	"time"
)

type Params map[string]string

func genBaseUriParams(kugouId int64, version int) Params {
	serverId := helper.GetApolloClient().GetIntValue("serverId", 3428)
	return Params{
		"serverid":   strconv.Itoa(serverId),
		"servertime": strconv.Itoa(int(time.Now().Unix())),
		"appid":      "1010",
		"clientver":  fmt.Sprintf("%d", version),
		"userid":     fmt.Sprintf("%d", kugouId),
	}
}

func buildFullUrl(path string, kugouId int64) string {
	uriParams := genBaseUriParams(kugouId, 0)
	uriParams["spec"] = "4"
	uriParams["from"] = "server"
	u, _ := url.Parse(path)
	query := u.Query()
	for key, value := range uriParams {
		query.Add(key, value)
	}
	serverKey := helper.GetApolloClient().GetStringValue("serverKey", "HvVhkiqgFw7U0g8Km6VfdnqlD4rbLsdW")
	query.Add("signature", kugourpc.BuildSignature(serverKey, uriParams, nil))
	u.RawQuery = query.Encode()
	return u.String()
}

func GetUserCoin(kugouId int64, coinType int32) *decimal.Decimal {
	switch coinType {
	case 0:
		return getUserFxCoin(kugouId)
	case 1:
		return getUserDogCoin(kugouId)
	case 2:
		return getUserSingCoin(kugouId)
	default:
		return &decimal.Zero
	}
}

func GetUserCoinByAssetType(kugouId int64, assetType adapter_revenue.AssetType) *decimal.Decimal {
	if assetType == adapter_revenue.AssetType_AssetTypeCarrierDogecoin {
		return getUserDogCoin(kugouId)
	}
	if assetType == adapter_revenue.AssetType_AssetTypeKugouFxCoin {
		return getUserFxCoin(kugouId)
	}
	return &decimal.Zero
}

func getUserDogCoin(kugouId int64) *decimal.Decimal {
	kgrpcHost := "mstc.kgidc.cn"
	kgUserBalance := &model.KgUserBalance{}
	fullUrl := buildFullUrl("/musicsymbol/v1/user/info", kugouId)
	err := kugourpc.GetWithJson(kgrpcHost, fullUrl, kgUserBalance, nil)
	if err != nil {
		logger.Warnf("getUserDogCoin. fullUrl: %v, err: %v", fullUrl, err)
		return &decimal.Zero
	}
	balance := decimal.NewFromInt(int64(kgUserBalance.Data.BalanceCoins))
	return &balance
}

func getUserFxCoin(kugouId int64) *decimal.Decimal {
	client, err := xthrift.GetTClient(consumeservice.NewPlatformConsumeReadServiceClient, "platform_consume_service")
	if err != nil {
		return &decimal.Zero
	}
	rsp, err := client.GetUserMoney(context.Background(), kugouId, 110765)
	if err != nil {
		logger.Warnf("platformConsumeReadService.GetUserFxCoin error kugouId:%v,err:%v", kugouId, err)
		return &decimal.Zero
	}
	if rsp.Ret != 0 || rsp.Data == nil {
		logger.Errorf("platformConsumeReadService.GetUserFxCoin fail kugouId:%v,resp:%v", kugouId, rsp)
		return &decimal.Zero
	}
	var userBalance UserBalance
	err = json.Unmarshal([]byte(*rsp.Data), &userBalance)
	if err != nil {
		return &decimal.Zero
	}
	return userBalance.Coin
}

func getUserSingCoin(kugouId int64) *decimal.Decimal {
	client, err := xthrift.GetTClient(platform_consume_read_service.NewPlatformConsumeReadServiceClient, "sing_consume_service")
	if err != nil {
		logger.Warnf("singConsumeReadService.GetUserSingCoin error kugouId:%v,err:%v", kugouId, err)
		return &decimal.Zero
	}
	rsp, err := client.GetUserMoney(context.Background(), kugouId, 8001858)
	if err != nil {
		logger.Warnf("singConsumeReadService.GetUserSingCoin error kugouId:%v,err:%v", kugouId, err)
		return &decimal.Zero
	}
	if rsp.Ret != 0 || rsp.Data == nil {
		logger.Errorf("singConsumeReadService.GetUserSingCoin fail kugouId:%v,resp:%v", kugouId, rsp)
		return &decimal.Zero
	}
	var userBalance UserBalance
	err = json.Unmarshal([]byte(*rsp.Data), &userBalance)
	if err != nil {
		return &decimal.Zero
	}
	return userBalance.Coin
}

type UserBalance struct {
	KugouId              int64            `json:"kugouId"`              // 酷狗ID，主键
	UserId               int64            `json:"userId"`               // 用户ID、繁星ID，唯一索引
	Coin                 *decimal.Decimal `json:"coin"`                 // 星币余额
	CoinSpend            *decimal.Decimal `json:"coinSpend"`            // 累计消费的星币
	CoinTotal            *decimal.Decimal `json:"coinTotal"`            // 累计收到的星币
	Money                *decimal.Decimal `json:"money"`                // 充值星币余额
	MoneySpend           *decimal.Decimal `json:"moneySpend"`           // 累计消费的充值星币
	Bean                 *decimal.Decimal `json:"bean"`                 // 星豆余额
	BeanTotal            *decimal.Decimal `json:"beanTotal"`            // 累计获得的星豆
	CzTotal              *decimal.Decimal `json:"czTotal"`              // 累计充值获得的总星币
	CreateTime           *time.Time       `json:"createTime"`           // 创建时间
	UpdateTime           *time.Time       `json:"updateTime"`           // 最后更新时间
	Status               int              `json:"status"`               // 用户余额记录状态（1：正常，2：冻结，3：无效）
	Hashcode             string           `json:"hashcode"`             // 金钱字段防篡改哈希值
	Exchange             *decimal.Decimal `json:"exchange"`             // 兑换账户余额，默认值为零
	UpdateTimeMs         int64            `json:"updateTimeMs"`         // 最后更新时间(ms)
	LastTransactionLogId int64            `json:"lastTransactionLogId"` // 最后更新的transaction_log_id
}
