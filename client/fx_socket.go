package client

import (
	"context"
	"git.kugou.net/fxgo/core/client/xthrift"
	"git.kugou.net/fxgo/core/logger"
	"kugou_adapter_service/pojo/constant"
	"kugou_adapter_service/third-party/gen-go/kgthrift/service"
	"kugou_adapter_service/third-party/gen-go/kgthrift/types"
	"kugou_adapter_service/utils"
)

const (
	KeyTower           string = "adapter"
	ClickTypeNone      int32  = 0
	ClickTypeH5        int32  = 1
	ClickTypeHalfH5    int32  = 2
	ClickTypeRoom      int32  = 3
	ClickTypeGift      int32  = 4
	ClickTypeFans      int32  = 5
	ClickTypeFansTitle int32  = 6
	ColorStyleDefault  int32  = 0
	ColorStyleOrange   int32  = 1
	SocketCmdToUser    int32  = 400301
	SocketCmdToRoom    int32  = 400302
	SocketCmdToAll     int32  = 400303
	BcId               int32  = 2030
	BcKey              string = "9B263A60D4314131D761EFEEF266D472"
	TailRange          int64  = 10
)

type CommonPublicChat struct {
	Key             string   `json:"key"`
	Template        string   `json:"template"`
	Images          []string `json:"images"`
	ClickType       int32    `json:"clickType"`
	Url             string   `json:"url"`
	UseCommonParams bool     `json:"useCommonParams"`
	JumpRoomId      int32    `json:"jumpRoomId"`
	StarsName       string   `json:"starsName"`
	ColorStyle      int32    `json:"colorStyle"`
	GiftId          int32    `json:"giftId"`
	FromRoomId      int32    `json:"fromRoomId"`
	RoomTails       []int32  `json:"roomTails"`
}

func SendCommonPublicChatToUser(kugouId int64, roomId int32, content CommonPublicChat) {
	SendContentToUser(SocketCmdToUser, kugouId, roomId, utils.JsonString(content))
}

func SendCommonPublicChatToRoom(roomId int32, content CommonPublicChat) {
	SendContentToRoom(SocketCmdToRoom, roomId, utils.JsonString(content))
}

func SendCommonPublicChatToAll(content CommonPublicChat) {
	SendContentToAll(BcId, BcKey, SocketCmdToAll, utils.JsonString(content))
}

func SendContentToUser(cmd int32, kugouId int64, roomId int32, content string) {
	appDispatchServiceV2, err := xthrift.GetTClient(service.NewAppDispatchServiceV2Client, "platform_socket_message")
	if err != nil {
		//返回-1
		return
	}
	bytes := []byte(content)
	msgOption := &types.MsgOption{
		SendToAll: true,
		ClientIds: make([]int32, 0),
	}
	val, err := appDispatchServiceV2.SendToUser(context.Background(), roomId, kugouId, cmd, bytes, msgOption)
	if err != nil {
		logger.Errorf("appDispatchServiceV2.SendToUser error cmd:%v, kugouId:%v, roomId:%v, content:%v, err:%v", cmd, kugouId, roomId, content, err)
		return
	}
	if val.Ret != 0 {
		logger.Errorf("appDispatchServiceV2.SendToUser fail cmd:%v, kugouId:%v, roomId:%v, content:%v, err:%v", cmd, kugouId, roomId, content, err)
	} else {
		logger.Infof("SendContentToUser cmd:%v, kugouId:%v, roomId:%v, content:%v", cmd, kugouId, roomId, content)
	}
}

func SendContentToRoom(cmd int32, roomId int32, content string) {
	appDispatchServiceV2, err := xthrift.GetTClient(service.NewAppDispatchServiceV2Client, "platform_socket_message")
	if err != nil {
		//返回-1
		return
	}
	bytes := []byte(content)
	msgOption := &types.MsgOption{
		SendToAll: true,
		ClientIds: make([]int32, 0),
	}
	val, err := appDispatchServiceV2.SendToRoom(context.Background(), roomId, cmd, bytes, msgOption)
	if err != nil {
		logger.Errorf("appDispatchServiceV2.SendToRoom error cmd:%v, roomId:%v, content:%v, err:%v", cmd, roomId, content, err)
		return
	}
	if val.Ret != constant.STATUS_SUCCESS {
		logger.Errorf("appDispatchServiceV2.SendToRoom fail cmd:%v, roomId:%v, content:%v, err:%v", cmd, roomId, content, err)
	} else {
		logger.Infof("SendContentToRoom cmd:%v, roomId:%v, content:%v", cmd, roomId, content)
	}
}

func SendContentToAll(bcId int32, bcKey string, cmd int32, content string) {
	appDispatchServiceV2, err := xthrift.GetTClient(service.NewAppDispatchServiceV2Client, "platform_socket_message")
	if err != nil {
		//返回-1
		return
	}
	bytes := []byte(content)
	msgOption := &types.MsgOption{
		SendToAll: true,
		ClientIds: make([]int32, 0),
	}
	val, err := appDispatchServiceV2.SendToAll(context.Background(), bcId, bcKey, cmd, bytes, msgOption)
	if err != nil {
		logger.Errorf("appDispatchServiceV2.SendToAll error cmd:%v, content:%v, err:%v", cmd, content, err)
		return
	}
	if val.Ret != constant.STATUS_SUCCESS {
		logger.Errorf("appDispatchServiceV2.SendToAll fail cmd:%v, content:%v, err:%v", cmd, content, err)
	} else {
		logger.Infof("SendContentToAll cmd:%v, content:%v", cmd, content)
	}
}
