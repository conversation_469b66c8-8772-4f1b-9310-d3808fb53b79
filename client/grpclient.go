package client

import (
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"kugou_adapter_service/pkg/gen/proto/moe_risk_proto/moe_risk_adapter/server"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_advert"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_revenue"
	adapter_unified_assets_callback "kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/adapter_unified_assets_callback"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_user"
	"kugou_adapter_service/pkg/gen/proto/pb/gopen"
	"kugou_adapter_service/pkg/gen/proto/pb/reward_sender"
	"sync"
)

type GrpcClient struct {
	conn *grpc.ClientConn
	mu   sync.Mutex // 保护 conn，防止并发 Close 出问题
}

var (
	grpcClient *GrpcClient
	once       sync.Once
)

// GetGrpcClient 单例初始化
func GetGrpcClient() (*GrpcClient, error) {
	var err error
	once.Do(func() {
		var conn *grpc.ClientConn
		opts := grpc.WithTransportCredentials(insecure.NewCredentials())
		conn, err = grpc.NewClient(GetTarget(), opts)
		if err != nil {
			return
		}
		grpcClient = &GrpcClient{
			conn: conn,
		}
	})
	return grpcClient, err
}

// Close 优雅关闭连接
func (gc *GrpcClient) Close() error {
	gc.mu.Lock()
	defer gc.mu.Unlock()

	if gc.conn != nil {
		err := gc.conn.Close()
		gc.conn = nil // 防止后面再次使用
		return err
	}
	return nil
}

func (gc *GrpcClient) GetGOpenClient() gopen.GOpenServiceClient {
	return gopen.NewGOpenServiceClient(gc.conn)
}

func (gc *GrpcClient) GetAdapterRevenueClient() adapter_revenue.AdapterRevenueClient {
	return adapter_revenue.NewAdapterRevenueClient(gc.conn)
}

func (gc *GrpcClient) GetAdapterUserClient() adapter_user.AdapterUserClient {
	return adapter_user.NewAdapterUserClient(gc.conn)
}

func (gc *GrpcClient) GetAdapterAdvertClient() adapter_advert.AdapterAdvertClient {
	return adapter_advert.NewAdapterAdvertClient(gc.conn)
}

func (gc *GrpcClient) GetRewardSenderClient() reward_sender.RewardSenderClient {
	return reward_sender.NewRewardSenderClient(gc.conn)
}

func (gc *GrpcClient) GetMoeRiskAdapterClient() server.MoeRiskAdapterClient {
	return server.NewMoeRiskAdapterClient(gc.conn)
}

func (gc *GrpcClient) GetCallbackClient() adapter_unified_assets_callback.CallbackClient {
	return adapter_unified_assets_callback.NewCallbackClient(gc.conn)
}
