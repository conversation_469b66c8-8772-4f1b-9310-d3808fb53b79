package client

import (
	"context"
	"git.kugou.net/fxgo/core/client/xthrift"
	"git.kugou.net/fxgo/core/logger"
	"kugou_adapter_service/pojo/constant"
	"kugou_adapter_service/third-party/gen-go/headlineservice"
)

type HeadlineSendDto struct {
	Biz         int32    `json:"biz"`
	GlobalId    int64    `json:"globalId"`
	ImageList   []string `json:"imageList"`
	Template    string   `json:"template"`
	BgColor     []string `json:"bgColor"`
	Contents    []string `json:"contents"`
	Link        string   `json:"link"`
	CurrentTime int64    `json:"currentTime"`
	ShowTime    int32    `json:"showTime"`
	ExpireTime  int32    `json:"expireTime"`
	Ext         string   `json:"ext"`
	Effect      string   `json:"effect"`
	RoomId      int32    `json:"roomId"`
}

// 发送头条
func SendHeadline(ctx context.Context, dto *HeadlineSendDto) (bool, error) {
	headlineService, err := xthrift.GetTClient(headlineservice.NewHeadlineThriftServiceClient, "platform_operation_template")
	if err != nil {
		return false, err
	}
	params := &headlineservice.HeadlineSendRequest{
		Biz:         dto.Biz,
		GlobalId:    dto.GlobalId,
		ImageList:   dto.ImageList,
		Template:    &dto.Template,
		BgColor:     dto.BgColor,
		Link:        dto.Link,
		Contents:    dto.Contents,
		CurrentTime: dto.CurrentTime,
		ShowTime:    &dto.ShowTime,
		ExpireTime:  &dto.ExpireTime,
		Ext:         &dto.Ext,
		Effect:      &dto.Effect,
		RoomId:      &dto.RoomId,
	}
	val, err := headlineService.Send(ctx, params)
	if err != nil {
		logger.Errorf("SendHeadline failed. req: %v, value:%v,err:%v", dto, val, err)
		return false, err
	}
	if val.Ret != constant.STATUS_SUCCESS {
		logger.Errorf("SendHeadline biz error.  req: %v, value:%v,err:%v", dto, val, err)
		return false, err
	}
	return true, nil
}
