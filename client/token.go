package client

import (
	"context"
	"git.kugou.net/fxgo/core/client/xthrift"
	"kugou_adapter_service/third-party/gen-go/tokenservice"
)

type CheckTokenPara struct {
	Kugouid int64
	Token   string
	IP      string
	Appid   int32
}

func CheckToken(para *CheckTokenPara) (bool, error) {
	client, err := xthrift.GetTClient(tokenservice.NewTokenServiceClient, "fxsoa_checktoken")
	if err != nil {
		return false, err
	}
	p := &tokenservice.CheckPara{
		Kugouid: para.Kugouid,
		Appid:   para.Appid,
		IP:      para.IP,
		Token:   para.Token,
	}
	val, err := client.CheckTokenValue(context.Background(), p)
	if err != nil {
		return false, err
	} else {
		return val.GetResult_(), nil
	}
}
