package client

import (
	"context"
	"fmt"
	"git.kugou.net/fxgo/core/client/xthrift"
	"git.kugou.net/fxgo/core/logger"
	"kugou_adapter_service/third-party/gen-go/userv2service"
	"kugou_adapter_service/third-party/gen-go/userv2service/userv2vo"
)

// 酷狗用户信息

type UserInfoClient struct {
}

func GetUserInfoClient() *UserInfoClient {
	return &UserInfoClient{}
}

type Gender int32

type UserInfo struct {
	// 昵称
	Nickname string `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 加密 uid
	EncryptUid string `protobuf:"bytes,3,opt,name=encrypt_uid,json=encryptUid,proto3" json:"encrypt_uid,omitempty"`
	//明星等级
	StarLevel int32 `json:"starLevel"`
	//财富等级
	RichLevel int32 `json:"richLevel"`
	//房间id
	RoomId int32 `json:"roomId"`
	// 头像框
	AvatarFrame string `protobuf:"bytes,5,opt,name=avatar_frame,json=avatarFrame,proto3" json:"avatar_frame,omitempty"`
	// 年龄
	Age uint32 `protobuf:"varint,6,opt,name=age,proto3" json:"age,omitempty"`
	// vip 等级
	VipLevel uint32 `protobuf:"varint,7,opt,name=vip_level,json=vipLevel,proto3" json:"vip_level,omitempty"`
	// 性别
	Gender Gender `protobuf:"varint,8,opt,name=gender,proto3,enum=adapter_user.Gender" json:"gender,omitempty"`
	// 城市
	City string `protobuf:"bytes,9,opt,name=city,proto3" json:"city,omitempty"`
	// 原始 uid
	RawUid uint64 `protobuf:"varint,10,opt,name=raw_uid,json=rawUid,proto3" json:"raw_uid,omitempty"`
	// 游戏Profile信息
	// appid = 20000045时, 返回map["forbidPerson"]="true"时表示开启私密,否则为关闭
	Extra map[string]string `protobuf:"bytes,11,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// isRobot, 0否, 1是
	IsRobot uint32 `protobuf:"varint,12,opt,name=isRobot,proto3" json:"isRobot,omitempty"`
	//用户ID
	UserId int64 `json:"userId"`
	//酷狗ID
	KugouId int64 `json:"kugouId"`
}

func (client *UserInfoClient) FxGetUserInfo(kgId int64) (*UserInfo, error) {

	kgUser := doGetKgUser(kgId)
	if kgUser == nil {
		return nil, nil
	}

	userInfo := &UserInfo{
		Nickname:    kgUser.GetNickName(),
		Avatar:      kgUser.GetUserLogo(),
		EncryptUid:  fmt.Sprintf("%d", kgUser.GetKugouId()),
		RichLevel:   kgUser.GetRichLevel(),
		StarLevel:   kgUser.GetStarLevel(),
		RoomId:      kgUser.GetRoomId(),
		AvatarFrame: "",
		Age:         0,
		VipLevel:    0,
		Gender:      Gender(kgUser.GetSex()),
		City:        "",
		RawUid:      uint64(kgUser.GetKugouId()),
		Extra:       make(map[string]string),
		IsRobot:     0,
		UserId:      kgUser.GetUserId(),
		KugouId:     kgUser.GetKugouId(),
	}

	return userInfo, nil
}

func doGetKgUser(kgId int64) *userv2vo.UserVO {
	if kgId < 1 {
		logger.Errorf("GetUserInfo error.kgId 不合法. kgId=%d", kgId)
		return nil
	}

	client, err := xthrift.GetTClient(userv2service.NewUserModuleV2BizServiceClient, "fxsoa_user_baseinfo")
	if err != nil {
		logger.Errorf("GetUserInfo error.创建client失败. kgId=%d, error=%v", kgId, err)
		return nil
	}

	response, err := client.GetUserByKugouId(context.Background(), kgId)
	if err != nil {
		logger.Errorf("GetUserInfo error.调用失败. kgId=%d, error=%v", kgId, err)
		return nil
	}

	logger.Warnf("GetUserInfo success. kgId=%d, response=%v", kgId, response)

	if response != nil && response.Ret == 0 {
		return response.Data
	}

	return nil
}
