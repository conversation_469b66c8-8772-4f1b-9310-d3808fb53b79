package client

import (
	"context"
	"git.kugou.net/fxgo/core/client/xthrift"
	"git.kugou.net/fxgo/core/logger"
	"kugou_adapter_service/third-party/gen-go/strategyservice"
)

func Conclude(vo *strategyservice.StrategyVO) (*strategyservice.Result_, error) {
	client, err := xthrift.GetTClient(strategyservice.NewPlatformStrategyServiceClient, "platform_globalid_v2")
	if err != nil {
		logger.Error("Conclude GetTClient failed. err:%v", err)
		return nil, err
	}
	resp, err := client.Conclude(context.Background(), vo)
	if err != nil {
		logger.Error("Conclude execute failed. err:%v", err)
		return nil, err
	}
	if resp == nil || resp.Recode != 0 {
		logger.Error("Conclude failed. resp:%v", resp)
		return nil, err
	}
	result := resp.Data
	return result, nil
}
